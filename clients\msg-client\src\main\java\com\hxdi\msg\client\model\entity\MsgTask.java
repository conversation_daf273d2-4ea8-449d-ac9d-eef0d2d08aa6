package com.hxdi.msg.client.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * @program: hxdicloud_2.0
 * @description: 定时发送消息记录表
 * @author: flying
 * @create: 2025-06-30 15:17
 */
@Getter
@Setter
@TableName("MSG_TASK")
public class MsgTask {
    @TableId(type = IdType.AUTO)
    private Integer id;
    @TableField("MSG_ID")
    private String msgId;
    @TableField("RECEIVER")
    private String receiver;
    @TableField("SEND_TIME")
    private Date sendTime;

}
