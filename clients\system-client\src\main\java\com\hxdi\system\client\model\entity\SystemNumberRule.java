package com.hxdi.system.client.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hxdi.common.core.mybatis.base.entity.AbstractEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
@TableName("system_number_rule")
public class SystemNumberRule extends AbstractEntity {

    private static final long serialVersionUID=1L;

    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 规则编号
     */
    private String code;

    /**
     * 规则名称
     */
    private String name;


    /**
     * 前缀
     */
    private String prefix;

    /**
     * 后缀位数
     */
    private Integer suffixNum;

    /**
     * 规则定义
     */
    private String rule;

    /**
     * 当前数值
     */
    @TableField(exist = false)
    private Integer currNumber;

    /**
     * 版本号
     */
    @TableField(exist = false)
    private Integer vrs;

    /**
     * 0-无效，1-有效
     */
    private Integer status;

    /**
     * 是否每日清零：0-否，1-是
     */
    private Integer zeroPerDay;

    /**
     * 是否系统预设：0-否， 1-是
     */
    private Integer isPersist;


}
