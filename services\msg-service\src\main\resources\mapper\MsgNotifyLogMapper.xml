<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdicloud.msg.mapper.MsgNotifyLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hxdi.msg.client.model.MsgNotifyLog">
        <id column="id" property="id" />
        <result column="tpl_id" property="tplId" />
        <result column="content" property="content" />
        <result column="create_time" property="createTime" />
        <result column="receive_id" property="receiveId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, tpl_id, content, create_time, receive_id
    </sql>

</mapper>
