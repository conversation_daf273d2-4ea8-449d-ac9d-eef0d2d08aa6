package com.hxdi.gateway.filter;

import com.hxdi.common.core.interceptor.FeignRequestInterceptor;
import com.hxdi.common.core.security.SecurityHelper;
import com.hxdi.gateway.util.ReactiveWebUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.web.server.ServerWebExchange;
import org.springframework.web.server.WebFilter;
import org.springframework.web.server.WebFilterChain;
import reactor.core.publisher.Mono;

import java.net.URI;
import java.util.Date;
import java.util.UUID;

/**
 * 请求前缀过滤器,增加请求时间
 *
 * <AUTHOR>
 */
@Slf4j
public class PreRequestFilter implements WebFilter {

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, WebFilterChain chain) {
        // 添加自定义请求头
        String rid = UUID.randomUUID().toString();
        ServerHttpRequest request = exchange.getRequest();
        URI uri = request.getURI();
        log.info("===>当前uri:{}, host:{}", uri.getPath(), ReactiveWebUtils.getRemoteAddress(exchange));

        request = request.mutate().headers(headers -> headers.set(FeignRequestInterceptor.X_REQUEST_ID, rid)).build();

        ServerHttpResponse response = exchange.getResponse();
        response.getHeaders().set(FeignRequestInterceptor.X_REQUEST_ID,rid);

        //将现在的request 变成 change对象
        ServerWebExchange mutateExchange = exchange.mutate().request(request).response(response).build();
        // 添加请求时间
        mutateExchange.getAttributes().put("requestTime", new Date());
        return chain.filter(mutateExchange).doFinally(s -> {
            SecurityHelper.cleanUserHolder();
        });
    }
}

