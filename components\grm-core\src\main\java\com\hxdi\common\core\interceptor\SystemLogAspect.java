package com.hxdi.common.core.interceptor;


import cn.hutool.core.lang.UUID;
import com.google.common.collect.Sets;
import com.hxdi.common.core.annotation.Log;
import com.hxdi.common.core.security.BaseUserDetails;
import com.hxdi.common.core.security.SecurityHelper;
import com.hxdi.common.core.utils.JsonConverter;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.util.Set;

/**
 * 操作日志切面
 */
@Aspect
@Component
@Slf4j
public class SystemLogAspect {

    // 忽略记录的URL前缀
    private final Set<String> ignores = Sets.newHashSet("/favicon.ico",
            "/webjars", "/doc.html", "/swagger-ui", "/v3/api-docs", "/api/file");

    /**
     * 设置操作日志切入点 记录操作日志 在注解的位置切入代码
     */
    @Pointcut("@annotation(com.hxdi.common.core.annotation.Log)")
    public void operationLogPointCut() {
    }

    /**
     * 环绕通知，记录请求开始和结束，计算执行时间
     *
     * @param joinPoint 切入点
     * @return 方法执行结果
     * @throws Throwable 可能抛出的异常
     */
    @Around("operationLogPointCut()")
    public Object doAround(ProceedingJoinPoint joinPoint) throws Throwable {
        long startTime = System.currentTimeMillis();

        // 获取请求信息
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            HttpServletRequest request = attributes.getRequest();
            if (!isIgnoreUrl(request.getServletPath())) {
                // 设置请求ID到MDC
                String reqId = UUID.randomUUID().toString();
                MDC.put("reqId", reqId);
                // 记录请求开始日志
                logRequestStart(request, joinPoint);
            }
        }

        // 执行目标方法
        Object result = null;
        Exception exception = null;
        try {
            result = joinPoint.proceed();
            return result;
        } catch (Exception e) {
            exception = e;
            throw e;
        } finally {
            // 记录操作日志
            long executionTime = System.currentTimeMillis() - startTime;
            handleLog(joinPoint, result, exception, executionTime);
            // 清理MDC
            MDC.remove("reqId");
        }
    }

    /**
     * 判定当前请求url是否不需要日志记录
     *
     * @param url 路径
     * @return 是否忽略
     */
    private boolean isIgnoreUrl(String url) {
        for (String ignore : ignores) {
            if (url.startsWith(ignore)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 记录请求开始的日志
     *
     * @param request   请求对象
     * @param joinPoint 切入点
     */
    private void logRequestStart(HttpServletRequest request, JoinPoint joinPoint) {
        try {
            // 设置用户信息
            BaseUserDetails user = SecurityHelper.getUser();
            String userIdStr = null;
            String username = null;
            if (user != null) {
                username = user.getNickName();
                userIdStr = user.getUserId();
            }

            // 获取请求参数
            String requestParams = getRequestParams(joinPoint);

            // 获取操作注解信息
            Log logAnnotation = getOperationLogAnnotation(joinPoint);

            //todo 此处改为从类上的swagger注解中获取
            String module = logAnnotation != null ? logAnnotation.module() : "";

            String description = logAnnotation != null ? logAnnotation.value() : "";

            // 记录请求开始的日志
            log.info("操作日志 - 开始 | 模块: {} | 描述: {} | URL: \"{}\" ({}) | IP: {} | 用户Id: {} | 用户名: {} | 参数: {}",
                    module, description, request.getServletPath(), request.getMethod(),
                    getClientIp(request), userIdStr, username, requestParams);

        } catch (Exception e) {
            log.error("记录请求开始日志异常", e);
        }
    }

    /**
     * 处理日志记录
     *
     * @param joinPoint     切入点
     * @param result        返回结果
     * @param e             异常
     * @param executionTime 执行时间(毫秒)
     */
    private void handleLog(JoinPoint joinPoint, Object result, Exception e, long executionTime) {
        try {
            // 获取注解信息
            Log logAnnotation = getOperationLogAnnotation(joinPoint);
            if (logAnnotation == null) {
                return;
            }

            // 获取请求信息
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes == null) {
                return;
            }

            HttpServletRequest request = attributes.getRequest();
            if (isIgnoreUrl(request.getServletPath())) {
                return;
            }


            // 设置用户信息
            BaseUserDetails user = SecurityHelper.getUser();
            String userIdStr = null;
            if (user != null) {
                userIdStr = user.getUserId();
            }
            if (userIdStr != null) {
                operationLog.setUserId(userIdStr);
                operationLog.setUsername(user.getUsername());
            } else {
                operationLog.setUsername("未登录");
            }

            //todo 改为构造消息，并通过消息队列进行发送

            // 设置请求信息
            operationLog.setRequestIp(getClientIp(request));
            operationLog.setRequestUrl(request.getRequestURI());
            operationLog.setRequestMethod(request.getMethod());
            operationLog.setMethod(joinPoint.getSignature().getDeclaringTypeName() + "." + joinPoint.getSignature().getName());

            // 设置注解信息
            operationLog.setModule(logAnnotation.module());
            operationLog.setOperationType(logAnnotation.operationType());
            operationLog.setDescription(logAnnotation.description());

            // 设置请求参数
            if (logAnnotation.saveRequestData()) {
                operationLog.setRequestParam(getRequestParams(joinPoint));
            }

            // 设置响应数据
            if (logAnnotation.saveResponseData() && result != null) {
                operationLog.setResponseResult(JsonConverter.toJsonWithPretty(result));
            }

            // 设置操作状态和时间
            operationLog.setStatus(e == null ? 0 : 1);
            operationLog.setOperationTime(LocalDateTime.now());
            operationLog.setExecutionTime(executionTime);

            // 设置异常信息
            if (e != null) {
                operationLog.setErrorMsg(e.getMessage());
            }

            // 保存操作日志
            //operationLogService.saveOperationLog(operationLog);

        } catch (Exception ex) {
            log.error("记录操作日志异常", ex);
        }
    }

    /**
     * 获取操作日志注解
     *
     * @param joinPoint 切入点
     * @return 操作日志注解
     */
    private Log getOperationLogAnnotation(JoinPoint joinPoint) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        return method.getAnnotation(Log.class);
    }

    /**
     * 获取请求参数
     *
     * @param joinPoint 切入点
     * @return 请求参数字符串
     */
    private String getRequestParams(JoinPoint joinPoint) {
        Object[] args = joinPoint.getArgs();
        return args.length > 0 ? JsonConverter.toJsonWithPretty(args) : "";
    }

    /**
     * 获取客户端IP
     *
     * @param request 请求对象
     * @return IP地址
     */
    private String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        // 多个代理的情况，第一个IP为客户端真实IP
        if (ip != null && ip.contains(",")) {
            ip = ip.split(",")[0].trim();
        }
        return ip;
    }
} 