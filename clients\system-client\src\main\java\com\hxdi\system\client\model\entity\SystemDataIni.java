package com.hxdi.system.client.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/10/19 2:53 下午
 * @description 系统初始化配置
 * @version 1.0
 */
@Getter
@Setter
@TableName("system_data_ini")
public class SystemDataIni implements Serializable {

    private static final long serialVersionUID = 7614002559300383766L;

    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    private String iniType;

    private String templateId;

    private String templateName;

    private String interfaceUrl;

    private String serviceName;
    /**
     * 状态：0-关，1-开
     */
    private Integer enable;

    private Integer optType;

    /**
     * 初始化时间
     */
    private Date iniDate;

}
