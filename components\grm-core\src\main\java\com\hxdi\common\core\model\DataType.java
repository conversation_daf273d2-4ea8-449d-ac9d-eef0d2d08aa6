package com.hxdi.common.core.model;

public enum DataType implements IEnum<String>{

    STRING,
    LONG,
    INTEGER,
    NUMBER,
    ;

    @Override
    public String value() {
        return this.name();
    }

    public Class<?> getType() {
        switch (this) {
            case STRING:
                return String.class;
            case LONG:
                return Long.class;
            case INTEGER:
                return Integer.class;
            case NUMBER:
                return Number.class;
            default:
                return null;
        }
    }

    public static DataType parse(String type) {
        return IEnum.innerParse(type, values());
    }
}
