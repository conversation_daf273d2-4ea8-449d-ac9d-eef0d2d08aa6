package com.hxdi.common.core.security;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.util.Assert;

/**
 * 自定义已授权权限标识
 *
 * <AUTHOR>
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public final class BaseCustomerAuthority implements GrantedAuthority {

    /**
     * 权限标识
     */
    private String authority;


    public BaseCustomerAuthority() {
    }

    public BaseCustomerAuthority(String authority) {
        Assert.hasText(authority, "A granted authority textual representation is required");
        this.authority = authority;
    }


    public void setAuthority(String authority) {
        this.authority = authority;
    }

    @Override
    public String getAuthority() {
        return this.authority;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        } else {
            return obj instanceof BaseCustomerAuthority ? this.authority.equals(((BaseCustomerAuthority) obj).authority) : false;
        }
    }

    @Override
    public int hashCode() {
        return this.authority.hashCode();
    }

    @Override
    public String toString() {
        return this.authority;
    }

}
