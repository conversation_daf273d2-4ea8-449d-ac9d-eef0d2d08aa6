package com.hxdi.system.client.model.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hxdi.common.core.mybatis.base.entity.AbstractEntity;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
 * 系统角色-角色与用户关联
 *
 * @author: liuyadu
 * @date: 2018/10/24 16:21
 * @description:
 */
@Getter
@Setter
@TableName("system_role_user")
public class SystemRoleUser implements Serializable {
    private static final long serialVersionUID = -667816444278087761L;
    /**
     * 系统用户ID
     */
    private String userId;

    /**
     * 角色ID
     */
    private String roleId;

    private String tenantId;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    public Date createTime;

}
