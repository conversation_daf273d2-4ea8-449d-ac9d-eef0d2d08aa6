package com.hxdi.common.core.converter;

import org.springframework.core.convert.converter.Converter;
import org.springframework.lang.NonNull;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * @date 2023/7/21 11:43
 * @description
 * @version 1.0
 */
public enum StringToLocalDateTimeConverter implements Converter<String, LocalDateTime> {

    INSTANCE;

    private static final DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @NonNull
    @Override
    public LocalDateTime convert(String source) {
        return LocalDateTime.parse(source, dtf);
    }
}
