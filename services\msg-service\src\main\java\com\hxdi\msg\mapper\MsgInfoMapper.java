package com.hxdi.msg.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.model.PageParams;
import com.hxdi.msg.client.model.entity.MsgInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

@Mapper
public interface MsgInfoMapper extends BaseMapper<MsgInfo> {

    Page<MsgInfo> selectUnreadMessagesPageForUser(@Param("pageParams") PageParams pageParams,@Param("alermType") String alermType, @Param("userId") String userId,@Param("allUsersId") String allUsersId);

    List<MsgInfo> selectUnreadMessagesListForUser(@Param("alermType") String alermType, @Param("userId") String userId, @Param("allUsersId") String allUsersId);


    Page<MsgInfo> selectUserMessages(@Param("pageParams") PageParams pageParams,@Param("title") String title,@Param("publisher") String publisher,@Param("isRead") String isRead,@Param("alermType") String alermType,@Param("userId") String userId,@Param("allUsersId") String allUsersId);

    void updateStateAndSendTime(@Param("id") String id, @Param("status") int status,@Param("sendTime") Date sendTime);
}