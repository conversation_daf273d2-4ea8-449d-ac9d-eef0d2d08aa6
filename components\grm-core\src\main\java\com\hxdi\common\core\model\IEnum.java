package com.hxdi.common.core.model;

import com.hxdi.common.core.utils.CommonUtils;

import java.io.Serializable;

public interface IEnum<T extends Serializable> {

    T value();

    static <E extends IEnum, T> E innerParse(T value, E[] values){
        for(E e : values){
            if(CommonUtils.equals(e.value(), value)){
                return e;
            }
        }

        return null;
    }

}
