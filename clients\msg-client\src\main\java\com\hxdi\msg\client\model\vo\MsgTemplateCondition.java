package com.hxdi.msg.client.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @program: hxdicloud_2.0
 * @description: 消息模板查询条件
 * @author: 王贝强
 * @create: 2025-07-08 14:33
 */
@Getter
@Setter
@ApiModel("消息模板查询条件")
public class MsgTemplateCondition {
    /**
     * 消息类型：SMS，EMAIL，SITE
     */
    @ApiModelProperty("消息类型：SMS，EMAIL，SITE")
    private String msgType;

    /**
     * 模板标识
     */
    @ApiModelProperty("模板标识")
    private String msgCode;

    /**
     * 模板名称
     */
    @ApiModelProperty("模板名称")
    private String tplName;

    /**
     * 提醒方式:1-待办，2-预警，3-提醒
     */
    @ApiModelProperty("提醒方式:1-待办，2-预警，3-提醒")
    private Integer alermType;

    private Long page;

    private Long limit;
}
