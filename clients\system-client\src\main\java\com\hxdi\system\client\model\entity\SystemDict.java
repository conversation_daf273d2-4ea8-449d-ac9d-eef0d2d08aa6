package com.hxdi.system.client.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hxdi.common.core.mybatis.base.entity.AbstractEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@Getter
@Setter
@TableName("system_dict")
public class SystemDict extends AbstractEntity {

    private static final long serialVersionUID=1L;

    @TableId(type = IdType.ASSIGN_ID)
    private String dictId;

    private String itemKey;

    private String parentId;

    /**
     * 字典类型：01-字典项，02-字典项值
     */
    @NotEmpty(message = "字典类型不能为空")
    private String dictType;

    private String itemValue;

    @NotEmpty(message = "字典名称不能为空")
    private String text;

    private Integer seq;

    private Integer status;

    private Integer isPersist;

    @TableField(exist = false)
    private List<SystemDict> list;

}
