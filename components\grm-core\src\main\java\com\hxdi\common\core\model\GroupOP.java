package com.hxdi.common.core.model;

/**
 * 条件关系
 */
public enum GroupOP implements IEnum<String>{

    AND("and", " and "),
    OR("or", " or ");

    private String code;
    private String exp;

    GroupOP(String code, String exp){
        this.code = code;
        this.exp = exp;
    }

    @Override
    public String value() {
        return code;
    }

    public String exp(){
        return exp;
    }

    public static GroupOP parse(String code) {
        return IEnum.innerParse(code, values());
    }
}
