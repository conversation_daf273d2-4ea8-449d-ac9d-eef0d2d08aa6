package com.hxdi.file.controller;

import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.mybatis.base.controller.BaseController;
import com.hxdi.file.client.model.entity.FileResource;
import com.hxdi.file.client.service.FileResourceServiceClient;
import com.hxdi.file.service.FileResourceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/22 16:35
 * @description 文件资源管理
 * @version 1.0
 */
@Api(tags = "文件资源管理")
@RestController
@RequestMapping("/file")
public class FileResourceController extends BaseController<FileResourceService, FileResource> implements FileResourceServiceClient {

    @ApiOperation("预览资源")
    @GetMapping("/preview/{resId}")
    public void preview(@PathVariable String resId, HttpServletRequest request, HttpServletResponse response) {
        bizService.preview(resId, request, response);
    }

    @ApiOperation("查询资源信息")
    @GetMapping("/query/resources")
    public ResultBody<List<FileResource>> queryResources(@RequestParam String resIds) {
        return ResultBody.ok().data(bizService.queryResInfo(StringUtils.commaDelimitedListToStringArray(resIds)));
    }


    @ApiOperation("上传文件")
    @PostMapping("/upload")
    public ResultBody<FileResource> uploadFile(@RequestParam("file") MultipartFile file, @RequestParam(value = "category", required = false) String category) {
        return ResultBody.ok().data(bizService.saveAndStore(file, category));
    }

    @ApiOperation("通过URL读取并上传文件")
    @PostMapping("/upload/internal/byUrl")
    @Override
    public ResultBody<FileResource> uploadFileByUrl(@RequestParam("fileUrl") String fileUrl) {
        return ResultBody.ok().data(bizService.readAndStore(fileUrl));
    }

    @Override
    @ApiOperation("删除资源")
    @DeleteMapping("/remove")
    public ResultBody remove(@RequestParam("resIds") String resIds) {
        bizService.clean(StringUtils.commaDelimitedListToStringArray(resIds));
        return ResultBody.ok();
    }

    @ApiOperation("下载文件")
    @GetMapping("/download")
    public void download(@RequestParam("resId") String resId, HttpServletRequest request, HttpServletResponse response) {
        bizService.download(resId, request, response);
    }

    @ApiOperation("读取流")
    @GetMapping("/stream")
    public void getStream(@RequestParam("resId") String resId, HttpServletRequest request, HttpServletResponse response) {
        bizService.getStream(resId, request, response);
    }

    @ApiOperation("压缩&下载")
    @GetMapping("/downloadZip")
    public void downloadZip(@RequestParam("resIds") String resIds, HttpServletRequest request, HttpServletResponse response) {
        bizService.downloadZip(StringUtils.commaDelimitedListToStringArray(resIds), request, response);
    }
}
