package com.hxdi.file.client.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.hxdi.common.core.mybatis.base.entity.Entity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/2/22 15:43
 * @description 视频资源对象
 * @version 1.0
 */
@Getter
@Setter
@ToString
@TableName("fss_video_resource")
public class VideoResource extends Entity {

    private static final long serialVersionUID = -4622998351307431036L;
    /**
     * 资源ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 原始文件名称
     */
    private String originalFilename;

    /**
     * 扩展名
     */
    private String extname;

    /**
     * 资源访问路径
     */
    private String url;

    /**
     * 资源存储路径
     */
    private String path;

    /**
     * 字节长度
     */
    private Long byteLen;

    /**
     * 内容类型
     */
    private String contentType;

    /**
     * MD5散列值
     */
    private String etag;

    /**
     * 视频来源
     */
    private String origin;

    /**
     * 搜索关键字，车牌号，设备编号
     */
    private String searchKey;

    /**
     * 视频记录时间
     */
    private Date recTime;

    private String alarmId;

    @TableField(value = "create_id", fill = FieldFill.INSERT)
    private String createId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 修改时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    private String tenantId;


}
