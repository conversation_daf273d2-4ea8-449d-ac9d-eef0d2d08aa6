html {
    height: 100%;
    overflow: hidden;
}

body {
    background: #e7e7e7;
    height: 100%;
}

/*移除表单填充色*/
input:-webkit-autofill {
    -webkit-box-shadow: 0 0 0 1000px #ececec inset;
}
.login-img {
    margin:0 auto;
    width:100%;
}
.imgloginclass {
    float:left;
    left:0;
    margin-top:20px;
}
.imgleftclass{
    width:100%;
    height:380px;
}
/*省端*/
.m-login-bg{
    width: 100%;
    height: 100%;
    background-image: url('../images/background_login.jpg');
    background-size: cover;
    background-position: center;
    position: relative;
}
/*库端*/
/*.m-login-bg{
    width: 100%;
    height: 100%;
    background-image: url('../images/background_KPT.jpg');
    background-size: cover;
    background-position: center;
    position: relative;
}*/

.m-login {
    width: 450px;
    position: absolute;
    left: 50%;
    top: 50%;
    margin-left: -195px;
    margin-top: -205px;
}
.m-login-mwc {
    width:50%;
    float:left;
}
.m-login h3 {
    color: #000;
    text-align: center;
    height: 60px;
    font-size: 28px;
}

.m-login .copyright {
    text-align: center;
    color: #999;
    padding-top: 10px;
}

.m-login-warp {
    background: rgba(255,255,255,0.95);
    -webkit-box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
    -webkit-border-radius: 6px;
    border-radius: 6px;
    padding: 25px;
    float:left;
    width:100%;
    height:380px;
}

.m-login-warp .card-header{
    position: relative;
    height: 42px;
    line-height: 42px;
    padding: 0 15px;
    border-bottom: 1px solid #f6f6f6;
    color: #333;
    border-radius: 2px 2px 0 0;
    font-size: 14px;
}

.m-login-warp .form-control {
    height: 40px;
    line-height: 40px \9;
    /*margin-bottom: 5px;*/
    -webkit-transition-property: none;
    transition-property: none;
    background: #ececec;
    border: 1px solid #ececec;
}

.m-login-warp .m-login-btn .layui-inline {
    width: 49%;
    margin: 0;
}

.m-login-warp .m-login-btn .layui-inline button {
    width: 100%;
}

@media screen and (max-width: 450px) {
    .m-login {
        width: 300px;
        position: absolute;
        left: 50%;
        top: 50%;
        margin-left: -150px;
        margin-top: -240px;
    }

    .m-login-warp .m-login-btn .layui-inline {
        width: 100%;
        margin: 0 0 10px;
    }
}
