package com.hxdi.file.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.common.core.security.SecurityHelper;
import com.hxdi.file.client.model.entity.Scott;
import com.hxdi.file.mapper.ScottMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/22 17:51
 * @description 多租户插件测试类
 * @version 1.0
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Slf4j
public class ScottServiceImpl extends BaseServiceImpl<ScottMapper, Scott> {

    public Page<Scott> page(int limit, int page) {
        Page<Scott> cond = new Page<>(page, limit);
        baseMapper.selectPage(cond, Wrappers.emptyWrapper());
        return cond;
    }

    public List<Scott> lists() {
        return baseMapper.selectList(Wrappers.<Scott>lambdaQuery().eq(Scott::getTenantId, SecurityHelper.getTenantId()));
    }

    public List<Scott> lists2() {
        return baseMapper.selectListByTenant(SecurityHelper.obtainTenantId().get());
    }

    public Scott get(String id) {
        return baseMapper.selectById(id);
    }

    public void create(Scott scott) {
        baseMapper.insert(scott);
    }

    public void update(String id) {
        Scott updating = baseMapper.selectById(id);
        baseMapper.updateById(updating);
    }

    public void create2(Scott scott) {
        scott.setTenantId(SecurityHelper.getTenantId());
        baseMapper.insert(scott);
    }

    public void delete(String id) {
        baseMapper.deleteById(id);
    }

    public void delete2(String id) {
        String userId = SecurityHelper.getUserId();
        baseMapper.delete(Wrappers.<Scott>lambdaQuery().eq(Scott::getId, id).eq(Scott::getTenantId, SecurityHelper.obtainTenantId().get()));
    }
}
