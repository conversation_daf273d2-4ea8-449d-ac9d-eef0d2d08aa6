package com.hxdi.msg.service.impl;


import com.aliyun.dysmsapi20170525.Client;
import com.aliyun.dysmsapi20170525.models.SendSmsRequest;
import com.aliyun.dysmsapi20170525.models.SendSmsResponse;
import com.aliyun.teaopenapi.models.Config;
import com.hxdi.msg.client.model.SmsMessage;
import com.hxdi.msg.service.SmsSender;
import lombok.extern.slf4j.Slf4j;


/**
 * <AUTHOR>
 */
@Slf4j
public class AliyunSmsSenderImpl implements SmsSender {

    private String accessKeyId;

    private String accessKeySecret;

    private final static String OK = "OK";

    public AliyunSmsSenderImpl() {
        log.info("init aliyunSMS sender:" + this);
    }

    /**
     * 使用AK&SK初始化账号Client
     * @param accessKeyId
     * @param accessKeySecret
     * @return Client
     * @throws Exception
     */
    public static Client createClient(String accessKeyId, String accessKeySecret) throws Exception {
        Config config = new Config()
                .setAccessKeyId(accessKeyId)
                .setAccessKeySecret(accessKeySecret);
        // 访问的域名
        config.endpoint = "dysmsapi.aliyuncs.com";
        return new Client(config);
    }

    @Override
    public Boolean send(SmsMessage parameter) {
        boolean result = false;
        try {
            Client client = createClient(accessKeyId, accessKeySecret);
            SendSmsRequest sendSmsRequest = new SendSmsRequest()
                    .setPhoneNumbers(parameter.getPhoneNum())
                    .setSignName(parameter.getSignName())
                    .setTemplateCode(parameter.getTplCode())
                    .setTemplateParam(parameter.getTplParams());
            SendSmsResponse response = client.sendSms(sendSmsRequest);
            String code = response.body.code;
            if (OK.equals(code)){
                result = true;
            } else {
                log.error("发送短信失败：{}", response.body.message);
            }
        } catch (Exception e) {
            log.error("发送短信接口调用错误", e);
        }

        return result;
    }

    public String getAccessKeyId() {
        return accessKeyId;
    }

    public void setAccessKeyId(String accessKeyId) {
        this.accessKeyId = accessKeyId;
    }

    public String getAccessKeySecret() {
        return accessKeySecret;
    }

    public void setAccessKeySecret(String accessKeySecret) {
        this.accessKeySecret = accessKeySecret;
    }

}
