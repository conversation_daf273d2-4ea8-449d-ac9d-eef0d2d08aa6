package com.hxdi.common.core.interceptor;

import com.hxdi.common.core.annotation.DataPermission;
import com.hxdi.common.core.constants.CommonConstants;
import com.hxdi.common.core.model.*;
import com.hxdi.common.core.security.BaseUserDetails;
import com.hxdi.common.core.security.SecurityHelper;
import com.hxdi.common.core.utils.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;

import java.util.ArrayList;
import java.util.List;

/**
 * <数据权限控制切面类>
 * <pre>
 *  数据权限控制方式分为4种：
 *    1. 方法级：通过注解指定权限字段和值进行过滤；
 *    2. 用户级：可以针对用户进行权限规则配置；
 *    3. 自定义：数据角色自定义，则是针对角色进行权限规则配置；
 *    4. 系统级：系统级数据角色包括（全部、本级及以下、本级、本人）；
 * </pre>
 * <AUTHOR>
 * @version 1.0
 * @since 2025/4/11 16:51
 */
@Slf4j
@Aspect
public class DataPermissionAspect {

    /**
     * 数据权限控制规则上下文
     */
    private static final ThreadLocal<DataSqlParser> SCOPES_CONTEXT = new ThreadLocal<>();

    /**
     * 数据权限注解对象缓存上下文
     */
    private static final ThreadLocal<DataPermission> DATA_PERMISSION_CONTEXT = new ThreadLocal<>();

    private RedisUtil redisUtil;

    public DataPermissionAspect(RedisUtil redisUtil) {
        this.redisUtil = redisUtil;
    }

    @Around("@annotation(dp)")
    public Object doAround(ProceedingJoinPoint pjp, DataPermission dp) throws Throwable {
        try {
            BaseUserDetails user = SecurityHelper.getUser();
            if (user == null || SecurityHelper.isSuperUser(user)) {
                return pjp.proceed();
            }

            DataSqlParser.DataFilterGroup dataFilter = null;
            if (dp.key() != ColumnKey.NONE) {
                // 1.获取方法级自定义数据权限
                dataFilter = new DataSqlParser.DataFilterGroup();
                dataFilter.setType(PriviType.METHOD);
                switch (dp.key()) {
                    case MOBILE:
                        dataFilter.addRule(new DataSqlParser.DataFilterRule(dp.column(), user.getMobile(), RuleOP.EQ, DataType.STRING));
                        break;
                    case PID:
                        dataFilter.addRule(new DataSqlParser.DataFilterRule(dp.column(), user.getPid(), RuleOP.EQ, DataType.STRING));
                        break;
                    case ORG_ID:
                        dataFilter.addRule(new DataSqlParser.DataFilterRule(dp.column(), user.getOrganId(), RuleOP.EQ, DataType.STRING));
                        break;
                    case PID_ORGID:
                        List<String> scopes = new ArrayList<>();
                        scopes.add(user.getOrganId());
                        if (SecurityHelper.isStationUser(user)) {
                            scopes.add(user.getPid());
                        }
                        dataFilter.addRule(new DataSqlParser.DataFilterRule(dp.column(), scopes, RuleOP.IN, DataType.STRING));
                        break;
                    case USER_ID:
                        dataFilter.addRule(new DataSqlParser.DataFilterRule(dp.column(), user.getUserId(), RuleOP.EQ, DataType.STRING));
                        break;
                    default:
                        break;
                }
            }

            if (dataFilter == null) {
                // 2.获取用户数据权限, 权限优先级：用户自定义权限 > 角色数据权限
                dataFilter = (DataSqlParser.DataFilterGroup) redisUtil.hget(CommonConstants.DATA_SCOPE, user.getUserId());
            }

            if (dataFilter == null) {
                if (Integer.valueOf(3).equals(user.getUserScopeRoleType())) {
                    // 3.获取自定义角色数据权限
                    dataFilter = (DataSqlParser.DataFilterGroup) redisUtil.hget(CommonConstants.DATA_SCOPE, user.getUserScopeRole());
                } else {
                    // 4.获取系统级角色数据权限
                    DataScope systemScope = DataScope.parse(user.getUserScopeRole());
                    dataFilter = new DataSqlParser.DataFilterGroup();
                    dataFilter.setType(PriviType.ROLE);
                    switch (systemScope) {
                        case CURRENT_AND_BELOW:
                            dataFilter.addRule(new DataSqlParser.DataFilterRule(PermColumn.PERMISSION_COLUMN.value(), user.getScopes(), RuleOP.IN, DataType.STRING));
                            break;
                        case CURRENT:
                            dataFilter.addRule(new DataSqlParser.DataFilterRule(PermColumn.PERMISSION_COLUMN.value(), user.getOrganId(), RuleOP.EQ, DataType.STRING));
                            break;
                        case USER:
                            dataFilter.addRule(new DataSqlParser.DataFilterRule(PermColumn.CREATE_ID.value(), user.getUserId(), RuleOP.EQ, DataType.STRING));
                            break;
                        case ALL:
                            dataFilter = null;
                            break;
                        default:
                            break;
                    }
                }
            }

            if (dataFilter != null) {
                // 设置别名
                dataFilter.setAlias(dp.alias());
                DataPermissionAspect.setScopesContext(new DataSqlParser(dataFilter));
                DataPermissionAspect.setDataPermissionContext(dp);
            }

            // 执行目标方法
            return pjp.proceed();
        } finally {
            DataPermissionAspect.clear();
        }
    }


    public static DataSqlParser getScopesContext(){
        return SCOPES_CONTEXT.get();
    }

    private static void setScopesContext(DataSqlParser sqlParser){
        if (SCOPES_CONTEXT.get() == null){
            SCOPES_CONTEXT.set(sqlParser);
        }
    }

    private static void setDataPermissionContext(DataPermission dataPermission){
        if (DATA_PERMISSION_CONTEXT.get() == null){
            DATA_PERMISSION_CONTEXT.set(dataPermission);
        }
    }

    public static DataPermission getDataPermissionContext(){
        return DATA_PERMISSION_CONTEXT.get();
    }

    private static void clear(){
        if (SCOPES_CONTEXT.get() != null){
            SCOPES_CONTEXT.remove();
        }

        if (DATA_PERMISSION_CONTEXT.get() != null) {
            DATA_PERMISSION_CONTEXT.remove();
        }
    }
}
