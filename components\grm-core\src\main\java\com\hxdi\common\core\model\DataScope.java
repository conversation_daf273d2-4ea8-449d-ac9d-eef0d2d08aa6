package com.hxdi.common.core.model;

/**
 * 系统预定义数据权限类型
 */
public enum DataScope implements IEnum<String>{

    /**
     * 全部
     */
    ALL("DATA_ALL"),
    /**
     * 本级及以下级
     */
    CURRENT_AND_BELOW("DATA_CURRENT_AND_BELOW"),

    /**
     * 本级
     */
    CURRENT("DATA_CURRENT"),

    /**
     * 本人
     */
    USER("DATA_USER"),

    /**
     * 自定义
     */
    CUSTOM("DATA_CUSTOM");

    private String code;

    DataScope(String code){
        this.code = code;
    }

    @Override
    public String value() {
        return code;
    }

    public static DataScope parse(String code) {
        return IEnum.innerParse(code, values());
    }
}
