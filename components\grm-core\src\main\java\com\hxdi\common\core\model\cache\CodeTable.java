package com.hxdi.common.core.model.cache;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;


/**
 * 系统码表
 */
@Getter
@Setter
public class CodeTable implements Serializable {

    private String identifier;
    private String code;
    private String name;
    private String tenantId;

    private List<CodeTable> items;

    public CodeTable(){

    }

    public CodeTable(String identifier, String code, String name){
        this.identifier = identifier;
        this.code = code;
        this.name = name;
    }
}
