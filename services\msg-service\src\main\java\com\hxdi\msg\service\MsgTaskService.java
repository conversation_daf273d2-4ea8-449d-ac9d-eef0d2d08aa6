package com.hxdi.msg.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hxdi.msg.client.model.entity.MsgInfo;
import com.hxdi.msg.client.model.entity.MsgTask;

import java.util.List;

/**
 * @program: hxdicloud_2.0
 * @description: 消息定时发送任务接口
 * @author: flying
 * @create: 2025-06-30 15:30
 */
public interface MsgTaskService extends IService<MsgTask> {

    /**
     * 添加定时发送任务
     * @param msg
     */
    void addTask(MsgInfo msg);
    /**
     * 删除定时发送任务
     * @param msg
     */
    void removeTask(MsgInfo msg);
    /**
     * 更新定时发送任务
     * @param msg
     */
    void updateTask(MsgInfo msg);
    /**
     * 发送消息(传入待执行的任务，或者待发送的消息)
     * @param msg
     * @param task
     */
    void sendMsg(List<MsgInfo> msg, List<MsgTask> task);
}
