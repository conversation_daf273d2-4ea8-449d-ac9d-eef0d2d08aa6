package com.hxdi.common.core.utils;

import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;

/**
 * <计算工具类>
 * <AUTHOR>
 * @version 1.0
 * @since 2024/6/28 10:59
 */
@Slf4j
public class CalculationUtil {

    private static final BigDecimal TON_TO_KG = new BigDecimal(1000);

    /**
     * 将千克转换为吨
     * @param kg
     * @return
     */
    public static BigDecimal convertKgToTon(BigDecimal kg){
        if (kg == null) {
            return BigDecimal.ZERO;
        }

        return kg.divide(TON_TO_KG, 3, BigDecimal.ROUND_HALF_UP);
    }

    /**
     * 将千克转换为吨
     * @param kgValue
     * @return
     */
    public static BigDecimal convertKgToTon(String kgValue){
        BigDecimal kg = convert2BigDecimal(kgValue);
        return convertKgToTon(kg);
    }

    /**
     * 将吨转换为千克
     * @param ton
     * @return
     */
    public static BigDecimal convertTonToKg(BigDecimal ton){
        if (ton == null) {
            return BigDecimal.ZERO;
        }

        return ton.multiply(TON_TO_KG);
    }

    public static BigDecimal convertTonToKg(String tonValue){
        return convertTonToKg(convert2BigDecimal(tonValue));
    }

    public static BigDecimal subtract(BigDecimal num1, BigDecimal num2){
        if(num1 == null){
            num1 = BigDecimal.ZERO;
        }
        if(num2 == null){
            num2 = BigDecimal.ZERO;
        }
        return num1.subtract(num2);
    }

    public static BigDecimal subtract(String num1, String num2){
        return convert2BigDecimal(num1).subtract(convert2BigDecimal(num2));
    }

    public static BigDecimal add(BigDecimal num1, BigDecimal num2){
        if(num1 == null){
            num1 = BigDecimal.ZERO;
        }
        if(num2 == null){
            num2 = BigDecimal.ZERO;
        }
        return num1.add(num2);
    }

    public static BigDecimal add(String num1, String num2){
        return convert2BigDecimal(num1).add(convert2BigDecimal(num2));
    }


    public static BigDecimal convert2BigDecimal(String value){
        BigDecimal result = BigDecimal.ZERO;
        if(CommonUtils.isNotEmpty(value)){
            try {
                result = new BigDecimal(value.trim());
            } catch (Exception e) {
                log.error("String cast to BigDecimal fail.");
            }

        }

        return result;
    }

    /**
     * 创建一个加法器
     * @return
     */
    public static Adder createAdder(){
        return new Adder();
    }

    /**
     * <AUTHOR>
     * @date 2024/6/28 14:21
     * @description 加法器
     * @version 1.0
     */
    public static class Adder {

        private BigDecimal result = BigDecimal.ZERO;

        public Adder add(BigDecimal ... array){
            for (BigDecimal num : array) {
                if (num != null) {
                    result = result.add(num);
                }
            }

            return this;
        }

        public Adder add(String ... array){
            BigDecimal[] array2 = new BigDecimal[array.length];
            for (int i = 0; i < array.length; i++) {
                array2[i] = convert2BigDecimal(array[i]);
            }
            return add(array2);
        }

        public BigDecimal getResult() {
            return result;
        }

        @Override
        public String toString() {
            return this.result.toString();
        }
    }
}
