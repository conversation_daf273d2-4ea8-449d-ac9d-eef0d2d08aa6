package com.hxdi.common.core.utils;

import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Method;

/**
 * <AUTHOR>
 * @date 2021/12/20 7:35 下午
 * @description 反射调用工具类
 * @version 1.0
 */
@Slf4j
public class ReflectionUtil {

    /**
     * 反射调用
     * @param object 调用对象
     * @param methodName 调用方法名称
     * @param paramsClass 参数类型
     * @param args 参数值
     * @return
     */
    public static Object invoke(Object object, String methodName, Class<?>[] paramsClass, Object... args){
        try{
            Class clazz = object.getClass();
            Method method = clazz.getMethod(methodName, paramsClass);
            return method.invoke(object, args);
        } catch (NoSuchMethodException e){
            log.error("反射找不到方法#{}", methodName);
        } catch (Exception e){
            log.error("反射调用异常#{}, {}", methodName, e.getMessage());
        }

        return null;
    }

    /**
     * 反射调用
     * @param object 调用对象
     * @param methodName 调用方法名称
     * @return
     */
    public static Object invoke(Object object, String methodName){
        try{
            Class clazz = object.getClass();
            Method method = clazz.getDeclaredMethod(methodName, null);
            return method.invoke(object);
        } catch (NoSuchMethodException e){
            log.error("反射找不到方法#{}", methodName);
        } catch (Exception e){
            log.error("反射调用异常#{},{}", methodName, e.getMessage());
        }

        return null;
    }

    /**
     * 反射调用
     * @param beanName Spring Bean名称
     * @param methodName 调用方法名称
     * @param paramsClass 参数类型
     * @param args 参数值
     * @return
     */
    public static Object invokeBean(String beanName, String methodName, Class<?>[] paramsClass, Object... args){
        Object bean = SpringContextHolder.getBean(beanName);
        return invoke(bean, methodName, paramsClass, args);
    }

    /**
     * 反射调用
     * @param beanName Spring Bean名称
     * @param methodName 调用方法名称
     * @return
     */
    public static Object invokeBean(String beanName, String methodName){
        Object bean = SpringContextHolder.getBean(beanName);
        return invoke(bean, methodName);
    }

}
