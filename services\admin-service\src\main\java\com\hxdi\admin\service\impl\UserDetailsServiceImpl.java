package com.hxdi.admin.service.impl;

import com.hxdi.admin.service.feign.SystemUserServiceClient;
import com.hxdi.common.core.constants.ErrorCode;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.security.BaseUserDetails;
import com.hxdi.system.client.constants.SystemConstants;
import com.hxdi.system.client.model.UserInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

@Slf4j
@Service("userDetailsService")
public class UserDetailsServiceImpl implements UserDetailsService {

    @Autowired
    private SystemUserServiceClient systemUserServiceClient;

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        ResultBody<UserInfo> resp = systemUserServiceClient.loadUser(username);
        if (resp.getCode() != ErrorCode.OK.getCode()) {
            throw new UsernameNotFoundException(ErrorCode.ACCOUNT_NOT_EXIST.getMessage());
        }

        UserInfo account = resp.getData();
        String domain = account.getAccDomain();
        String accountId = account.getAccountId();
        String userId = account.getUserId();
        String encodedPassword = account.getPassword();
        String nickName = account.getNickName();
        String mobile = account.getMobile();
        String avatar = account.getAvatar();
        String accountType = account.getAccountType();
        boolean accountNonLocked = account.getStatus().intValue() != SystemConstants.ACCOUNT_STATUS_LOCKED;
        boolean credentialsNonExpired = (account.getCredentialExpireTime() != null)
                ? ((System.currentTimeMillis() - account.getCredentialExpireTime().getTime()) > 0 ? false : true)
                : true;
        boolean enabled = account.getStatus().intValue() == SystemConstants.ACCOUNT_STATUS_NORMAL ? true : false;
        boolean accountNonExpired = true;
        BaseUserDetails userDetails = new BaseUserDetails();
        userDetails.setAccDomain(domain);
        userDetails.setAccountId(accountId);
        userDetails.setAccountType(accountType);
        userDetails.setUserId(userId);
        userDetails.setUsername(username);
        userDetails.setPassword(encodedPassword);
        userDetails.setNickName(nickName);
        userDetails.setUserType(account.getUserType());
        userDetails.setAuthorities(account.getAuthorities());
        userDetails.setMobile(mobile);
        userDetails.setAvatar(avatar);
        userDetails.setOrganId(account.getOrganId());
        userDetails.setOrganName(account.getOrganName());
        userDetails.setOrganPath(account.getOrganPath());
        userDetails.setOrganCode(account.getOrganCode());
        userDetails.setOrganType(account.getOrganType());
        userDetails.setPid(account.getPid());
        userDetails.setPname(account.getPname());
        userDetails.setDataHierarchyId(account.getOrganId());
        userDetails.setUserScopeRole(account.getUserScopeRole());
        userDetails.setUserScopeRoleType(account.getUserScopeRoleType());
        userDetails.setScopes(account.getScopes());
        userDetails.setTenantId(account.getTenantId());
        userDetails.setTenantCode(account.getTenantCode());
        userDetails.setTenantName(account.getTenantName());
        userDetails.setAccountNonLocked(accountNonLocked);
        userDetails.setAccountNonExpired(accountNonExpired);
        userDetails.setCredentialsNonExpired(credentialsNonExpired);
        userDetails.setEnabled(enabled);
        userDetails.setClientId("");
        return userDetails;
    }
}
