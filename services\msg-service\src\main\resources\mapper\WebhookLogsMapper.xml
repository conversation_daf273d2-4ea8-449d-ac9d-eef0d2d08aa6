<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdicloud.msg.mapper.WebhookLogsMapper">
  <resultMap id="BaseResultMap" type="com.hxdi.msg.client.model.entity.WebhookLogs">
    <id column="msg_id" jdbcType="VARCHAR" property="msgId" />
    <result column="retry_nums" jdbcType="DECIMAL" property="retryNums" />
    <result column="total_nums" jdbcType="DECIMAL" property="totalNums" />
    <result column="delay" jdbcType="DECIMAL" property="delay" />
    <result column="result" jdbcType="DECIMAL" property="result" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="business_key" jdbcType="VARCHAR" property="businessKey" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="url" jdbcType="LONGVARCHAR" property="url" />
    <result column="content" jdbcType="LONGVARCHAR" property="content" />
  </resultMap>
</mapper>
