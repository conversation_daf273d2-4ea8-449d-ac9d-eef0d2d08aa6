package com.hxdi.msg.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.msg.client.model.MsgSmsConditon;
import com.hxdi.msg.client.model.MsgSmsTemplate;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-03
 */
public interface MsgSmsTemplateMapper extends SuperMapper<MsgSmsTemplate> {

    Page<MsgSmsTemplate> pageList(Page<MsgSmsTemplate> page, @Param("condition") MsgSmsConditon condition);

}
