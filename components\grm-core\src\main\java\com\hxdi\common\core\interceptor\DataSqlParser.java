package com.hxdi.common.core.interceptor;

import com.hxdi.common.core.constants.StrPool;
import com.hxdi.common.core.exception.BaseException;
import com.hxdi.common.core.model.DataType;
import com.hxdi.common.core.model.GroupOP;
import com.hxdi.common.core.model.PriviType;
import com.hxdi.common.core.model.RuleOP;
import com.hxdi.common.core.security.SecurityHelper;
import com.hxdi.common.core.utils.CommonUtils;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import net.sf.jsqlparser.expression.BinaryExpression;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.LongValue;
import net.sf.jsqlparser.expression.StringValue;
import net.sf.jsqlparser.expression.operators.conditional.AndExpression;
import net.sf.jsqlparser.expression.operators.relational.EqualsTo;
import net.sf.jsqlparser.expression.operators.relational.ExpressionList;
import net.sf.jsqlparser.expression.operators.relational.InExpression;
import net.sf.jsqlparser.expression.operators.relational.ItemsList;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.schema.Column;
import net.sf.jsqlparser.schema.Table;
import net.sf.jsqlparser.statement.select.PlainSelect;
import net.sf.jsqlparser.statement.select.Select;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

@ToString
public class DataSqlParser implements Serializable {

    public DataFilterGroup dataFilter = new DataFilterGroup();

    public DataSqlParser() {}

    public DataSqlParser(DataFilterGroup dataFilter) {
        this.dataFilter = dataFilter;
    }

    public Expression getSqlSegment() {
        List<Expression> expressions = dataFilter.getRules().stream().map(DataFilterRule::getSqlSegment).filter(Objects::nonNull).collect(Collectors.toList());
        return mergeWithAnd(expressions);
    }

    @Getter
    @Setter
    public static class DataFilterGroup {
        private GroupOP op;
        private String res;
        private String subject;
        private PriviType type;
        private List<DataFilterRule> rules = new ArrayList<>();

        public void addRule(DataFilterRule rule) {
            this.rules.add(rule);
        }

        public void setAlias(String alias) {
            rules.forEach(rule -> rule.setAlias(alias));
        }
    }

    @Getter
    @Setter
    public static class DataFilterRule {
        /**
         * 别名
         */
        private String alias;
        /**
         * 授权字段
         */
        private String field;

        /**
         * 条件参数值
         */
        private Object val;

        private RuleOP op;

        private DataType dataType;

        private String sqlString;

        public DataFilterRule(){}

        public DataFilterRule(String field, Object val, RuleOP op, DataType dataType) {
            this.field = field;
            this.val = val;
            this.op = op;
            this.dataType = dataType;
        }

        public DataFilterRule(String sqlString) {
            this.sqlString = sqlString;
        }

        /**
         * 检查是否是sql模式
         * @return
         */
        public boolean isSqlMode(){
            return CommonUtils.isNotBlank(sqlString);
        }

        /**
         * 获取sql片段
         * @return
         */
        public Expression getSqlSegment() {
            if (this.val == null && !this.isSqlMode()) {
                return null;
            }

            Expression expression = null;
            try {
                if (this.isSqlMode()) {
                    Map<String, Object> params = SecurityHelper.getUserDataScopeParams();
                    String replacedSql = replacePlaceholders(this.sqlString, params);
                    expression = CCJSqlParserUtil.parseCondExpression(replacedSql);
                    if (CommonUtils.isNotBlank(alias)) {
                        expression = addTableAlias(expression, this.alias);
                    }
                } else {
                    Class<?> clazz = this.dataType.getType();
                    switch (this.op) {
                        case IN:
                            ItemsList itemsList = null;
                            if (clazz == String.class) {
                                List<String> list = parseToList(this.val, String.class);
                                itemsList = new ExpressionList(list.stream().map(StringValue::new).collect(Collectors.toList()));
                            } else if (clazz == Integer.class || clazz == Long.class || clazz == Number.class) {
                                List<?> list = parseToList(this.val, clazz);
                                itemsList = new ExpressionList(list.stream().map(v -> new LongValue(v.toString())).collect(Collectors.toList()));
                            }
                            expression = new InExpression(buildColumn(this.alias, this.field), itemsList);
                            break;
                        case EQ:
                            Object v = parseObj(this.val, clazz);
                            if (clazz == String.class) {
                                expression = new EqualsTo(buildColumn(this.alias, this.field), new StringValue(v.toString()));
                            } else if (clazz == Integer.class || clazz == Long.class || clazz == Number.class) {
                                expression = new EqualsTo(buildColumn(this.alias, this.field), new LongValue(v.toString()));
                            }
                            break;
                    }
                }
            } catch (Exception e) {
                throw new BaseException("sql解析异常", e);
            }

            return expression;
        }
    }

    /**
     * 创建字段对象
     * @param alias
     * @param column
     * @return
     */
    private static Column buildColumn(String alias, String column) {
        if (CommonUtils.isNotBlank(alias)) {
            return new Column(alias + "." + column);
        } else {
            return new Column(column);
        }
    }

    /**
     * 在条件表达式的字段名上添加表别名
     *
     * @param expression 原始条件表达式
     * @param alias      表别名
     * @return 修改后的条件表达式
     */
    private static Expression addTableAlias(Expression expression, String alias) {
        if (expression instanceof BinaryExpression) {
            // 处理二元表达式（如 AND/OR）
            BinaryExpression binaryExpression = (BinaryExpression) expression;
            binaryExpression.setLeftExpression(addTableAlias(binaryExpression.getLeftExpression(), alias));
            binaryExpression.setRightExpression(addTableAlias(binaryExpression.getRightExpression(), alias));
        } else if (expression instanceof Column) {
            // 处理列名
            Column column = (Column) expression;
            if (column.getTable() == null || column.getTable().getName() == null) {
                // 如果列没有表名或表别名，则添加表别名
                column.setTable(new Table(alias));
            }
        }
        return expression;
    }

    /**
     * 将 List<Expression> 转换为一个完整表达式，用 AND 连接
     *
     * @param expressions 表达式列表
     * @return 合并后的完整表达式
     */
    private static Expression mergeWithAnd(List<Expression> expressions) {
        if (expressions == null || expressions.isEmpty()) {
            throw new IllegalArgumentException("Expression list cannot be null or empty.");
        }

        // 如果只有一个表达式，直接返回
        if (expressions.size() == 1) {
            return expressions.get(0);
        }

        // 初始化第一个表达式
        Expression result = expressions.get(0);

        // 遍历剩余的表达式，用 AND 连接
        for (int i = 1; i < expressions.size(); i++) {
            result = new AndExpression(result, expressions.get(i));
        }

        return result;
    }


    /**
     * 替换 SQL 中的占位符为 Map 中的值
     *
     * @param sql    带有占位符的 SQL 字符串
     * @param params 参数映射
     * @return 替换后的 SQL 字符串
     */
    public static String replacePlaceholders(String sql, Map<String, Object> params) {
        if (sql == null || params == null || params.isEmpty()) {
            throw new IllegalArgumentException("SQL or parameters cannot be null or empty.");
        }

        // 遍历参数并替换占位符
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            String placeholder = "{" + entry.getKey() + "}";
            String value = formatValue(entry.getValue());
            sql = sql.replace(placeholder, value);
        }

        return sql;
    }

    /**
     * 格式化值（字符串加单引号，其他类型直接转换为字符串）
     *
     * @param value 参数值
     * @return 格式化后的字符串
     */
    private static String formatValue(Object value) {
        if (value instanceof String) {
            // 如果是字符串，添加单引号
            return "'" + value.toString() + "'";
        } else {
            // 其他类型直接转换为字符串
            return value.toString();
        }
    }



    /**
     * 将对象转换为指定类型的对象实例
     * @param value
     * @param typeCls
     * @param <E>
     * @return
     */
    private static <E> List<E> parseToList(Object value, Class<E> typeCls) {
        if (value == null) return null;

        if (value instanceof String) {
            String[] arr = ((String) value).split(StrPool.Symbol.COMMA);
            return Arrays.stream(arr).map(v -> parseObj(v, typeCls)).collect(Collectors.toList());
        } else if (value instanceof Collection) {
            return (List<E>) new ArrayList<>((Collection<?>) value);
        } else if (value.getClass().isArray()) {
            return (List<E>) Arrays.asList(value);
        }

        return null;
    }

    /**
     * 将对象转换为指定类型的对象实例
     * @param value
     * @param typeCls
     * @param <T>
     * @return
     */
    private static <T> T parseObj(Object value, Class<T> typeCls) {
        if (value == null) return null;

        if (typeCls.isInstance(value)) {
            return typeCls.cast(value);
        } else if (value instanceof String) {
            return cast((String) value, typeCls);
        }

        return null;
    }

    /**
     * 将字符串转换为指定类型的对象实例。
     *
     * @param value   输入的字符串值
     * @param typeCls 目标类型的 Class 对象
     * @param <T>     泛型参数，表示目标类型
     * @return 转换后的对象实例
     * @throws IllegalArgumentException 如果无法完成转换
     */
    private static <T> T cast(String value, Class<T> typeCls) {
        if (value == null || typeCls == null) {
            throw new IllegalArgumentException("Input value or type class cannot be null.");
        }

        // 处理基本类型和包装类
        if (typeCls == String.class) {
            return typeCls.cast(value);
        } else if (typeCls == Integer.class) {
            return typeCls.cast(Integer.valueOf(value));
        } else if (typeCls == Long.class) {
            return typeCls.cast(Long.valueOf(value));
        } else if (typeCls == Double.class) {
            return typeCls.cast(Double.valueOf(value));
        } else if (typeCls == Float.class) {
            return typeCls.cast(Float.valueOf(value));
        } else if (typeCls == Boolean.class) {
            return typeCls.cast(Boolean.valueOf(value));
        }

        return null;
    }

//    public static void main(String[] args) throws Exception {
//        DataSqlParser sqlParser = new DataSqlParser();
//        sqlParser.dataFilter.setOp(GroupOP.OR);
//
//        DataFilterRule rule1 = new DataFilterRule();
//        rule1.setOp(RuleOP.IN);
//        rule1.setDataType(DataType.STRING);
//        rule1.setField("data_hierarchy_id");
//        rule1.setVal("1,2,3,4");
//        rule1.setAlias("t");
//
//        DataFilterRule rule2 = new DataFilterRule();
//        rule2.setOp(RuleOP.EQ);
//        rule2.setDataType(DataType.NUMBER);
//        rule2.setField("age");
//        rule2.setVal(18);
//
//        DataFilterRule rule3 = new DataFilterRule();
//        rule3.setSqlString("create_id = {userId} and mobile={mobile}");
//
//
//        sqlParser.dataFilter.addRule(rule1);
//        sqlParser.dataFilter.addRule(rule2);
//        //sqlParser.dataFilter.addRule(rule3);
//
//        System.out.println("customSql==== " + sqlParser.getSqlSegment().toString());
//        String boundSql = "select name, password from sys_user where 1=1";
//        Expression rightExpression = sqlParser.getSqlSegment();
//
//        Select select = (Select)CCJSqlParserUtil.parse(boundSql);
//        PlainSelect selectBody = (PlainSelect)select.getSelectBody();
//        if (selectBody.getWhere() != null) {
//            System.out.println("originalSql=== " + selectBody.getWhere());
//            selectBody.setWhere(new AndExpression(selectBody.getWhere(), rightExpression));
//        } else {
//            selectBody.setWhere(rightExpression);
//        }
//
//        System.out.println(">>> "+selectBody.toString());
//
//    }

}
