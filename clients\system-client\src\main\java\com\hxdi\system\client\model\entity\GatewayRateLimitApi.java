package com.hxdi.system.client.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hxdi.common.core.mybatis.base.entity.AbstractEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@TableName("gateway_rate_limit_api")
public class GatewayRateLimitApi extends AbstractEntity {
    /**
     * 限制数量
     */
    private String policyId;

    /**
     * 时间间隔(秒)
     */
    private String apiId;


    private static final long serialVersionUID = 1L;
}
