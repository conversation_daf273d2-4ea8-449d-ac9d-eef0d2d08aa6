package com.hxdi.watermark.handle;

import com.hxdi.watermark.properties.WatermarkProperties;
import com.itextpdf.text.BaseColor;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.Element;
import com.itextpdf.text.pdf.BaseFont;
import com.itextpdf.text.pdf.PdfContentByte;
import com.itextpdf.text.pdf.PdfReader;
import com.itextpdf.text.pdf.PdfStamper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.BufferedOutputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;

/**
 * 添加水印处理
 */
@Slf4j
public class WatermarkHandle {

    private WatermarkProperties watermarkProperties;

    public WatermarkHandle(WatermarkProperties watermarkProperties) {
        this.watermarkProperties = watermarkProperties;
    }

    public OutputStream watermark(MultipartFile file) {
        try {
            ByteArrayOutputStream baos;
            String originFilename = file.getOriginalFilename();
            String fileExtname = StringUtils.substringAfterLast(originFilename, ".");
            if (".pdf".equalsIgnoreCase(fileExtname)) {
                baos = (ByteArrayOutputStream) pdfWatermark(file);
            } else {
                baos = (ByteArrayOutputStream) picWatermark(file);
            }
            return baos;
        } catch (Exception e) {
            log.error("添加水印异常", e);
        }

        return null;
    }

    /**
     * 图片绘制水印
     *
     * @param file
     * @return
     */
    public OutputStream picWatermark(MultipartFile file) {
        try (ByteArrayOutputStream baos = new ByteArrayOutputStream();
             BufferedOutputStream bos = new BufferedOutputStream(baos)
        ) {
            Image image = ImageIO.read(file.getInputStream());
            int width = image.getWidth(null);
            int height = image.getHeight(null);

            //1、创建缓存图片对象
            BufferedImage bufferedImage = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);

            //2、创建JAVA绘图工具对象
            Graphics2D graphics2D = bufferedImage.createGraphics();

            //3、使用绘图工具对象，将原图绘制到缓存图片对象
            graphics2D.drawImage(image, 0, 0, width, height, null);

            //4、使用绘图工具，将水印（文字/图片）绘制到缓存图片对象
            draw(width, height, bufferedImage, graphics2D);

            ImageIO.write(bufferedImage, "JPEG", bos);

//            JPEGImageEncoder jpegImageEncoder = JPEGCodec.createJPEGEncoder(bos);
//            //6、使用图片编码工具类，输出缓存图像到模板图片文件
//            jpegImageEncoder.encode(bufferedImage);

            return baos;
        } catch (Exception e) {
            log.error("图片添加水印异常", e);
        }

        return null;
    }

    /**
     * pdf添加水印
     *
     * @param multipartFile
     * @return
     */
    public OutputStream pdfWatermark(MultipartFile multipartFile) {

        PdfReader reader = null;
        PdfStamper stamper = null;
        try (ByteArrayOutputStream baos = new ByteArrayOutputStream();
             BufferedOutputStream bos = new BufferedOutputStream(baos)
        ) {
            // 读取原始 PDF 文件
            reader = new PdfReader(multipartFile.getInputStream());
            stamper = new PdfStamper(reader, bos);
            // 获取 PDF 中的页数
            int pageCount = reader.getNumberOfPages();
            // 添加水印
            for (int i = 1; i <= pageCount; i++) {
                PdfContentByte contentByte = stamper.getUnderContent(i);
                contentByte.beginText();
                // 设置中文字体
                contentByte.setFontAndSize(BaseFont.createFont(BaseFont.HELVETICA, BaseFont.CP1257, BaseFont.EMBEDDED), 36f);
                // 设置样式
                contentByte.setColorFill(BaseColor.LIGHT_GRAY);
                contentByte.showTextAligned(Element.ALIGN_CENTER, watermarkProperties.getMarkText(), 300, 400, 45);
                contentByte.endText();
            }

            return baos;
        } catch (Exception e) {
            log.error("pdf添加水印异常", e);
        } finally {
            // 保存修改后的 PDF 文件并关闭文件流
            if (stamper != null) {
                try {
                    stamper.close();
                } catch (DocumentException e) {
                    e.printStackTrace();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }

            if (reader != null) {
                reader.close();
            }
        }
        return null;
    }

    /**
     * 图片绘制水印时使用的绘制工具
     *
     * @param width
     * @param height
     * @param bufferedImage
     * @param graphics2D
     */
    public void draw(int width, int height, BufferedImage bufferedImage, Graphics2D graphics2D) {
        //使用绘图工具，将水印（文字/图片）绘制到缓存图片对象
        graphics2D.setFont(new Font(watermarkProperties.getFontName(), watermarkProperties.getFontStyle(), watermarkProperties.getFontSize()));
        graphics2D.setColor(Color.decode(watermarkProperties.getFontColor()));

        //获取文字水印的宽、高
        int width1 = watermarkProperties.getFontSize() * getTextLength(watermarkProperties.getMarkText());
        int height1 = watermarkProperties.getFontSize();

        //设置水印图层
        graphics2D.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_ATOP, watermarkProperties.getAlpha()));
        //图层以中心为基点，转30度的弧度。
        graphics2D.rotate(Math.toRadians(30), bufferedImage.getWidth() / 2, bufferedImage.getHeight() / 2);

        int x = -width / 2;
        int y = -height / 2;

        while (x < width * 1.5) {
            y = -height / 2;
            while (y < height * 1.5) {
                graphics2D.drawString(watermarkProperties.getMarkText(), x, y);
                y += height1 + watermarkProperties.getMarginAll();
            }
            x += width1 + watermarkProperties.getMarginAll();
        }


        graphics2D.dispose();
    }

    /**
     * 获取水印文字大小的方法
     *
     * @param text
     * @return
     */
    public int getTextLength(String text) {
        int length = text.length();

        for (int i = 0; i < text.length(); i++) {
            String s = String.valueOf(text.charAt(i));
            if (s.getBytes().length > 1) {
                length++;
            }
        }

        length = length % 2 == 0 ? length / 2 : length / 2 + 1;
        return length;
    }
}
