package com.hxdi.system.client.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * 系统操作日志实体类
 */
@Getter
@Setter
@TableName("system_log")
@ApiModel(description = "系统操作日志实体类")
public class SystemLog {

    /**
     * 日志ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "日志ID")
    private Long id;

    /**
     * 操作模块
     */
    @ApiModelProperty(value = "操作模块")
    private String module;

    /**
     * 操作描述
     */
    @ApiModelProperty(value = "操作描述")
    private String description;

    /**
     * 请求方法
     */
    @ApiModelProperty(value = "请求方法")
    private String method;

    /**
     * 请求URL
     */
    @ApiModelProperty(value = "请求URL")
    private String requestUrl;

    /**
     * 请求方式
     */
    @ApiModelProperty(value = "请求方式")
    private String requestMethod;

    /**
     * 请求IP
     */
    @ApiModelProperty(value = "请求IP")
    private String requestIp;

    /**
     * 请求参数
     */
    @ApiModelProperty(value = "请求参数")
    private String requestParam;

    /**
     * 响应结果
     */
    @ApiModelProperty(value = "响应结果")
    private String responseResult;

    /**
     * 操作状态（0正常 1异常）
     */
    @ApiModelProperty(value = "操作状态（0正常 1异常）")
    private Integer status;

    /**
     * 错误信息
     */
    @ApiModelProperty(value = "错误信息")
    private String errorMsg;

    /**
     * 操作用户ID
     */
    @ApiModelProperty(value = "操作用户ID")
    private String userId;

    /**
     * 操作用户名
     */
    @ApiModelProperty(value = "操作用户名")
    private String username;

    /**
     * 操作时间
     */
    @ApiModelProperty(value = "操作时间")
    private LocalDateTime operationTime;

    /**
     * 执行时长（毫秒）
     */
    @ApiModelProperty(value = "执行时长（毫秒）")
    private Long executionTime;
} 