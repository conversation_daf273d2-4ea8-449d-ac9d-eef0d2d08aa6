package com.hxdi.common.core.mybatis.base.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;


/**
 * <AUTHOR>
 * @date 2023/2/23 14:57
 * @description 抽象实体类
 * @version 1.0
 */
public abstract class Entity implements Serializable {

    private static final long serialVersionUID = -9039327785518795853L;

    @TableField(exist = false)
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    public Map<String, Object> additional = new HashMap<>(16);

}
