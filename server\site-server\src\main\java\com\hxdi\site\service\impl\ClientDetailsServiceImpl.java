package com.hxdi.site.service.impl;

import com.hxdi.common.core.constants.CommonConstants;
import com.hxdi.common.core.security.BaseClientInfo;
import com.hxdi.common.core.utils.RedisUtil;
import com.hxdi.site.service.feign.BaseAppServiceClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.oauth2.provider.ClientDetails;
import org.springframework.security.oauth2.provider.ClientDetailsService;
import org.springframework.security.oauth2.provider.ClientRegistrationException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * @author: liuyadu
 * @date: 2018/11/12 16:26
 * @description:
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class ClientDetailsServiceImpl implements ClientDetailsService {

    @Autowired
    private BaseAppServiceClient baseAppServiceClient;

    @Autowired
    private RedisUtil redisUtil;

    private static final String ENABLED = "1";

    @Override
    public ClientDetails loadClientByClientId(String clientId) throws ClientRegistrationException {
        BaseClientInfo details = null;
        String key = CommonConstants.APP_CLIENT_PREFIX + clientId;
        if (redisUtil.hasKey(key)) {
            details = (BaseClientInfo)redisUtil.get(key);
        }

        if (details == null){
            details = baseAppServiceClient.getByClientId(clientId).getData();
        }

        if (details != null && details.getClientId() != null && details.getAdditionalInformation() != null) {
            String status = details.getAdditionalInformation().getOrDefault("status", "0").toString();
            if (!ENABLED.equals(status)) {
                throw new ClientRegistrationException("客户端已被禁用");
            }
        }
        return details;
    }
}
