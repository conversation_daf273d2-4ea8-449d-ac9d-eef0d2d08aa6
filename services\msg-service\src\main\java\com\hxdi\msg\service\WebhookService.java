package com.hxdi.msg.service;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hxdi.common.core.model.PageParams;
import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.msg.client.model.entity.WebhookLogs;

/**
 * 异步通知日志接口
 *
 * @author: liuya<PERSON>
 * @date: 2019/2/13 14:39
 * @description:
 */
public interface WebhookService extends IBaseService<WebhookLogs> {
    /**
     * 分页查询
     *
     * @param pageParams
     * @return
     */
    IPage<WebhookLogs> findPage(PageParams pageParams);

}
