package com.hxdi.site.service.feign;

import com.hxdi.system.client.constants.SystemConstants;
import com.hxdi.system.client.service.ISystemDeveloperServiceClient;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

/**
 * @author: liuya<PERSON>
 * @date: 2018/10/24 16:49
 * @description:
 */
@Component
@FeignClient(value = SystemConstants.SYSTEM_SERVER)
public interface SystemDeveloperServiceClient extends ISystemDeveloperServiceClient {


}
