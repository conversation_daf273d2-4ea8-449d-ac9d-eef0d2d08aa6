package com.hxdi.msg.client.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * @program: hxdicloud_2.0
 * @description: 使用消息模版发送消息
 * @author: 王贝强
 * @create: 2025-07-08 18:50
 */
@Getter
@Setter
@ApiModel("使用消息模版批量发送消息")
public class MsgTempateSend {

    @NotEmpty
    @ApiModelProperty("模版标识码")
    List<String> msgCodes;

    @NotEmpty
    @ApiModelProperty("自定义模版内容参数(JSON格式)")
    List<String> jsonParamsList;

    @NotEmpty
    @ApiModelProperty("接收人(字符串格式：使用','分隔的ID)")
    List<String> receivers;
}
