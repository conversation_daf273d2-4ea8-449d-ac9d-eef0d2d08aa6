package com.hxdi.database.autoconfigure;

import com.baomidou.mybatisplus.extension.plugins.inner.InnerInterceptor;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/13 14:14
 * @description mybatisplus 配置器
 * @version 1.0
 */
public abstract class MybatisAppConfigurer {

    /**
     * 多租户拦截器
     */
    protected InnerInterceptor tenantLineInnerInterceptor;

    /**
     * 分页之前配置拦截器列表
     */
    protected List<InnerInterceptor> paginationBeforeInnerInterceptors = new ArrayList<>();
    /**
     * 分页拦截器
     */
    protected InnerInterceptor paginationInnerInterceptor;
    /**
     * 分页之后配置拦截器列表
     */
    protected List<InnerInterceptor> paginationAfterInnerInterceptors = new ArrayList<>();

    /**
     * 重新装配mybatis-plus拦截器
     * @return
     */
    public List<InnerInterceptor> assemblyInnerInterceptors() {
        List<InnerInterceptor> innerInterceptors = new ArrayList<>();
        if (tenantLineInnerInterceptor != null) {
            innerInterceptors.add(tenantLineInnerInterceptor);
        }

        innerInterceptors.addAll(paginationBeforeInnerInterceptors);

        if (paginationInnerInterceptor != null) {
            innerInterceptors.add(paginationInnerInterceptor);
        }

        innerInterceptors.addAll(paginationAfterInnerInterceptors);
        return innerInterceptors;
    }

    public void setTenantLineInnerInterceptor(InnerInterceptor tenantLineInnerInterceptor) {
        this.tenantLineInnerInterceptor = tenantLineInnerInterceptor;
    }

    public void addInnerInterceptorBeforePagination(InnerInterceptor interceptor) {
        this.paginationBeforeInnerInterceptors.add(interceptor);
    }

    public void setPaginationInnerInterceptor(InnerInterceptor paginationInnerInterceptor) {
        this.paginationInnerInterceptor = paginationInnerInterceptor;
    }

    public void addInnerInterceptorAfterPagination(InnerInterceptor interceptor) {
        this.paginationAfterInnerInterceptors.add(interceptor);
    }
}
