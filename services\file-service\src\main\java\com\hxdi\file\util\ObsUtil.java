package com.hxdi.file.util;

import cn.hutool.core.util.StrUtil;
import com.hxdi.common.core.exception.BizExp;
import com.hxdi.file.util.dto.FileParams;
import com.hxdi.file.util.dto.FileWriteResult;
import com.obs.services.ObsClient;
import com.obs.services.exception.ObsException;
import com.obs.services.model.*;
import io.minio.*;
import io.minio.http.Method;
import io.minio.messages.DeleteError;
import io.minio.messages.DeleteObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.net.URL;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/4/24 10:30
 * @description 基于obs对象存储二次封装操作工具类
 * @version 1.0
 */
@Slf4j
public class ObsUtil extends AbstractFileOperator{

    private static final Pattern IMAGE_PATTERN = Pattern.compile("(gif|jpg|jpeg|png)$", Pattern.CASE_INSENSITIVE);


    /**
     * 分段上传的大小20MB
     */
    public static final long PART_SIZE = 20 * 1024 * 1024L;

    /**
     * IO密集型特征：阻塞时cpu是空闲的可以处理其他任务，故线程数可以大于核心数8CPU * 4
     */
    private Executor executorService;

    private ObsClient obsClient;

    private String bucketName;

    public ObsUtil(ObsClient obsClient, Executor executorService, String bucketName) {
        this.obsClient = obsClient;
        this.executorService = executorService;
        this.bucketName = bucketName;
    }

    /**
     * 检查Bucket是否存在
     * @param bucketName
     * @return
     */
    public boolean bucketIsExists(String bucketName) {
        try {
            return obsClient.headBucket(bucketName);
        } catch (ObsException e) {
            log.error("查询Bucket[{}]失败, Error: {}", bucketName);
            logErrorDetail(e);
        }

        return false;
    }

    /**
     * 创建Bucket
     *
     * @param bucketName
     */
    public void createBucket(String bucketName) {
        try {
            ObsBucket obsBucket = new ObsBucket();
            obsBucket.setBucketName(bucketName);
            obsClient.createBucket(obsBucket);
        } catch (ObsException e) {
            log.error("创建Bucket[{}]失败", bucketName);
            this.logErrorDetail(e);
        }
    }

    /**
     * 存储对象-输入流
     * @param is    对象
     * @param params
     * @return
     */
    @Override
    public Optional<FileWriteResult> storeObject(InputStream is, FileParams params) {
        FileWriteResult result = preHandleResult(params);
        try {
            PutObjectRequest request = buildPutObjectRequest(result.getObjectName());
            request.setInput(is);

            PutObjectResult response = obsClient.putObject(request);

            result.setEtag(response.getEtag());
            result.setObjectName(response.getObjectKey());
            return Optional.of(result);
        } catch (ObsException e) {
            log.error("流式上传失败, Bucket[{}], Object[{}]", bucketName, result.getObjectName());
            logErrorDetail(e);
        }

        return Optional.empty();
    }

    /**
     * 本地文件对象上传
     *
     * @param file   文件对象
     * @param params
     */
    public Optional<FileWriteResult> storeObject(File file, FileParams params) {
        FileWriteResult result = preHandleResult(params);
        try {
            PutObjectRequest request = buildPutObjectRequest(result.getObjectName());
            request.setFile(file);
            PutObjectResult response = obsClient.putObject(request);

            result.setEtag(response.getEtag());
            return Optional.of(result);
        } catch (ObsException e) {
            log.error("本地文件对象上传失败, Bucket[{}], Object[{}]", bucketName, result.getObjectName());
            logErrorDetail(e);
        }

        return Optional.empty();
    }

    /**
     * 网络文件对象上传
     *
     * @param file   对象
     * @param params
     */
    public Optional<FileWriteResult> storeObject(MultipartFile file, FileParams params) {
        FileWriteResult result = preHandleResult(params);
        try {
            PutObjectRequest request = buildPutObjectRequest(result.getObjectName());
            request.setInput(file.getInputStream());
            PutObjectResult response = obsClient.putObject(request);

            result.setEtag(response.getEtag());
            return Optional.of(result);
        } catch (ObsException e) {
            log.error("网络文件对象上传失败, Bucket[{}], Object[{}]", bucketName, result.getObjectName());
            logErrorDetail(e);
        } catch (IOException e2) {
            log.error("读取网络文件IO流失败", e2);
        }

        return Optional.empty();
    }

    /**
     * 分段上传
     *
     * @param file
     * @param params
     * @return
     */
    public Optional<FileWriteResult> multiStoreObject(File file, FileParams params) {
        FileWriteResult result = preHandleResult(params);

        // 1. 初始化分段上传任务
        String uploadId = initialMultipartStoreObjectTask(result);

        // 2. 并发分段上传
        List<PartEtag> partEtags = Collections.EMPTY_LIST;
        boolean multipartUploadSuccess = true;
        try {
            partEtags = startMultipartUploading(uploadId, file, result);
        } catch (Exception e) {
            multipartUploadSuccess = false;
            log.error("分段上传失败, Bucket[{}], Object[{}], UploadId[{}]", bucketName, result.getObjectName(), uploadId);
            log.error(e.getMessage(), e);
        }

        if (multipartUploadSuccess) {
            // 3. 分段合并
            partEtags.sort(Comparator.comparing(PartEtag::getPartNumber));
            CompleteMultipartUploadResult response = completeMultipartUpload(uploadId, partEtags, result.getObjectName());
            result.setEtag(response.getEtag());
            return Optional.of(result);
        } else {
            // 4. 失败取消分段上传
            abortMultipartUpload(uploadId, result.getObjectName());
        }

        return Optional.empty();
    }

    /**
     * 初始化分段上传任务
     *
     * @param param
     * @return
     */
    private String initialMultipartStoreObjectTask(FileWriteResult param) {
        InitiateMultipartUploadRequest request = new
                InitiateMultipartUploadRequest(bucketName, param.getObjectName());
        //request.setExpires(1080);

        InitiateMultipartUploadResult result = obsClient.initiateMultipartUpload(request);
        String uploadId = result.getUploadId();
        log.info("初始化分段上传任务，Bucket[{}], Object[{}], UploadId[{}]", bucketName, param.getObjectName(), uploadId);
        return uploadId;
    }

    /**
     * 开始分段上传，并发
     *
     * @param uploadId
     * @param largeFile
     * @param param
     * @return
     */
    private List<PartEtag> startMultipartUploading(String uploadId, File largeFile, FileWriteResult param) {
        long fileSize = largeFile.length();
        // 计算需要上传的段数
        long partCount = (fileSize % PART_SIZE == 0) ? (fileSize / PART_SIZE) : (fileSize / PART_SIZE + 1);

        final CountDownLatch latch = new CountDownLatch((int) partCount);
        final List<PartEtag> partEtags = Collections.synchronizedList(new ArrayList<>());
        for (int i = 0; i < partCount; i++) {
            // 分段起始偏移量
            final long offset = i * PART_SIZE;
            // 分段大小
            final long currPartSize = (i + 1 == partCount) ? fileSize - offset : PART_SIZE;
            // 分段序号
            final int partNumber = i + 1;

            executorService.execute(() -> {
                UploadPartRequest uploadPartRequest = new UploadPartRequest();
                uploadPartRequest.setBucketName(bucketName);
                uploadPartRequest.setObjectKey(param.getObjectName());
                uploadPartRequest.setUploadId(uploadId);
                uploadPartRequest.setFile(largeFile);
                uploadPartRequest.setPartSize(currPartSize);
                uploadPartRequest.setOffset(offset);
                uploadPartRequest.setPartNumber(partNumber);

                try {
                    UploadPartResult uploadPartResult = obsClient.uploadPart(uploadPartRequest);
                    log.info("UploadId#{}, Part#{} is done", uploadId, partNumber);
                    partEtags.add(new PartEtag(uploadPartResult.getEtag(), uploadPartResult.getPartNumber()));
                } catch (ObsException e) {
                    log.error("UploadId#{}, Part#{} is fail", uploadId, partNumber);
                    logErrorDetail(e);
                } finally {
                    latch.countDown();
                }
            });
        }

        try {
            latch.await();
        } catch (InterruptedException e) {
            BizExp.pop(StrUtil.format("UploadId#{}分段上传任务中断异常", uploadId), e);
        }

        if (partEtags.size() < partCount) {
            BizExp.pop(StrUtil.format("UploadId#{}分段上传失败,分段数量#{}, 上传成功分段数量#{}", uploadId, partCount, partEtags.size()));
        }

        return partEtags;
    }

    /**
     * 分段合并
     *
     * @param uploadId
     * @param partEtags
     * @param objectName
     */
    private CompleteMultipartUploadResult completeMultipartUpload(String uploadId, List<PartEtag> partEtags, String objectName) {
        CompleteMultipartUploadRequest completeMultipartUploadRequest = new
                CompleteMultipartUploadRequest(bucketName, objectName, uploadId, partEtags);
        CompleteMultipartUploadResult result = obsClient.completeMultipartUpload(completeMultipartUploadRequest);
        return result;
    }

    /**
     * 取消分段上传任务
     *
     * @param uploadId
     * @param objectName
     */
    private void abortMultipartUpload(String uploadId, String objectName) {
        AbortMultipartUploadRequest request = new AbortMultipartUploadRequest(bucketName, objectName, uploadId);
        obsClient.abortMultipartUpload(request);
    }

    /**
     * 获取对象数据
     * @param params
     * @return
     */
    @Override
    public Optional<InputStream> loadObject(FileParams params) {
        InputStream is = null;
        try {
            ObsObject obsObject = obsClient.getObject(bucketName, params.getObjectName());
            if (obsObject != null) {
                is = obsObject.getObjectContent();
                // 保证流使用后，释放资源
                ByteArrayOutputStream bos = new ByteArrayOutputStream(is.available());
                IOUtils.copy(is, bos);
                return Optional.of(new ByteArrayInputStream(bos.toByteArray()));
            }
        } catch (ObsException e) {
            log.error("获取对象数据失败, Bucket[{}], Object[{}]", bucketName, params.getObjectName());
            logErrorDetail(e);
        } catch (IOException e2) {
            log.error("获取对象数据失败, Bucket[{}], Object[{}], Error: {}", bucketName, params.getObjectName(), e2.getMessage());
        } finally {
            IOUtils.closeQuietly(is);
        }

        return Optional.empty();
    }

    public Optional<List<ObjectListing>> listObjects(FileParams params) {
        // 文件夹名称
        String dirName = params.getObjectName();
        ListObjectsRequest request = new ListObjectsRequest(bucketName);
        request.setMaxKeys(100);
        request.setPrefix(dirName);
        List<ObjectListing> objectListingList = new ArrayList<>();
        ObjectListing result;
        do {
            result = obsClient.listObjects(request);
            objectListingList.add(result);
            request.setMarker(result.getNextMarker());
        } while (result.isTruncated());
        return Optional.of(objectListingList);
    }

    /**
     * 获取文件对象访问链接，一天内有效
     * @param params
     * @return
     */
    @Override
    public Optional<String> getObjectUrl (FileParams params) {
        try {
            TemporarySignatureRequest request = new TemporarySignatureRequest(HttpMethodEnum.GET, 3600);
            request.setBucketName(bucketName);
            request.setObjectKey(params.getObjectName());
            TemporarySignatureResponse response = obsClient.createTemporarySignature(request);
            return Optional.of(response.getSignedUrl());
        } catch (ObsException e) {
            log.error("获取对象临时授权访问链接失败，Bucket[{}], Object[{}]", bucketName, params.getObjectName());
            logErrorDetail(e);
        }

        return Optional.empty();
    }

    /**
     * 删除对象
     * @param params
     */
    @Override
    public void removeObject(FileParams params) {
        try {
            obsClient.deleteObject(bucketName, params.getObjectName());
        } catch (ObsException e) {
            log.error("删除对象失败, Bucket[{}], Object[{}], Error: {}", bucketName, params.getObjectName());
            logErrorDetail(e);
        }
    }

    /**
     * 批量删除对象
     * @param params
     */
    @Override
    public void removeObjects(FileParams params) {
        DeleteObjectsRequest deleteRequest = new DeleteObjectsRequest();
        // 详细模式
        deleteRequest.setQuiet(false);
        deleteRequest.setBucketName(bucketName);

        KeyAndVersion[] kvs = new KeyAndVersion[params.getObjects().size()];
        int index = 0;
        for (String key : params.getObjects()) {
            kvs[index++] = new KeyAndVersion(key);
        }

        deleteRequest.setKeyAndVersions(kvs);

        DeleteObjectsResult deleteResult = obsClient.deleteObjects(deleteRequest);
        for (DeleteObjectsResult.DeleteObjectResult object : deleteResult.getDeletedObjectResults()) {
            log.info("批量删除对象成功，Bucket[{}], Object[{}]", bucketName, object.getObjectKey());
        }

        for (DeleteObjectsResult.ErrorResult error : deleteResult.getErrorResults()) {
            log.info("批量删除对象失败，Bucket[{}], Object[{}]", bucketName, error.getObjectKey());
        }
    }


    /**
     * 构造上传对象的请求实例
     *
     * @param objectKey
     * @return
     */
    private PutObjectRequest buildPutObjectRequest(String objectKey) {
        PutObjectRequest request = new PutObjectRequest();
        request.setBucketName(bucketName);
        // 对象存储时间周期3年
        // request.setExpires(1080);
        request.setObjectKey(objectKey);
        return request;
    }

    /**
     * 打印详细错误信息
     *
     * @param e
     */
    private void logErrorDetail(ObsException e) {
        log.error("HTTP Code: {}, Error Code: {}, Error Message: {}, Stack Message: {}",
                e.getResponseCode(), e.getErrorCode(), e.getErrorMessage(), e.getMessage());
    }
}
