package com.hxdi.common.core.model;

/**
 * <AUTHOR>
 * @date 2022/11/8 16:18
 * @description 密码安全等级
 * @version 1.0
 */
public enum PasswordComplexity implements IEnum<Integer>{
    L1(1, "密码不能少于8位"),
    L2(3, "密码不能少于8位,且应包含数字"),
    L3(5, "密码不能少于8位,且应包含大小写字母"),
    L4(7, "密码不能少于8位,且应包含数字、大小写字母"),
    L5(15,"密码不能少于8位,且应包含数字、大小写字母、特殊符号"),
    ;


    private Integer value;
    private String desc;

    PasswordComplexity(Integer value, String desc){
        this.value = value;
        this.desc = desc;
    }

    @Override
    public Integer value() {
        return value;
    }

    public String desc(){
        return desc;
    }

    public static PasswordComplexity parse(Integer value) {
        return IEnum.innerParse(value, values());
    }

    public static boolean verify(Integer currentLevel, Integer expectedLevel) {
        return currentLevel < expectedLevel ? false : true;
    }
}
