package com.hxdi.gateway.service;

import cn.hutool.core.date.DateUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.hxdi.common.core.constants.CommonConstants;
import com.hxdi.common.core.model.MessageDTO;
import com.hxdi.common.core.security.BaseUserDetails;
import com.hxdi.common.core.utils.JsonConverter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2022/8/25 10:37
 * @description 在线统计
 * @version 1.0
 */
@Slf4j
@Component
public class OnlineStatService {

    @Autowired
    private AmqpTemplate amqpTemplate;

    private final AntPathMatcher antPathMatcher = new AntPathMatcher();

    @JsonIgnore
    private Set<String> observeUri = new HashSet<>(Arrays.asList(new String[]{
            "/system/dict/list/table",
            "/system/tenant/listForFilter",
            "/system/tenant/list",
            "/emm/classification/tree",
            "/system/organ/tree",
            "/system/area/list",
            "/emm/screen/view/aggByEs"
    }));

    /**
     * 命中，更新在线统计
     *
     * @param requestPath
     * @return
     */
    public boolean hit(String requestPath) {
        return observeUri.stream().anyMatch(s -> antPathMatcher.match(s, requestPath));
    }

    /**
     * 登出命中，更新在线统计
     *
     * @param requestPath
     * @return
     */
    public boolean logoutHit(String requestPath) {
        return antPathMatcher.match("/admin/remove/token", requestPath);
    }

    /**
     * 登录命中，更新在线统计
     *
     * @param requestPath
     * @return
     */
    public boolean logonHit(String requestPath) {
        return antPathMatcher.match("/admin/current/user", requestPath);
    }

    public void onHandle(ServerWebExchange exchange) {
        ServerHttpRequest request = exchange.getRequest();
        try {
            String requestPath = request.getURI().getPath();
            Map<String, Object> map = new HashMap<>();
            Object requestTime = exchange.getAttribute("requestTime");
            map.put("requestTime", DateUtil.format((Date) requestTime, "yyyy-MM-dd HH:mm:ss"));

            if (logonHit(requestPath)) {
                map.put("type", "logon");
            } else if (logoutHit(requestPath)) {
                map.put("type", "logout");
            } else if (hit(requestPath)) {
                map.put("type", "hit");
            } else {
                return;
            }

            Mono<Authentication>  authenticationMono = exchange.getPrincipal();
            Mono<BaseUserDetails> authentication = authenticationMono
                    .map(Authentication::getPrincipal)
                    .cast(BaseUserDetails.class);
            authentication.subscribe(user ->
                    map.put("userId", user.getUserId())
            );

            MessageDTO msg = new MessageDTO();
            msg.setPayload(JsonConverter.toJson(map));
            amqpTemplate.convertAndSend(CommonConstants.QUEUE_ONLINE_STAT, msg);
        } catch (Exception e) {
            log.error("【用户在线统计】功能异常", e);
        }
    }
}
