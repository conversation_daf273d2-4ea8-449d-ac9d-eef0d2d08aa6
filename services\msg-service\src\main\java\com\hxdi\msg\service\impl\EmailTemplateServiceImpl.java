package com.hxdi.msg.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.msg.client.model.entity.EmailTemplate;
import com.hxdi.msg.mapper.EmailTemplateMapper;
import com.hxdi.msg.service.EmailTemplateService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 邮件模板配置 服务实现类
 *
 * <AUTHOR>
 * @date 2019-07-17
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class EmailTemplateServiceImpl extends BaseServiceImpl<EmailTemplateMapper, EmailTemplate> implements EmailTemplateService {

    /**
     * 根据模板编号获取模板
     *
     * @param code
     * @return
     */
    @Override
    public EmailTemplate getByCode(String code) {
        QueryWrapper<EmailTemplate> queryWrapper = new QueryWrapper();
        queryWrapper.eq("code", code);
        return baseMapper.selectOne(queryWrapper);
    }
}
