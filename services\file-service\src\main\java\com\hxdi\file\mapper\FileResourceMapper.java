package com.hxdi.file.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.file.client.model.entity.FileResource;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/22 16:33
 * @description
 * @version 1.0
 */
@Repository
@Mapper
public interface FileResourceMapper extends SuperMapper<FileResource> {

    /**
     * 查询资源路径
     * @param resId
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    @Select("select path, extname, etag, content_type from fss_file_resource where id = #{resId}")
    FileResource selectResourcePathAndType(String resId);

    /**
     * 覆写selectBatchIds
     * 租户拦截器不生效
     * @param idList
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    @Override
    List<FileResource> selectBatchIds(@Param(Constants.COLL) Collection<? extends Serializable> idList);
}
