package com.hxdi.msg.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.constants.CommonConstants;
import com.hxdi.common.core.model.PageParams;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.common.core.utils.JsonConverter;
import com.hxdi.common.core.utils.RestHttpClient;
import com.hxdi.msg.client.model.*;
import com.hxdi.msg.dispatcher.MessageDispatcher;
import com.hxdi.msg.mapper.MsgSmsTemplateMapper;
import com.hxdi.msg.service.IMsgNotifyLogService;
import com.hxdi.msg.service.IMsgSmsLogService;
import com.hxdi.msg.service.IMsgSmsTemplateService;
import com.hxdi.msg.service.IMsgSmsUserService;
import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.StringReader;
import java.io.StringWriter;
import java.util.*;


/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-03
 */
@Service
@Slf4j
public class MsgSmsTemplateServiceImpl extends BaseServiceImpl<MsgSmsTemplateMapper, MsgSmsTemplate> implements IMsgSmsTemplateService {

    @Autowired
    private IMsgSmsUserService smsUserService;

    @Autowired
    private MessageDispatcher dispatcher;

    @Autowired
    private IMsgSmsLogService msgSmsLogService;

    @Autowired
    private RestHttpClient restHttpClient;

    @Autowired
    private IMsgNotifyLogService msgNotifyLogService;

    @Override
    public Page<MsgSmsTemplate> pageList(PageParams pageParams) {
        MsgSmsConditon condition = pageParams.mapToBean(MsgSmsConditon.class);
        Page<MsgSmsTemplate> page = new Page<>(pageParams.getPage(), pageParams.getLimit());
        baseMapper.pageList(page, condition);
        return page;
    }

    @Override
    public Page<MsgSmsTemplate> configPageList(MsgSmsConditon condition) {
        Page<MsgSmsTemplate> page = new Page<>(condition.getPage(), condition.getLimit());
        condition.setConfigurable(true);
        baseMapper.pageList(page, condition);
        page.getRecords().forEach(item -> item.setParams(null));
        return page;
    }

    @Override
    public void sendMsg(Map<String, String> map) {
        //通过tpl_code获取模板
        String tplCode = map.get("tpl_code");
        MsgSmsTemplate msgSmsTemplate = this.getOne(new QueryWrapper<MsgSmsTemplate>().eq("code", tplCode));
        String status = msgSmsTemplate.getStatus();

        List<String> sendPhoneList = new ArrayList<>();
        List<String> sendNameList = new ArrayList<>();

        String sendPhone = map.get("sendPhone");
        String sendName = map.get("sendName");

        if (StringUtils.isNotEmpty(sendPhone)){
            sendPhoneList = Arrays.asList(sendPhone.split(","));
        }
        if (StringUtils.isNotEmpty(sendName)){
            sendNameList = Arrays.asList(sendName.split(","));
        }


        if ("1".equals(status)){
            String template = msgSmsTemplate.getTemplate();
            String tplId = msgSmsTemplate.getTplId();
            List<MsgSmsUser> sendList=new ArrayList<>();

            for (int i = 0; i < sendPhoneList.size(); i++) {
                MsgSmsUser msgSmsUser=new MsgSmsUser();
                msgSmsUser.setPhoneNumber(sendPhoneList.get(i));
                msgSmsUser.setUserName(sendNameList.get(i));
                sendList.add(msgSmsUser);
            }

            List<MsgSmsUser> msgSmsUserList = smsUserService.list(new QueryWrapper<MsgSmsUser>().eq("tpl_id", tplId));
            sendList.addAll(msgSmsUserList);
            for (MsgSmsUser msgSmsUser : sendList) {
                map.put("userName",msgSmsUser.getUserName());
                String phoneNumber = msgSmsUser.getPhoneNumber();
                SmsMessage smsMessage=new SmsMessage();
                smsMessage.setPhoneNum(phoneNumber);
                smsMessage.setTemplate(template);
                smsMessage.setTplParams(JsonConverter.toJson(map));
                smsMessage.setTplCode(tplCode);
                smsMessage.setReceveName(msgSmsUser.getUserName());
                smsMessage.setBuzId(map.get("noticeCode"));
                dispatcher.dispatch(smsMessage);
            }
        }
    }

    @Override
    public void sendMsgNotification(Map<String, String> map) {
        //通过tpl_code获取模板
        String tplCode = map.get("tpl_code");
        String thirdIds = map.get("thirdIds");
        MsgSmsTemplate msgSmsTemplate = this.getOne(new QueryWrapper<MsgSmsTemplate>().eq("code", tplCode));
        String status = msgSmsTemplate.getStatus();
        if("1".equals(status) && CommonUtils.isNotEmpty(thirdIds)){
            Map data = new HashMap();
            String url = msgSmsTemplate.getParams();
            data.put("title",msgSmsTemplate.getName());
            data.put("body",process(msgSmsTemplate.getTemplate(),map,null));
            data.put("receiverIds",thirdIds);
            log.info("===================浙政钉消息通知模板：" + msgSmsTemplate.toString()+",参数：" + map.toString());
            Map<String,Object> returnMap = restHttpClient.post(url, data, new ParameterizedTypeReference<Map<String,Object>>() {
            }).complete();
            if((Boolean)returnMap.get("success")) {
                MsgNotifyLog notifyLog = new MsgNotifyLog();
                notifyLog.setTplId(tplCode);
                notifyLog.setMsgId(((Map<String,Object>)returnMap.get("content")).get("msgId") + "");
                notifyLog.setCreateTime(new Date());
                notifyLog.setReceiveId(map.get("thirdIds"));
                notifyLog.setContent(data.get("body").toString());
                msgNotifyLogService.save(notifyLog);
            }
        }

    }

    /**
     * 解析字符串模板,通用方法
     *
     * @param template
     *            字符串模板
     * @param model;
     *            数据
     * @param configuration
     *            配置
     * @return 解析后内容
     */
    public static String process(String template, Map<String, ?> model, Configuration configuration) {
        if (template == null) {
            return null;
        }
        if (configuration == null) {
            configuration = new Configuration();
        }
        StringWriter out = new StringWriter();
        try {
            new Template("template", new StringReader(template), configuration).process(model, out);
        } catch (TemplateException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return out.toString();
    }

}
