package com.hxdi.database.autoconfigure;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.BlockAttackInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.IllegalSQLInnerInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.hxdi.common.core.interceptor.DataPermissionAspect;
import com.hxdi.common.core.utils.RedisUtil;
import com.hxdi.database.interceptor.CustomDataPermissionInterceptor;
import com.hxdi.database.interceptor.DefaultDataPermissionHandler;
import com.hxdi.database.interceptor.ModelMetaObjectHandler;
import com.hxdi.database.properties.DatabaseProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;

/**
 * <AUTHOR>
 * @date 2023/2/6 09:57
 * @description mybatis-plus基础应用配置
 * @version 1.0
 */
@ConditionalOnMissingBean(BaseMybatisAppAutoConfiguration.class)
@EnableConfigurationProperties({DatabaseProperties.class})
@Slf4j
public class BaseMybatisAppAutoConfiguration extends MybatisAppConfigurer {

    private DatabaseProperties databaseProperties;

    public BaseMybatisAppAutoConfiguration(DatabaseProperties databaseProperties) {
        this.databaseProperties = databaseProperties;
    }

    /**
     * mbp插件拦截器配置
     * 注意:
     * 使用多个功能需要注意顺序关系,建议使用如下顺序
     * 多租户,动态表名
     * <数据权限>
     * 分页,乐观锁
     * sql 性能规范,防止全表更新与删除
     *
     * 总结：对 SQL 进行单次改造的插件应优先放入，不对 SQL 进行改造的插件最后放入。
     */
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();

        // 数据权限插件
        DefaultDataPermissionHandler dataPermissionHandler = new DefaultDataPermissionHandler();
        CustomDataPermissionInterceptor dataPermissionInterceptor = new CustomDataPermissionInterceptor(dataPermissionHandler);
        super.addInnerInterceptorBeforePagination(dataPermissionInterceptor);
        interceptor.addInnerInterceptor(dataPermissionInterceptor);
        log.info("【启用数据权限插件】");

        // 分页插件
        PaginationInnerInterceptor paginationInnerInterceptor = new PaginationInnerInterceptor();
        paginationInnerInterceptor.setDbType(DbType.DM);
        paginationInnerInterceptor.setMaxLimit(databaseProperties.getMaxLimit());
        paginationInnerInterceptor.setOverflow(databaseProperties.getOverflow());
        super.setPaginationInnerInterceptor(paginationInnerInterceptor);
        interceptor.addInnerInterceptor(paginationInnerInterceptor);
        log.info("【启用分页插件】");

        // 防止全表更新与删除插件
        if (databaseProperties.getEnableBlockAttack()) {
            BlockAttackInnerInterceptor blockAttackInnerInterceptor = new BlockAttackInnerInterceptor();
            super.paginationAfterInnerInterceptors.add(blockAttackInnerInterceptor);
            interceptor.addInnerInterceptor(blockAttackInnerInterceptor);
            log.info("【启用防止全表更新与删除插件】");
        }
        // sql性能规范插件
        if (databaseProperties.getEnableIllegalSql()) {
            IllegalSQLInnerInterceptor illegalSQLInnerInterceptor = new IllegalSQLInnerInterceptor();
            super.paginationAfterInnerInterceptors.add(illegalSQLInnerInterceptor);
            interceptor.addInnerInterceptor(illegalSQLInnerInterceptor);
            log.info("【启用sql性能规范插件】");
        }
        return interceptor;
    }

    /**
     * 自动填充模型数据
     */
    @Bean
    public MetaObjectHandler modelMetaObjectHandler() {
        ModelMetaObjectHandler metaObjectHandler = new ModelMetaObjectHandler();
        log.info("自动填充模型数据处理器 [{}]", metaObjectHandler);
        return metaObjectHandler;
    }

    /**
     * 数据权限切面处理处理器
     * @return
     */
    @Bean
    public DataPermissionAspect dataPermissionAspect(RedisUtil redisUtil) {
        DataPermissionAspect dataPermissionAspect = new DataPermissionAspect(redisUtil);
        log.info("数据权限切面处理处理器 [{}]", dataPermissionAspect);
        return dataPermissionAspect;
    }
}

