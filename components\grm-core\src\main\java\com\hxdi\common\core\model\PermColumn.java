package com.hxdi.common.core.model;

/**
 * 定义常用的权限字段
 */
public enum PermColumn implements IEnum<String>{

    /**
     * 租户字段
     */
    TENANT_COLUMN("TENANT_ID"),

    /**
     * 数据权限字段
     */
    PERMISSION_COLUMN("DATA_HIERARCHY_ID"),
    /**
     * 用户本人字段
     */
    CREATE_ID("CREATE_ID");

    private String code;

    PermColumn(String code){
        this.code = code;
    }

    @Override
    public String value() {
        return code;
    }

    public static PermColumn parse(String code) {
        return IEnum.innerParse(code, values());
    }
}
