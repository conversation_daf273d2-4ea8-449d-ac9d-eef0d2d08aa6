package com.hxdi.file.client.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.hxdi.common.core.mybatis.base.entity.Entity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/2/22 15:43
 * @description 文件资源对象
 * @version 1.0
 */
@Getter
@Setter
@ToString
@TableName("fss_file_resource")
public class FileResource extends Entity {

    private static final long serialVersionUID = -1672072687716355558L;

    /**
     * 资源ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 资源分类
     */
    private String category;

    /**
     * 原始文件名称
     */
    private String originalFilename;

    /**
     * 扩展名
     */
    private String extname;

    /**
     * 资源访问路径
     */
    private String url;

    /**
     * 资源存储路径
     */
    private String path;

    /**
     * 字节长度
     */
    private Long byteLen;

    /**
     * 内容类型
     */
    private String contentType;

    /**
     * MD5散列值
     */
    private String etag;

    @TableField(value = "create_id", fill = FieldFill.INSERT)
    private String createId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 修改时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    private String tenantId;


}
