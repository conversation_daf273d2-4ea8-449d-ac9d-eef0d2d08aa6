package com.hxdi.msg.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.model.PageParams;
import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.msg.client.model.MsgSmsConditon;
import com.hxdi.msg.client.model.MsgSmsTemplate;

import java.util.Map;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-03
 */
public interface IMsgSmsTemplateService extends IBaseService<MsgSmsTemplate> {

    Page<MsgSmsTemplate> pageList(PageParams build);

    /**
     * 发送短信
     * @param map
     */
    void sendMsg(Map<String, String> map);

    /**
     * 发送消息通知
     * @param map
     */
    void sendMsgNotification(Map<String, String> map);

    Page<MsgSmsTemplate> configPageList(MsgSmsConditon condition);
}
