package com.hxdi.system.client.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.hxdi.system.client.constants.DataChangeType;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/3/21 18:46
 * @description
 */
@Getter
@Setter
@TableName("data_change_log")
public class DataChangeLog implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    private String id;
    private String modelName;
    /**
     * 变更类型：ADD，UPDATE，DELETE，HDELETE
     */
    private String changeType;
    private String identify;
    private String beforeObj;
    private String afterObj;
    private String userName;
    private Integer versionId;
    private String tenantId;
    private String tel;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    public DataChangeLog() {
    }

    public DataChangeLog(String modelName, String changeType, String identify, String beforeObj, String afterObj, String userName, String tel, String tenantId) {
        this.modelName = modelName;
        this.changeType = changeType;
        this.identify = identify;
        this.beforeObj = beforeObj;
        this.afterObj = afterObj;
        this.userName = userName;
        this.tel = tel;
        this.tenantId = tenantId;
    }

    public static DataChangeLogBuilder builder() {
        return new DataChangeLogBuilder();
    }

    public static class DataChangeLogBuilder {
        private String modelName;
        private String changeType;
        private String identify;
        private String beforeObj;
        private String afterObj;
        private String userName;
        private String tel;
        private String tenantId;

        public static final String MODEL_ORGAN = "Organ";
        public static final String MODEL_USER = "User";

        DataChangeLogBuilder() {
        }


        public DataChangeLogBuilder modelName(String modelName) {
            this.modelName = modelName;
            return this;
        }

        public DataChangeLogBuilder changeType(DataChangeType changeType) {
            this.changeType = changeType.name();
            return this;
        }

        public DataChangeLogBuilder identify(String identify) {
            this.identify = identify;
            return this;
        }

        public DataChangeLogBuilder beforeObj(String beforeObj) {
            this.beforeObj = beforeObj;
            return this;
        }

        public DataChangeLogBuilder afterObj(String afterObj) {
            this.afterObj = afterObj;
            return this;
        }

        public DataChangeLogBuilder userName(String userName) {
            this.userName = userName;
            return this;
        }

        public DataChangeLogBuilder tel(String tel) {
            this.tel = tel;
            return this;
        }

        public DataChangeLogBuilder tenantId(String tenantId) {
            this.tenantId = tenantId;
            return this;
        }


        public DataChangeLog build() {
            return new DataChangeLog(this.modelName, this.changeType, this.identify, this.beforeObj, this.afterObj, this.userName, this.tel, this.tenantId);
        }
    }
}
