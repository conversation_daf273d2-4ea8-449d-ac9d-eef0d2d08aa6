package com.hxdi.system.client.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hxdi.common.core.mybatis.base.entity.AbstractEntity;
import lombok.*;

/**
 * 系统权限-功能操作关联表
 *
 * @author: liuya<PERSON>
 * @date: 2018/10/24 16:21
 * @description:
 */
@Getter
@Setter
@ToString
@TableName("system_authority_action")
public class SystemAuthorityAction extends AbstractEntity {
    private static final long serialVersionUID = 1471599074044557390L;
    /**
     * 操作资源ID
     */
    private String actionId;

    /**
     * 权限ID
     */
    private String authorityId;
}
