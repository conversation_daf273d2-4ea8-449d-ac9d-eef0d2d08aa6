package com.hxdi.msg.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hxdi.msg.client.model.entity.MsgTemplate;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hxdi.msg.client.model.vo.MsgTemplateCondition;

import java.util.List;

public interface MsgTemplateService extends IService<MsgTemplate>{

    /**
     * 根据条件获取消息模板列表
     * @param condition
     * @return
     */
    List<MsgTemplate> getListByCondition(MsgTemplateCondition condition);

    /**
     * 根据条件获取消息模板分页列表
     * @param condition
     * @return
     */
    IPage<MsgTemplate> getPageByCondition(MsgTemplateCondition condition);

    /**
     * 保存消息模板
     * @param msgTemplate
     */
    void addV1(MsgTemplate msgTemplate);

    /**
     * 修改消息模板
     * @param msgTemplate
     */
    void updateV1(MsgTemplate msgTemplate);

    /**
     * 修改消息模板状态
     * @param id
     * @param status
     */
    void  changeStatus(String id, Integer status);

    /**
     * 删除消息模板
     * @param id
     */
    void deleteV1(String id);


    //------------ 通过模版发送消息相关接口(具体发送时间及方式由所选模版决定) -----------------

    /**
     * 通过模版发送消息
     * @param msgCode 消息模版标识
     * @param JSONParams json格式的参数
     * @param receiver 接收人
     */
    void sendMsgByTemplate(List<String> msgCodes, List<String> jsonParamsList, List<String> receivers);
}
