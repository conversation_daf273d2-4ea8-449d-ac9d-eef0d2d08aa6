package com.hxdi.msg.service;

import com.hxdi.msg.client.model.EmailMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Component;

import javax.mail.MessagingException;
import javax.mail.internet.MimeMessage;
import java.io.File;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class EmailSender {

    public static final String FILE_PATH = "FILEPATH";
    public static final String FILENAME = "FILENAME";
    public static final String ORIGINAL_FILENAME = "ORIGINAL_FILENAME";

    /**
     * 发送邮件
     */
    public void sendSimpleMail(JavaMailSenderImpl javaMailSender, EmailMessage emailMessage) throws Exception {
        MimeMessage message = javaMailSender.createMimeMessage();
        MimeMessageHelper helper = new MimeMessageHelper(message, true);
        helper.setTo(emailMessage.getTo());
        if (emailMessage.getCc() != null && emailMessage.getCc().length > 0) {
            helper.setCc(emailMessage.getCc());
        }
        helper.setFrom(javaMailSender.getUsername());
        helper.setSubject(emailMessage.getSubject());
        helper.setText(emailMessage.getContent(), true);
        this.addAttachment(helper, emailMessage);
        javaMailSender.send(message);
    }

    private void addAttachment(MimeMessageHelper helper, EmailMessage emailMessage) throws MessagingException {
        if (emailMessage.getAttachments() != null && !emailMessage.getAttachments().isEmpty()) {
            for (Map<String, String> fileMap : emailMessage.getAttachments()) {
                String filePath = fileMap.get(FILE_PATH);
                String originalFilename = fileMap.get(ORIGINAL_FILENAME);
                File file = new File(filePath);
                helper.addAttachment(originalFilename, file);
            }
        }
    }


}
