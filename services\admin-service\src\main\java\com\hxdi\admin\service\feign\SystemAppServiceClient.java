package com.hxdi.admin.service.feign;

import com.hxdi.system.client.service.ISystemAppServiceClient;
import com.hxdi.system.client.constants.SystemConstants;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

/**
 * @author: liuya<PERSON>
 * @date: 2018/10/24 16:49
 * @description:
 */
@Component
@FeignClient(value = SystemConstants.SYSTEM_SERVER)
public interface SystemAppServiceClient extends ISystemAppServiceClient {


}
