package com.hxdi.common.core.properties;

import com.google.common.collect.Lists;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/23 13:50
 * @description swagger参数配置
 * @version 1.0
 */
@Getter
@Setter
@ToString
@ConfigurationProperties(prefix = "cloud.swagger2")
public class SwaggerProperties {
    /**
     * 是否启用swagger,生产环境建议关闭
     */
    private boolean enabled;

    /**
     * 接口文档版本号
     */
    private String version;

    /**
     * 注解扫描路径
     */
    private String basePackage;

    /**
     * 文档标题
     */
    private String title;

    /**
     * 文档描述
     */
    private String description;

    /**
     * 接口维护人
     */
    private String name;

    /**
     *
     */
    private String url;

    /**
     * 联系人邮件地址
     */
    private String email;

    /**
     * 客户端ID
     */
    private String clientId;
    /**
     * 客户端密钥
     */
    private String clientSecret;
    /**
     * 客户端授权范围
     */
    private String scope;
    /**
     * 获取token
     */
    private String accessTokenUri;
    /**
     * 认证地址
     */
    private String userAuthorizationUri;

    private List<String> ignores = Lists.newArrayList();
}
