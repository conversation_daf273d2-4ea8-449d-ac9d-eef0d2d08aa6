package com.hxdi.common.core.utils;

import com.hxdi.common.core.model.ITree;

import java.io.Serializable;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 递归工具类
 * <AUTHOR>
 */
public class TreeUtils {

    /**
     * 获取树形结构数据
     * @param topId
     * @param sourceList
     * @param <S>
     * @param <E>
     * @return
     */
    public static <S extends Serializable, E extends ITree<S, E>> List<E> getTree(S topId, List<E> sourceList){
        //获取顶层数据集合
        List<E> resultList = sourceList.stream()
                .filter(e -> e.getParentId() == null || e.getParentId().equals(topId))
                .collect(Collectors.toCollection(LinkedList::new));

        //获取每个顶层数据子集
        resultList.parallelStream().map(e -> {
            e.setChildren(getSubList(e.getId(), sourceList));
            return e;
        }).count();

        return resultList;
    }

    /**
     * 获取排序之后的数据结构数据
     * @param topId
     * @param sourceList
     * @param keyExtractor
     * @param <S>
     * @param <E>
     * @return
     */
    public static <S extends Serializable, E extends ITree<S, E>> List<E> getTreeAfterOrder(S topId, List<E> sourceList, Function<? super E, ? extends Integer> keyExtractor){
        //获取顶层数据集合
        Map<S, List<E>> QSMap = sourceList.stream().collect(Collectors.groupingBy(E::getParentId));
        List<E> resultList = sourceList.stream()
                .filter(e -> e.getParentId() == null || e.getParentId().equals(topId))
                .sorted(Comparator.comparing(keyExtractor))
                .collect(Collectors.toCollection(LinkedList::new));

        //获取每个顶层数据子集
        resultList.parallelStream().map(e -> {
            e.setChildren(getSubListAfterOrder(e.getId(), QSMap, E::getSeq));
            return e;
        }).count();

        return resultList;
    }

    public static <S extends Serializable, E extends ITree<S, E>> List<E> getTreeAfterOrder(Set<S> topIds, List<E> sourceList, Function<? super E, ? extends Integer> keyExtractor){
        //获取顶层数据集合
        Map<S, List<E>> QSMap = sourceList.stream().collect(Collectors.groupingBy(E::getParentId));
        List<E> resultList = sourceList.stream()
                .filter(e -> e.getParentId() == null || "0".equals(e.getParentId()) || topIds.contains(e.getId()))
                .sorted(Comparator.comparing(keyExtractor))
                .collect(Collectors.toCollection(LinkedList::new));

        //获取每个顶层数据子集
        resultList.parallelStream().map(e -> {
            e.setChildren(getSubListAfterOrder(e.getId(), QSMap, E::getSeq));
            return e;
        }).count();

        return resultList;
    }

    /**
     * 递归操作
     * @param currNode
     * @param sourceList
     * @param <S>
     * @param <E>
     * @return
     */
    private static <S extends Serializable, E extends ITree<S, E>> List<E> getSubList(S currNode, List<E> sourceList){
        //子集直接子对象
        List<E> childList = sourceList.stream().filter(e -> e.getParentId().equals(currNode)).collect(Collectors.toCollection(LinkedList::new));

        //子集的间接子对象
        childList.stream().map(e -> {
            e.setChildren(getSubList(e.getId(), sourceList));
            return e;
        }).count();

        //递归退出条件
        if (childList.size() == 0) {
            return new LinkedList<>();
        }

        return childList;
    }

    /**
     * 添加排序递归操作
     * @param currNode
     * @param QSMap  快速查询
     * @param keyExtractor
     * @param <S>
     * @param <E>
     * @return
     */
    private static <S extends Serializable, E extends ITree<S, E>> List<E> getSubListAfterOrder(S currNode, Map<S, List<E>> QSMap, Function<? super E, ? extends Integer> keyExtractor){
        //子集直接子对象
        List<E> childList = QSMap.get(currNode);
        if (childList != null) {
            childList = childList.stream()
                    .sorted(Comparator.comparing(keyExtractor))
                    .collect(Collectors.toCollection(LinkedList::new));
        } else {
            childList = new LinkedList<>();
        }

        //子集的间接子对象
        childList.stream().map(e -> {
            e.setChildren(getSubListAfterOrder(e.getId(), QSMap, E::getSeq));
            return e;
        }).count();

        return childList;
    }
}
