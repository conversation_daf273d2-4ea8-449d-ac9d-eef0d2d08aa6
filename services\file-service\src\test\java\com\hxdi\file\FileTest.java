package com.hxdi.file;


import com.hxdi.file.util.MinioUtil;
import com.hxdi.file.util.dto.FileParams;
import io.minio.*;
import org.junit.Test;

import java.util.Optional;

//@RunWith(SpringRunner.class)
//@SpringBootTest(classes = {FileServiceApplication.class})
public class FileTest {

    private static String BUCKET = "emr-cloud";

    @Test
    public void test() throws Exception {
        MinioClient minioClient = MinioClient.builder()
                .endpoint("http://10.13.4.129:9000")
                //.credentials("VYWxLGhAg5wrmn3c", "13AkJgezkS1oesScktxyv8xIjJRzmZFa")
                .build();
        MinioUtil minioUtil = new MinioUtil(minioClient, BUCKET);
//        Optional<ObjectWriteResponse> response = minioUtil.storeObject(BUCKET, "hello.txt", "/Users/<USER>/Desktop/hello.txt");
//        System.out.println(response.get().etag());

//        minioUtil.storeObject("helloworld2".getBytes(StandardCharsets.UTF_8), BUCKET, "hello.txt");

//        minioUtil.storeObject("helloworld2".getBytes(StandardCharsets.UTF_8), BUCKET, "var/hello.txt");

//        MultipartFile file = new MockMultipartFile("1", "1.txt", null, "HELLO".getBytes(StandardCharsets.UTF_8));
//        minioUtil.storeObject(file.getInputStream(), new FileParams(file.getOriginalFilename()));

//        Optional<InputStream> optional = minioUtil.loadObject(new FileParams("2023/3/hack_1677753929364.png"));
//        FileUtils.copyInputStreamToFile(optional.get(), new File("/Users/<USER>/Desktop/123.png"));

        Optional<String> url = minioUtil.getObjectUrl(new FileParams("head.jpg"));
        System.out.println(url.get());

        //minioUtil.removeObject(new FileParams("test.png"));
        //minioUtil.removeObjects(new FileParams(Lists.list("2023/3/1_1677751509642.txt","hack_1677752470557.png")));

//        Optional<InputStream> is = minioUtil.loadObject(BUCKET, "/var/hello.txt");
//        System.out.println(is.get().available());

//        minioUtil.loadObject(BUCKET, "hello.txt", "/Users/<USER>/Desktop/demo1.txt");

//        String etag = MD5Util.fileToMD5("/Users/<USER>/Desktop/hello.txt");
//        System.out.println(etag);


    }

}
