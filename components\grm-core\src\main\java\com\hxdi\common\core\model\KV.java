package com.hxdi.common.core.model;

import org.apache.http.util.LangUtils;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021/8/24 8:32 下午
 * @description 键值对
 * @version 1.0
 */
public class KV<K, V> implements Serializable {

    private K key;
    private V value;

    public KV(){
        this(null, null);
    }

    public KV(K key, V value){
        this.key = key;
        this.value = value;
    }

    public void put(K key, V value){
        this.key = key;
        this.value = value;
    }

    public K getKey() {
        return key;
    }

    public void setKey(K key) {
        this.key = key;
    }

    public V getValue() {
        return value;
    }

    public void setValue(V value) {
        this.value = value;
    }

    @Override
    public boolean equals(final Object obj){
        if(obj == null) return false;
        if(this == obj) return true;
        if(obj instanceof KV){
            KV that = (KV) obj;
            return LangUtils.equals(this.key, that.key) && LangUtils.equals(this.value, that.value);
        }else{
            return false;
        }
    }



    @Override
    public int hashCode() {
        int hash = LangUtils.HASH_SEED;
        hash = LangUtils.hashCode(hash, this.key);
        hash = LangUtils.hashCode(hash, this.value);
        return hash;
    }
}
