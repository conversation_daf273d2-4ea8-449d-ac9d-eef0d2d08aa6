package com.hxdi.common.core.exception;

import com.hxdi.common.core.constants.ErrorCode;

/**
 * <AUTHOR>
 * @date 2022/3/1 2:41 下午
 * @description 抛异常
 * @version 1.0
 */
public class BizExp {

    /**
     * 弹出异常
     * @param message
     */
    public static void pop(String message) {
        throw new BaseException(message);
    }

    public static void pop(String message, Throwable e) {
        throw new BaseException(ErrorCode.ERROR.getCode(), message, e);
    }

    public static void pop(Integer code, String message) {
        throw new BaseException(code, message);
    }
    /**
     * 弹出异常
     * @param error
     */
    public static void pop(ErrorCode error) {
        pop(error.getCode(), error.getMessage());
    }
}
