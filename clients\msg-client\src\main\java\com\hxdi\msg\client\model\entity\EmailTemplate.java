package com.hxdi.msg.client.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.hxdi.common.core.mybatis.base.entity.AbstractEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 邮件模板配置
 */
@Getter
@Setter
@TableName("msg_email_template")
public class EmailTemplate extends AbstractEntity {

    private static final long serialVersionUID=1L;

    @TableId(type = IdType.ASSIGN_ID)
    private String tplId;

    @ApiModelProperty(value = "模板名称")
    private String name;

    @ApiModelProperty(value = "模板编码")
    private String code;

    @ApiModelProperty(value = "发送服务器配置")
    private String configId;

    @ApiModelProperty(value = "模板")
    private String template;

    @ApiModelProperty(value = "模板参数")
    private String params;


}
