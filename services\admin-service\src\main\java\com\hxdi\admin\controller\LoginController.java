package com.hxdi.admin.controller;

import cn.hutool.core.codec.Base64;
import com.alibaba.fastjson.JSONObject;
import com.google.code.kaptcha.Producer;
import com.hxdi.admin.service.feign.SystemUserServiceClient;
import com.hxdi.admin.service.impl.KaptchaService;
import com.hxdi.admin.service.impl.OAuthRequest;
import com.hxdi.admin.service.impl.WechatMiniServiceImpl;
import com.hxdi.common.core.constants.CommonConstants;
import com.hxdi.common.core.constants.ErrorCode;
import com.hxdi.common.core.constants.StrPool;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.security.BaseUserDetails;
import com.hxdi.common.core.security.SecurityHelper;
import com.hxdi.common.core.utils.JsonConverter;
import com.hxdi.common.core.utils.codec.RsaEncryptUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.security.oauth2.provider.token.DefaultTokenServices;
import org.springframework.security.oauth2.provider.token.TokenStore;
import org.springframework.security.web.authentication.logout.SecurityContextLogoutHandler;
import org.springframework.web.bind.annotation.*;

import javax.imageio.ImageIO;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.security.Principal;
import java.util.HashMap;
import java.util.Map;

@Api(tags = "用户认证")
@RestController
@Slf4j
public class LoginController {

    @Autowired
    private TokenStore tokenStore;

    @Autowired
    private OAuthRequest oauthRequest;

    @Autowired
    private WechatMiniServiceImpl wechatMiniService;

    @Autowired
    private SystemUserServiceClient systemUserServiceClient;

    @Autowired
    private DefaultTokenServices tokenService;

    @Autowired
    private KaptchaService kaptchaService;


    @ApiOperation(value = "获取当前用户信息")
    @GetMapping("/current/user")
    public ResultBody getUserProfile() {
        return ResultBody.ok().data(SecurityHelper.getUser());
    }

    @ApiOperation(value = "获取当前用户信息-SSO登录")
    @GetMapping("/current/user/sso")
    public Principal principal(Principal principal) {
        return principal;
    }

    @ApiOperation(value = "OAuth2登录-密码模式")
    @PostMapping("/login/token")
    public Object loginByPassword(@RequestParam String username,
                                  @RequestParam String password,
                                  @RequestParam String ApiKey,
                                  @RequestParam(value = "captchaCode", required = false) String captchaCode,
                                  @RequestParam(value = "captchaKey", required = false) String captchaKey,
                                  @RequestParam(value = "securityCheck", required = false, defaultValue = "110") Integer securityCheck,
                                  @RequestHeader HttpHeaders httpHeaders) throws Exception {

        // 验证码验证
        kaptchaService.validateCaptcha(captchaKey, captchaCode, securityCheck);

        //RSA解密
        password = RsaEncryptUtil.getDecryptCode(password);
        JSONObject result = oauthRequest.usernamePassword2Token(username, password, ApiKey, httpHeaders);
        if (result.containsKey(StrPool.ACCESS_TOKEN)) {
            return ResultBody.ok().data(result);
        } else {
            return result;
        }
    }

    @ApiOperation(value = "获取图形验证码")
    @GetMapping("/captcha/image/{key}")
    public void getImageCaptcha(HttpServletResponse response, HttpServletRequest request, @PathVariable("key") String key) throws IOException {
        // 清除浏览器缓存
        response.setDateHeader("Expires", 0);
        response.setHeader("Cache-Control", "no-store, no-cache, must-revalidate");
        response.addHeader("Cache-Control", "post-check=0, pre-check=0");
        response.setHeader("Pragma", "no-cache");
        response.setContentType("image/jpeg");

        // 创建验证码图片
        BufferedImage image = kaptchaService.createImage(key);
        ServletOutputStream out = response.getOutputStream();

        // 输出图片
        ImageIO.write(image, "jpg", out);
        out.flush();
        out.close();
    }

    @ApiOperation("删除令牌")
    @PostMapping("/remove/token")
    public ResultBody removeToken(@RequestParam String token, HttpServletRequest request) {
        OAuth2AccessToken accessToken = tokenStore.readAccessToken(token);
        tokenStore.removeAccessToken(accessToken);
        new SecurityContextLogoutHandler().logout(request, null, null);
        return ResultBody.ok();
    }

    @CrossOrigin
    @ApiOperation("校验令牌")
    @RequestMapping("/check_token/token")
    public Map<String, Object> checkToken(@RequestParam String token) {
        Map<String, Object> resp = new HashMap<>(16);
        try {
            OAuth2AccessToken accessToken = tokenStore.readAccessToken(token);
            OAuth2Authentication authentication = tokenService.loadAuthentication(token);
            if (!authentication.isClientOnly()) {
                if (authentication.getPrincipal() instanceof BaseUserDetails) {
                    BaseUserDetails user = (BaseUserDetails) authentication.getPrincipal();
                    Map<String, Object> result = new HashMap<>(16);
                    result.put("UserId", user.getUserId());
                    result.put("LoginName", user.getUsername());
                    result.put("UserName", user.getNickName());
                    result.put("MobileId", user.getMobile());
                    // 企业编码是统一的4位集团编码
                    //result.put("QyCode", user.getQyCode());
                    // 这个是组织ID，集团和加工企业都有组织ID
                    result.put("OrganId", user.getOrganId());
                    result.put("ExpiresIn", accessToken.getExpiresIn());

                    String resultJson = JsonConverter.toJson(result);
                    resp.put("result", Base64.encode(resultJson));
                    resp.put("code", "200");
                    resp.put("message", "成功");
                }
            }
        } catch (Exception e) {
            resp.put("message", "校验失败");
            resp.put("code", "300");
            log.error("校验令牌异常", e);
        }

        return resp;
    }

    @ApiOperation("OAuth2登录-授权码模式")
    @PostMapping("/login/code/token")
    public Object loginByCode(@RequestParam String client_id,
                               @RequestParam String redirect_uri,
                               @RequestParam String code,
                               @RequestHeader HttpHeaders httpHeaders) throws Exception {

        JSONObject result = oauthRequest.authorizationCode2Token(client_id, redirect_uri, code, httpHeaders);
        if (result.containsKey(StrPool.ACCESS_TOKEN)) {
            return ResultBody.ok().data(result);
        } else {
            return ResultBody.failed().data(result);
        }
    }

    @ApiOperation("小程序登录")
    @PostMapping("/app/wxcode2token")
    public Object wxCode2Token(@RequestParam(value = "code") String code,
                               @RequestParam(value = "ApiKey") String ApiKey,
                               @RequestHeader HttpHeaders httpHeaders) {

        ResultBody resultBody = ResultBody.ok();
        String openId = wechatMiniService.getOpenId(code);

        ResultBody<Integer> checkUserData = systemUserServiceClient.checkUser(openId);
        if (checkUserData.getData().intValue() == 0) {
            return resultBody.code(ErrorCode.ACCOUNT_NOT_EXIST.getCode()).msg(ErrorCode.ACCOUNT_NOT_EXIST.getMessage()).data(openId);
        } else {
            JSONObject result = oauthRequest.usernamePassword2Token(openId, CommonConstants.WECHAT_PW_PREFIX + openId, ApiKey, httpHeaders);
            if (result.containsKey(StrPool.ACCESS_TOKEN)) {
                return ResultBody.ok().data(result);
            }

            return result;
        }
    }

    @ApiOperation("注销账户")
    @PostMapping("/account/unregister")
    public ResultBody accountUnregister(@RequestParam String token, HttpServletRequest request) {
        systemUserServiceClient.unregister();
        OAuth2AccessToken accessToken = tokenStore.readAccessToken(token);
        tokenStore.removeAccessToken(accessToken);
        new SecurityContextLogoutHandler().logout(request, null, null);
        return ResultBody.ok();
    }
}
