<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.msg.mapper.MsgInfoMapper">
  <resultMap id="BaseResultMap" type="com.hxdi.msg.client.model.entity.MsgInfo">
    <!--@mbg.generated-->
    <!--@Table MSG_INFO-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="ALERM_TYPE" jdbcType="INTEGER" property="alermType" />
    <result column="TITLE" jdbcType="VARCHAR" property="title" />
    <result column="CONTENT" jdbcType="VARCHAR" property="content" />
    <result column="AUTO_READ" jdbcType="INTEGER" property="autoRead" />
    <result column="PUBLISHER" jdbcType="VARCHAR" property="publisher" />
    <result column="RCV_TYPE" jdbcType="INTEGER" property="rcvType" />
    <result column="SCHEDULED_TIME" jdbcType="TIMESTAMP" property="scheduledTime" />
    <result column="SEND_TIME" jdbcType="TIMESTAMP" property="sendTime" />
    <result column="STATE" jdbcType="INTEGER" property="state" />
    <result column="ENABLED" jdbcType="INTEGER" property="enabled" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="CREATE_ID" jdbcType="VARCHAR" property="createId" />
    <result column="UPDATE_ID" jdbcType="VARCHAR" property="updateId" />
    <result column="TENANT_ID" jdbcType="VARCHAR" property="tenantId" />
    <result column="DATA_HIERARCHY_ID" jdbcType="VARCHAR" property="dataHierarchyId" />
    <result column="URLS" jdbcType="VARCHAR" property="urls" />
    <result column="PUBLISHER_NAME" jdbcType="VARCHAR" property="publisherName" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, ALERM_TYPE, TITLE, CONTENT, AUTO_READ, PUBLISHER, RCV_TYPE, SCHEDULED_TIME, SEND_TIME, 
    "STATE", ENABLED, CREATE_TIME, UPDATE_TIME, CREATE_ID, UPDATE_ID, TENANT_ID, DATA_HIERARCHY_ID, URLS, PUBLISHER_NAME
  </sql>
  
  <sql id="mi_Base_Column_List">
    mi.ID,
    mi.ALERM_TYPE,
    mi.TITLE,
    mi.CONTENT,
    mi.AUTO_READ,
    mi.PUBLISHER,
    mi.RCV_TYPE,
    mi.SCHEDULED_TIME,
    mi.SEND_TIME,
    mi.STATE,
    mi.ENABLED,
    mi.CREATE_TIME,
    mi.UPDATE_TIME,
    mi.CREATE_ID,
    mi.UPDATE_ID,
    mi.TENANT_ID,
    mi.DATA_HIERARCHY_ID,
    mi.URLS,
    mi.PUBLISHER_NAME
  </sql>

  <select id="selectUnreadMessagesPageForUser" resultMap="BaseResultMap">
    SELECT <include refid="mi_Base_Column_List" />
    FROM MSG_INFO mi
    JOIN MSG_USERS mu ON mi.ID = mu.MSG_ID
    WHERE ( mu.USER_ID = #{userId} OR mu.USER_ID = #{allUsersId} )
    AND mi.ENABLED = 1
    AND mi.STATE = 1
    <if test="alermType != null and alermType != ''">
    AND mi.ALERM_TYPE = #{alermType}
    </if>
    AND NOT EXISTS (
      SELECT 1 FROM MSG_READ_MARK mrm
      WHERE mrm.MSG_ID = mi.ID
      AND ( mrm.USER_ID = #{userId} OR mrm.USER_ID = #{allUsersId} )
    )
    order by mi.CREATE_TIME DESC
  </select>

  <select id="selectUserMessages" resultType="com.hxdi.msg.client.model.entity.MsgInfo">
    SELECT <include refid="mi_Base_Column_List" />,mrm.READ_TIME AS read_time
    FROM MSG_INFO mi
    JOIN MSG_USERS mu ON mi.ID = mu.MSG_ID
    left join MSG_READ_MARK mrm ON mi.ID = mrm.MSG_ID AND mrm.USER_ID = #{userId}
    <where>
      ( mu.USER_ID = #{userId} OR mu.USER_ID = #{allUsersId} )
      <if test="title != null and @plugins.OGNL@isNotEmpty(title)">
        AND mi.title like concat('%',#{title},'%')
      </if>
      <choose>
        <when test="publisher != null and @plugins.OGNL@isNotEmpty(publisher)">
          AND mi.PUBLISHER = #{publisher}
        </when>
        <otherwise>
          AND mi.PUBLISHER NOT IN #{userId}
        </otherwise>
      </choose>
      <choose>
        <when test="isRead != null and @plugins.OGNL@isNotEmpty(isRead)">
          <choose>
            <when test="isRead == '1'.toString()">
              AND mrm.READ_TIME IS NOT NULL
            </when>
            <when test="isRead == '0'.toString()">
              AND mrm.READ_TIME IS NULL
            </when>
          </choose>
        </when>
        <otherwise>
          <!-- 不根据已读状态筛选 -->
        </otherwise>
      </choose>
      AND mi.ENABLED = 1
      AND mi.STATE = 1
      <if test="alermType != null and @plugins.OGNL@isNotEmpty(alermType)">
        AND mi.ALERM_TYPE = #{alermType}
      </if>
    </where>
    <!-- 先按已读状态排序，再按创建时间排序 -->
    order by mrm.READ_TIME DESC , mi.CREATE_TIME DESC
  </select>

  <update id="updateStateAndSendTime">
    UPDATE MSG_INFO
    SET STATE = 1 , SEND_TIME = #{sendTime}
    WHERE ID = #{id}
    </update>

  <select id="selectUnreadMessagesListForUser" resultMap="BaseResultMap">
    SELECT <include refid="mi_Base_Column_List" />
    FROM MSG_INFO mi
    JOIN MSG_USERS mu ON mi.ID = mu.MSG_ID
    WHERE ( mu.USER_ID = #{userId} OR mu.USER_ID = #{allUsersId} )
    AND mi.ENABLED = 1
    AND mi.STATE = 1
    <if test="alermType != null and alermType != ''">
      AND mi.ALERM_TYPE = #{alermType}
    </if>
    AND NOT EXISTS (
    SELECT 1 FROM MSG_READ_MARK mrm
    WHERE mrm.MSG_ID = mi.ID
    AND ( mrm.USER_ID = #{userId} OR mrm.USER_ID = #{allUsersId} )
    )
    order by mi.CREATE_TIME DESC
  </select>
</mapper>