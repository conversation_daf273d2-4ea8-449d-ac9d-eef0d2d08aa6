package com.hxdi.common.core.utils;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jdk8.Jdk8Module;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.module.paramnames.ParameterNamesModule;
import com.hxdi.common.core.constants.ErrorCode;
import com.hxdi.common.core.exception.BaseRestResponseErrorHandler;
import com.hxdi.common.core.exception.BizExp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @date 2022/6/29 17:35 下午
 * @description rest http client
 * @version 1.1
 */
@Slf4j
public class RestHttpClient implements InitializingBean {

    private RestTemplate restTemplate;

    private static final ThreadLocal<Map<String, String>> httpHeadersHolder = ThreadLocal.withInitial(() -> new HashMap<>());

    private static final ThreadLocal<Object> responseBodyHolder = new ThreadLocal<>();

    @Override
    public void afterPropertiesSet() throws Exception {
        SimpleClientHttpRequestFactory requestFactory = new SimpleClientHttpRequestFactory();
        requestFactory.setConnectTimeout(6000);
        requestFactory.setReadTimeout(10000);

        restTemplate = new RestTemplate();
        restTemplate.setRequestFactory(requestFactory);
        restTemplate.setErrorHandler(new BaseRestResponseErrorHandler());

        List<HttpMessageConverter<?>> messageConverters = restTemplate.getMessageConverters();
        for (int i = 0, size = messageConverters.size(); i < size; i++) {
            if (MappingJackson2HttpMessageConverter.class.isInstance(messageConverters.get(i))
                    && messageConverters.get(i).getClass() == MappingJackson2HttpMessageConverter.class) {
                MappingJackson2HttpMessageConverter newMessageConverter = new MappingJackson2HttpMessageConverter();
                ObjectMapper om = new ObjectMapper();
                om.setSerializationInclusion(JsonInclude.Include.NON_NULL);
                om.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
                om.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
                om.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                om.registerModule(new JavaTimeModule());
                om.registerModule(new Jdk8Module());
                om.registerModule(new ParameterNamesModule());
                newMessageConverter.setObjectMapper(om);
                messageConverters.set(i, newMessageConverter);
                break;
            }
        }

        log.info("Initialized RestHttpClient");
    }

    public RestTemplate getRestTemplate() {
        return restTemplate;
    }

    /**
     * 接口认证
     * @param url     授权接口
     * @param params  授权参数
     * @param callback  请求回调
     * @param <T>  响应结果
     * @param <R>  认证信息
     * @return
     */
    public <T, R> RestHttpClient  preAuth(String url, Map<String, ?> params, RequestCallback<T, R> ... callback) {
        Map<String, String> headers = new HashMap<>();
        headers.put(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_FORM_URLENCODED_VALUE);
        this.exchange(url, HttpMethod.POST, headers, params, new ParameterizedTypeReference<T>() {});
        if (callback == null || callback.length == 0) {
            callback = new RequestCallback[]{DEFAULT_CALLBACK};
        }

        R token = callback[0].apply(getResponseBodyAndKeepRequestState());

        buildOAuthAuthorizationHeader((String) token);
        return this;
    }



    public <T> RestHttpClient post(String url, Object requestBody, ParameterizedTypeReference<T> ofType, Object... uriVariables) {
        this.exchange(url, HttpMethod.POST, requestBody, ofType, uriVariables);
        return this;
    }

    public <T> RestHttpClient post(String url, Map<String, Object> requestBody, ParameterizedTypeReference<T> ofType, Object... uriVariables) {
        this.exchange(url, HttpMethod.POST, requestBody, ofType, uriVariables);
        return this;
    }

    public <T> RestHttpClient get(String url, Map<String, Object> requestBody, ParameterizedTypeReference<T> ofType, Object... uriVariables) {
        this.exchange(url, HttpMethod.GET, requestBody, ofType, uriVariables);
        return this;
    }

    /**
     *
     * @param url    请求url，可携带参数
     * @param httpMethod
     * @param requestBody    post请求携带请求体
     * @param headers   请求头
     * @param ofType  响应数据转化为对应类型
     * @param <T>
     * @return
     */
    public <T> void exchange(String url, HttpMethod httpMethod, Map headers, Object requestBody, ParameterizedTypeReference<T> ofType) {
        MultiValueMap headersMap = new LinkedMultiValueMap();
        headersMap.setAll(headers);
        HttpEntity<?> httpEntity;
        if ((headers.containsKey(HttpHeaders.CONTENT_TYPE) && headers.get(HttpHeaders.CONTENT_TYPE).equals(MediaType.APPLICATION_FORM_URLENCODED_VALUE))
                && requestBody instanceof Map) {
            MultiValueMap paramsMap = new LinkedMultiValueMap();
            paramsMap.setAll((Map) requestBody);
            httpEntity = new HttpEntity<>(paramsMap, headersMap);
        } else {
            httpEntity = new HttpEntity(requestBody, headersMap);
        }

        ResponseEntity<T> responseEntity = this.restTemplate.exchange(url, httpMethod, httpEntity, ofType);
        T t = responseEntity.getBody();
        responseBodyHolder.set(t);
    }

    /**
     *
     * @param url    请求url，可携带参数
     * @param httpMethod
     * @param requestBody    post请求携带请求体
     * @param ofType  响应数据转化为对应类型
     * @param uriVariables   url参数中替换值的数据
     * @param <T>
     * @return
     */
    public <T> void exchange(String url, HttpMethod httpMethod, Object requestBody, ParameterizedTypeReference<T> ofType, Object... uriVariables) {
        MultiValueMap headersMap = new LinkedMultiValueMap();
        headersMap.setAll(httpHeadersHolder.get());

        ResponseEntity<T> responseEntity;
        HttpEntity<?> httpEntity = new HttpEntity(requestBody, headersMap);
        if (uriVariables.length == 1 && uriVariables[0] instanceof Map){
            Map<String, Object> uriMap = (Map<String, Object>)uriVariables[0];
            responseEntity = this.restTemplate.exchange(url, httpMethod, httpEntity, ofType, uriMap);
        } else {
            responseEntity = this.restTemplate.exchange(url, httpMethod, httpEntity, ofType, uriVariables);
        }

        T t = responseEntity.getBody();
        responseBodyHolder.set(t);
    }

    /**
     * 添加请求头
     * @param httpHeader
     */
    public RestHttpClient addHttpHeader(Map<String, String> httpHeader) {
        httpHeadersHolder.get().putAll(httpHeader);
        return this;
    }

    /**
     * 添加请求头
     * @param key
     * @param value
     */
    public RestHttpClient addHttpHeader(String key, String value) {
        httpHeadersHolder.get().put(key, value);
        return this;
    }

    /**
     * 获取当前请求结果，注意请求状态未清除, 可能会导致内存泄露。
     * 可直接调用下一个请求，请求状态（httpHeaders）将会被复用
     * @param <T>
     * @return
     */
    public <T> T getResponseBodyAndKeepRequestState() {
        T t = (T) responseBodyHolder.get();
        responseBodyHolder.remove();
        return t;
    }

    /**
     * 获取请求结果，并清除请求状态
     */
    public <T> T complete() {
        httpHeadersHolder.remove();
        T t = (T) responseBodyHolder.get();
        responseBodyHolder.remove();
        return t;
    }

    public void buildBasicAuthorizationHeader(String username, String password){
        Map<String, String> headers = new HashMap<>();
        String tokenStr = username + ":" + password;
        String token =  Base64.getEncoder().encodeToString(tokenStr.getBytes(StandardCharsets.UTF_8));
        headers.put("Token", "Basic " + token);
        addHttpHeader(headers);
    }

    public void buildOAuthAuthorizationHeader(String accessToken){
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", "Bearer " + accessToken);
        addHttpHeader(headers);
    }


    /**
     * <AUTHOR>
     * @date 2023/8/28 19:13
     * @description 请求回调方法
     * @version 1.0
     */
    public interface RequestCallback<T, R> extends Function<T, R> {
        // todo 扩展...
    }

    /**
     * 默认认证回调方法
     */
    public final RequestCallback<Object, String> DEFAULT_CALLBACK = (obj) -> {
        Map<String, Object> response = (Map) obj;
        if (((Integer)response.get("code")).intValue() != 0) {
            BizExp.pop(ErrorCode.ERROR);
        }

        Map<String, ?> tokenInfo = (Map<String, ?>) response.get("data");
        return (String) tokenInfo.get("access_token");
    };
}
