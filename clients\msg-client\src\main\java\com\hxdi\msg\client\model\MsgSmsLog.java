package com.hxdi.msg.client.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-03
 */
@Data
@TableName("msg_sms_log")
public class MsgSmsLog  {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    @TableField("buz_id")
    private String buzId;

    @TableField("tpl_id")
    private String tplId;

    @TableField("content")
    private String content;

    @TableField("creata_time")
    private Date creataTime;

    @TableField("receve_name")
    private String receveName;

    @TableField("phone_number")
    private String phoneNumber;



}
