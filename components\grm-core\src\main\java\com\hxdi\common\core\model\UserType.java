package com.hxdi.common.core.model;

/**
 * 用户类型
 */
public enum UserType {

    SUPER("super-admin", "超级管理员"),
    TENANT("tenant-admin", "租户管理员"),
    ADMIN("admin", "普通管理员"),
    NORMAL("normal", "普通用户"),
    THIRD_PARTY("third-party", "第三方用户");

    private String name;
    private String desc;
    UserType(String name, String desc) {
        this.name = name;
        this.desc = desc;
    }

    public String getName() {
        return name;
    }

    public String getDesc() {
        return desc;
    }

    public boolean match(String typeName) {
        return this.name.equals(typeName) ? true : false;
    }
}
