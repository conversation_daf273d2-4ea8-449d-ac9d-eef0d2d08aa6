package com.hxdi.watermark.properties;

import java.awt.*;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;

@Getter
@Setter
@ConfigurationProperties(prefix = "hx.watermark")
public class WatermarkProperties {
    private String markText = "内部文件，请勿转发!";
    private String fontName = "微软雅黑";
    private int fontStyle = Font.BOLD;
    private int fontSize = 30;
    private int marginAll = 50;
    private String fontColor = "#FF0000";
    private int x = 10;
    private int y = 10;
    private float alpha = 0.3F;
}
