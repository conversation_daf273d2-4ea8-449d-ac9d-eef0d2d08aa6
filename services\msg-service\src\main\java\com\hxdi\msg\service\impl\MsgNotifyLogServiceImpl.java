package com.hxdi.msg.service.impl;

import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.msg.client.model.MsgNotifyLog;
import com.hxdi.msg.client.model.MsgSmsLog;
import com.hxdi.msg.mapper.MsgNotifyLogMapper;
import com.hxdi.msg.service.IMsgNotifyLogService;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-03
 */
@Service
public class MsgNotifyLogServiceImpl extends BaseServiceImpl<MsgNotifyLogMapper, MsgNotifyLog> implements IMsgNotifyLogService {

}
