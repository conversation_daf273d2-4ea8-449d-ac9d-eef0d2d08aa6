package com.hxdi.msg.service.impl;

import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.msg.client.model.entity.EmailLogs;
import com.hxdi.msg.mapper.EmailLogsMapper;
import com.hxdi.msg.service.EmailLogsService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 邮件发送日志 服务实现类
 *
 * <AUTHOR>
 * @date 2019-07-17
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class EmailLogsServiceImpl extends BaseServiceImpl<EmailLogsMapper, EmailLogs> implements EmailLogsService {

}
