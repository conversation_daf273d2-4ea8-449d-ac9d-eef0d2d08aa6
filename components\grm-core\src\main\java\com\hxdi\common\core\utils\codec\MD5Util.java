package com.hxdi.common.core.utils.codec;

import java.io.FileInputStream;
import java.io.InputStream;
import java.security.MessageDigest;

/**
 * md5工具类
 *
 * <AUTHOR>
 * @version $Revision: $Date: 2019-11-15
 * @since 1.0.0
 */
public class MD5Util {

    private static final String ALGORITHM = "MD5";

    private static final char[] HEX_DIGITS = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f'};

    /**
     * 字符串MD5加密
     */
    public static String toMD5(String str) {
        try {
            byte[] sourceBytes = str.getBytes();
            MessageDigest md = MessageDigest.getInstance(ALGORITHM);
            md.update(sourceBytes);
            return toHexString(md.digest());
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 根据文件名获得文件MD5
     */
    public static String fileToMD5(String filePath) {
        try (InputStream is = new FileInputStream(filePath)){
            return streamToMD5(is);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 根据输入流获得文件MD5
     *
     * @param is
     * @return
     */
    public static String streamToMD5(InputStream is) {
        try {
            MessageDigest md = MessageDigest.getInstance(ALGORITHM);
            byte[] buffer = new byte[1024 * 1024];
            int n = 0;
            while ((n = is.read(buffer)) > 0) {
                md.update(buffer, 0, n);
            }
            return toHexString(md.digest());
        } catch (Exception e) {
            return null;
        }
    }

    private static String toHexString(byte[] md) {
        int len = md.length;
        char[] buffer = new char[len * 2];
        for (int i = 0; i < len; i++) {
            byte $index = md[i];
            buffer[2 * i] = HEX_DIGITS[$index >>> 4 & 0xf];
            buffer[i * 2 + 1] = HEX_DIGITS[$index & 0xf];
        }
        return new String(buffer);
    }

    public static void main(String[] args) {
        System.out.println(toMD5("zjscbl@123"));
        //E10ADC3949BA59ABBE56E057F20F883E

        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] digest = md.digest("123456sdf".getBytes("utf-8"));
            System.out.println(digest.length);
            System.out.println(toHexString(digest));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
