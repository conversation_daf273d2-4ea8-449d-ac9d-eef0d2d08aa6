package com.hxdi.file.configuration;

import com.hxdi.common.core.properties.FssProperties;
import com.hxdi.file.component.FssTemplate;
import com.hxdi.file.util.AbstractFileOperator;
import com.hxdi.file.util.LocalFileUtil;
import com.hxdi.file.util.MinioUtil;
import com.hxdi.file.util.ObsUtil;
import com.obs.services.ObsClient;
import com.obs.services.ObsConfiguration;
import io.minio.MinioClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * <AUTHOR>
 * @date 2023/2/24 10:59
 * @description 文件服务配置
 * @version 1.0
 */
@Configuration
public class FileConfiguration {

    @Autowired
    private FssProperties fssProperties;

    @Bean
    @ConditionalOnProperty(name = "fss.type", havingValue = "minio")
    public MinioClient minioClient() {
        return MinioClient.builder()
                .endpoint(fssProperties.getEndpoint())
                .credentials(fssProperties.getAccessKey(), fssProperties.getSecretKey())
                .build();
    }

    @Bean
    @ConditionalOnProperty(name = "fss.type", havingValue = "minio")
    public MinioUtil minioUtil() {
        return new MinioUtil(minioClient(), fssProperties.getBucketName());
    }

    @Bean
    @ConditionalOnProperty(name = "fss.type", havingValue = "local")
    public LocalFileUtil localFileUtil() {
        return new LocalFileUtil(fssProperties);
    }


    @Bean
    @ConditionalOnProperty(name = "fss.type", havingValue = "obs")
    public ObsClient obsClient() {
        ObsConfiguration config = new ObsConfiguration();
        config.setEndPoint(fssProperties.getEndpoint());
        config.setSocketTimeout(60000);
        config.setConnectionTimeout(12000);
        config.setMaxErrorRetry(1);
        // 设置下载缓冲1MB
        config.setReadBufferSize(1024 * 1024);
        config.setWriteBufferSize(1024 * 1024);

        ObsClient obsClient = new ObsClient(fssProperties.getAccessKey(), fssProperties.getSecretKey(), config);
        return obsClient;
    }

    @Bean
    @ConditionalOnProperty(name = "fss.type", havingValue = "obs")
    public ObsUtil obsUtil(ObsClient obsClient) {
        return new ObsUtil(obsClient, multipartAsyncTaskExecutor(), fssProperties.getBucketName());
    }

    @Bean("multipartAsyncTaskExecutor")
    @ConditionalOnProperty(name = "fss.type", havingValue = "obs")
    public Executor multipartAsyncTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(8);
        executor.setMaxPoolSize(16);
        executor.setQueueCapacity(100);
        executor.setKeepAliveSeconds(60);
        executor.setThreadNamePrefix("multipart-async-task-");
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(60);
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        return executor;
    }


    @Bean
    public FssTemplate fssTemplate(AbstractFileOperator fileOperator) {
        return new FssTemplate(fileOperator);
    }
}
