package com.hxdi.system.client.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/11/11 14:51
 * @description 用户登录失败记录
 * @version 1.0
 */
@Getter
@Setter
@TableName("user_attempts")
public class UserAttempts implements Serializable {

    private static final long serialVersionUID = -2610823021554263913L;

    @TableId(type = IdType.INPUT)
    private String userId;
    private Integer attempts;
    private Date updateTime;
}
