package com.hxdi.system.client.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
@TableName("system_filter_rule")
public class SystemFilterRule implements Serializable {

    private static final long serialVersionUID = 5433291149543818303L;

    @TableId(type = IdType.ASSIGN_ID)
    private String dataPriviId;

    private String groupId;

    /**
     * 字段
     */
    private String field;

    /**
     * 值
     */
    private String val;

    /**
     * 数据类型：NUMBER, STRING
     */
    private String dataType;

    private String sqlString;

    private Integer seq;

    /**
     * 符号类型：=,>,>=,<=,<,in,like
     */
    private String op;

    /**
     * 扩展信息, eg: 组织编码
     */
    private String extValue;

    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    @TableField(exist = false)
    private String extName;
}
