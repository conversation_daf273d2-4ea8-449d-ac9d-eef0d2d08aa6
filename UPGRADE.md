# 升级日志

-------------

## 版本关键字解释说明
- fix：修补bug
- chore：构建过程或辅助工具的变动
- docs：文档（documentation）
- feat：新功能（feature）
- refactor：重构（即不是新增功能，也不是修改bug的代码变动）
- perf：改善性能和体现的修改
- build：改变构建流程，新增依赖库、工具等（例如webpack修改）；



## 1.0.0-RELEASE 2023年2月15日 22:05:00
### build
- springboot  `2.1.6.RELEASE > 2.3.7.RELEASE`
- springcloud  `Greenwich.SR2 > Hoxton.SR10`
- springcloud-alibaba  `2.1.0.RELEASE > 2.2.5.RELEASE`
- mybatis-plus  `3.3.0 > 3.5.2`

### feat
- 新增 回收管理功能
- (system-service): 用户管理 支持修改手机

### refactor
- 排除三方组件中低版本jar
- 其它代码优化

### fix
- 
- 其它若干问题修复

