package com.hxdi.common.autoconfigure;

import com.hxdi.common.core.utils.CommonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.web.ServerProperties;
import org.springframework.boot.web.context.WebServerInitializedEvent;
import org.springframework.context.ApplicationListener;

import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.util.Enumeration;

/**
 * <AUTHOR>
 */
@Slf4j
public class ServerConfiguration implements ApplicationListener<WebServerInitializedEvent> {

    private int serverPort;

    private String hostAddress;

    private String contextPath;

    public String getHostAddress() {
        return hostAddress;
    }

    public String getContextPath() {
        return contextPath;
    }

    public String getUrl() {
        return "http://" + this.hostAddress + ":" + this.serverPort + this.contextPath;
    }

    public int getPort() {
        return this.serverPort;
    }


    @Override
    public void onApplicationEvent(WebServerInitializedEvent event) {
        ServerProperties serverProperties = event.getApplicationContext().getBean(ServerProperties.class);
        this.serverPort = event.getWebServer().getPort();
        this.contextPath = serverProperties.getServlet().getContextPath()!=null?serverProperties.getServlet().getContextPath() : "";
        try {
            this.hostAddress = getRealIp();
        } catch (Exception e) {
            e.printStackTrace();
        }
        log.info("hostAddress[{}] serverPort[{}] contextPath[{}]",hostAddress,serverPort,contextPath);
    }


    private String getRealIp() throws SocketException {
        String localip = null;// 本地IP，如果没有配置外网IP则返回它
        String netip = null;// 外网IP

        Enumeration<NetworkInterface> netInterfaces = NetworkInterface.getNetworkInterfaces();
        boolean isFind = false;// 是否找到外网IP
        while (netInterfaces.hasMoreElements() && !isFind) {
            NetworkInterface ni = netInterfaces.nextElement();
            Enumeration<InetAddress> address = ni.getInetAddresses();
            while (address.hasMoreElements()) {
                InetAddress ip = address.nextElement();
                if (!ip.isSiteLocalAddress() && !ip.isLoopbackAddress() && ip.getHostAddress().indexOf(":") == -1) {// 外网IP
                    netip = ip.getHostAddress();
                    isFind = true;
                    break;
                } else if (ip.isSiteLocalAddress() && !ip.isLoopbackAddress() && ip.getHostAddress().indexOf(":") == -1) {// 内网IP
                    localip = ip.getHostAddress();
                }
            }
        }

        if (CommonUtils.isNotBlank(netip)) {
            return netip;
        } else {
            return localip;
        }
    }
}
