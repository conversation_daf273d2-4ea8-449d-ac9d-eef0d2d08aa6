package com.hxdi.msg.client.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @program: hxdicloud_2.0
 * @description: 分类的未读消息数量
 * @author: flying
 * @create: 2025-06-30 10:42
 */
@Getter
@Setter
@ApiModel("分类的未读消息数量")
public class ClassifyMessageNumber implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("未读的待办消息：alermType=1")
    private Integer todoNumber;
    @ApiModelProperty("未读的预警消息：alermType=2")
    private Integer warningNumber;
    @ApiModelProperty("未读的提醒消息：alermType=3")
    private Integer tipNumber;
}
