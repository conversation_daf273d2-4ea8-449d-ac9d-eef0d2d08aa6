package com.hxdi.msg.task;

import com.hxdi.msg.client.model.BaseMessage;
import com.hxdi.msg.exchanger.MessageExchanger;

import java.util.concurrent.Callable;

/**
 * <AUTHOR>
 */
public class MessageTask implements Callable<Boolean> {

    private MessageExchanger exchanger;

    private BaseMessage notify;

    public MessageTask(MessageExchanger exchanger, BaseMessage notify){
        this.exchanger = exchanger;
        this.notify = notify;
    }

    @Override
    public Boolean call() {
        return exchanger.exchange(notify);
    }
}
