package com.hxdi.common.core.model;


import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.google.common.collect.Maps;
import com.hxdi.common.core.constants.ErrorCode;

import java.io.Serializable;
import java.util.Map;
import java.util.ResourceBundle;

/**
 * <AUTHOR>
 */
public class ResultBody<T> implements Serializable {
    private static final long serialVersionUID = -6190689122701100762L;

    /**
     * 响应编码
     */
    private int code = ErrorCode.OK.getCode();
    /**
     * 提示消息
     */
    private String message = ErrorCode.OK.getMessage();

    /**
     * 请求路径
     */
    private String path;

    /**
     * 响应数据
     */
    private T data;

    /**
     * http状态码
     */
    private int httpStatus;

    /**
     * 附加数据
     */
    private Map<String, Object> extra;

    /**
     * 响应时间
     */
    private long timestamp = System.currentTimeMillis();

    public ResultBody() {
        super();
    }

    public int getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    public String getPath() {
        return path;
    }

    public T getData() {
        return data;
    }

    public Map<String, Object> getExtra() {
        return extra;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Object obj){
        //直接获取系统当前时间戳
    }

    @JSONField(serialize = false, deserialize = false)
    @JsonIgnore
    public int getHttpStatus() {
        return httpStatus;
    }

    @JSONField(serialize = false, deserialize = false)
    @JsonIgnore
    public boolean isOk() {
        return this.code == ErrorCode.OK.getCode();
    }


    /**
     * 已弃用， 请使用{@link #OK()}
     * @return
     */
    public static ResultBody ok() {
        return new ResultBody().code(ErrorCode.OK.getCode());
    }

    /**
     * 弃用, 请使用{@link #SUCCESS()}
     * @return
     */
    public static ResultBody success() {
        return new ResultBody().code(ErrorCode.SUCCESS.getCode());
    }

    /**
     * 弃用，请使用{@link #FAILED()}
     * @return
     */
    public static ResultBody failed() {
        return new ResultBody().code(ErrorCode.FAIL.getCode());
    }


    public static <T> ResultBody<T> OK() {
        return new ResultBody<T>().code(ErrorCode.OK.getCode());
    }

    public static<T> ResultBody<T> SUCCESS() {
        return new ResultBody<T>().code(ErrorCode.SUCCESS.getCode());
    }

    public static <T> ResultBody<T> FAILED() {
        return new ResultBody<T>().code(ErrorCode.FAIL.getCode());
    }


    public ResultBody<T> code(int code) {
        this.code = code;
        return this;
    }

    public ResultBody<T> msg(String message) {
        this.message = i18n(message, message);
        return this;
    }

    public ResultBody<T> data(T data) {
        this.data = data;
        return this;
    }

    public ResultBody<T> path(String path) {
        this.path = path;
        return this;
    }

    public ResultBody<T> httpStatus(int httpStatus) {
        this.httpStatus = httpStatus;
        return this;
    }

    public ResultBody<T> put(String key, Object value) {
        if (this.extra == null) {
            this.extra = Maps.newHashMap();
        }
        this.extra.put(key, value);
        return this;
    }

    @Override
    public String toString() {
        return "ResultBody{" +
                "code=" + code +
                ", message='" + message + '\'' +
                ", path='" + path + '\'' +
                ", data=" + data +
                ", httpStatus=" + httpStatus +
                ", extra=" + extra +
                ", timestamp=" + timestamp +
                '}';
    }

    /**
     * 错误信息配置
     */
    @JSONField(serialize = false, deserialize = false)
    @JsonIgnore
    private static ResourceBundle resourceBundle = ResourceBundle.getBundle("error");

    /**
     * 提示信息国际化
     *
     * @param message
     * @param defaultMessage
     * @return
     */
    @JSONField(serialize = false, deserialize = false)
    @JsonIgnore
    private static String i18n(String message, String defaultMessage) {
        return message != null ? (resourceBundle.containsKey(message) ? resourceBundle.getString(message) : defaultMessage) : "";
    }

}
