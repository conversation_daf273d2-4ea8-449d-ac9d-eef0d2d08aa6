package com.hxdi.gateway.filter;

import com.hxdi.gateway.service.OnlineStatService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.server.ServerWebExchange;
import org.springframework.web.server.WebFilter;
import org.springframework.web.server.WebFilterChain;
import reactor.core.publisher.Mono;

/**
 * 实时在线用户数统计过滤器
 * <AUTHOR>
 */
@Slf4j
public class OnlineStatFilter implements WebFilter {

    private OnlineStatService onlineStatService;

    public OnlineStatFilter(OnlineStatService onlineStatService) {
        this.onlineStatService = onlineStatService;
    }

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, WebFilterChain chain) {
        return chain.filter(exchange).then(Mono.fromRunnable(()->{
            onlineStatService.onHandle(exchange);
        }));
    }


}

