package com.hxdi.system.client.service;

import com.hxdi.common.core.model.ResultBody;
import com.hxdi.system.client.model.entity.SystemOrgan;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

public interface ISystemOrganServiceClient {

    /**
     * 根据地区获取组织
     *
     * @param address
     * @return
     */
    @GetMapping("/organ/getByAddress")
    ResultBody<List<SystemOrgan>> getByAddress(@RequestParam("address") String address, @RequestParam(value = "type", required = false, defaultValue = "0") String type);

    /**
     * 查询匹配的组织-根据条线/组织级别/行政区域
     * @param tenantId
     * @param ordination
     * @param region
     * @return
     */
    @GetMapping("/organ/ordination/query")
    ResultBody<SystemOrgan> queryOrdination(@RequestParam("tenantId") String tenantId, @RequestParam("ordination") String ordination, @RequestParam("region") String region);

    /**
     * 查询组织总数 - 根据组织类型
     * @param type
     * @return
     */
    @GetMapping("/organ/count")
    ResultBody<Integer> count(@RequestParam(value = "type", required = false) Integer type);

    @GetMapping({"/organ/type/directOrganList"})
    ResultBody<List<SystemOrgan>> getDirectOrganList(@RequestParam("code") String s, @RequestParam("organTypes") Integer... integers);
}
