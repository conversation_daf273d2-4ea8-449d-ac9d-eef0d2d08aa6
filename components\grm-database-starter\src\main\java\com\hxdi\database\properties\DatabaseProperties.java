package com.hxdi.database.properties;

import com.baomidou.mybatisplus.annotation.DbType;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.HashSet;
import java.util.Set;


/**
 * <AUTHOR>
 * @date 2023/2/6 14:55
 * @description 数据库相关配置
 * @version 1.0
 */
@Getter
@Setter
@ConfigurationProperties(prefix = "hx.database")
public class DatabaseProperties {
    /**
     * 分页大小限制
     */
    private long maxLimit = 500;

    private DbType dbType;
    /** 溢出总页数后是否进行处理 */
    protected Boolean overflow = true;
    /** 生成 countSql 优化掉 join 现在只支持 left join */
    protected Boolean optimizeJoin = true;

    /**
     * 是否启用 防止全表更新与删除插件
     */
    private Boolean enableBlockAttack = false;
    /**
     * 是否启用  sql性能规范插件
     */
    private Boolean enableIllegalSql = false;
    /**
     * 是否启用数据权限
     */
    private Boolean enableDataScope = true;

    /**
     * 在执行sql时，忽略 租户插件自动拼接租户编码的表
     * 仅 COLUMN 模式有效
     */
    private Set<String> ignoreTables = new HashSet<>(32);


}
