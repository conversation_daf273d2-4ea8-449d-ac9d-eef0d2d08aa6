package com.hxdi.system.client.service;

import com.hxdi.common.core.model.ResultBody;
import com.hxdi.system.client.model.entity.GatewayIpLimit;
import com.hxdi.system.client.model.entity.GatewayIpLimitApi;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

public interface IGatewayIpLimitClient {


    /**
     * 获取IP限制策略
     *
     * @param policyId
     * @return
     */
    @GetMapping("/gateway/limit/ip/info")
    ResultBody<GatewayIpLimit> get(@RequestParam("policyId") String policyId);

    /**
     * 查询IP限制策略已绑定API列表
     *
     * @param policyId
     * @return
     */
    @GetMapping("/gateway/limit/ip/api/list")
    ResultBody<List<GatewayIpLimitApi>> getApis(@RequestParam("policyId") String policyId);


}
