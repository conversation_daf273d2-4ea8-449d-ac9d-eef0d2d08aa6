package com.hxdi.system.client.model;

import com.hxdi.system.client.model.entity.SystemApi;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class RateLimitApi extends SystemApi implements Serializable {
    private static final long serialVersionUID = 1212925216631391016L;
    private String itemId;
    private String policyId;
    private String policyName;
    private Long limitQuota;
    private String intervalUnit;
    private String url;

    public String getItemId() {
        return itemId;
    }

    public void setItemId(String itemId) {
        this.itemId = itemId;
    }

    public String getPolicyId() {
        return policyId;
    }

    public void setPolicyId(String policyId) {
        this.policyId = policyId;
    }

    public String getPolicyName() {
        return policyName;
    }

    public void setPolicyName(String policyName) {
        this.policyName = policyName;
    }

    public Long getLimitQuota() {
        return limitQuota;
    }

    public void setLimitQuota(Long limitQuota) {
        this.limitQuota = limitQuota;
    }

    public String getIntervalUnit() {
        return intervalUnit;
    }

    public void setIntervalUnit(String intervalUnit) {
        this.intervalUnit = intervalUnit;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }
}
