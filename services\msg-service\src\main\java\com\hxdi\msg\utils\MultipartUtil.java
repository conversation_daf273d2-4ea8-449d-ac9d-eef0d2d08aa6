package com.hxdi.msg.utils;

import cn.hutool.core.date.DateUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.msg.service.EmailSender;
import org.springframework.web.multipart.MultipartFile;

import javax.mail.MessagingException;
import java.io.File;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @author: liuyadu
 * @date: 2019/7/17 16:26
 * @description:
 */
public class MultipartUtil {

    /**
     * 获取附件路径
     *
     * @throws MessagingException
     */
    public static  List<Map<String,String>> getMultipartFilePaths(MultipartFile[] multipartFiles)  {
        List<Map<String,String>> paths = Lists.newArrayList();

        try {
            if (multipartFiles != null) {
                String dir = System.getProperty("user.dir") + File.separator + "temp" + File.separator + "upload";
                for (MultipartFile multipartFile : multipartFiles) {
                    String originalFilename = multipartFile.getOriginalFilename();
                    String ext;
                    if (originalFilename.contains(".")) {
                        ext = originalFilename.substring(originalFilename.lastIndexOf("."));
                    } else {
                        ext = "";
                    }
                    String tempFileName = CommonUtils.genUUID() + ext;
                    String dirPath = dir + File.separator + DateUtil.format(new Date(), "yyyyMMdd");
                    File dirFile = new File(dirPath);
                    if (!dirFile.exists()) {
                        dirFile.mkdirs();
                    }

                    String filePath = dirPath + File.separator + tempFileName;
                    File targetFile = new File(filePath);
                    multipartFile.transferTo(targetFile);
                    Map fileMap = Maps.newHashMap();
                    fileMap.put(EmailSender.FILENAME, tempFileName);
                    fileMap.put(EmailSender.FILE_PATH, filePath);
                    fileMap.put(EmailSender.ORIGINAL_FILENAME, originalFilename);
                    paths.add(fileMap);
                }
            }
        }catch (Exception e){
            e.printStackTrace();
        }

        return paths;
    }
}
