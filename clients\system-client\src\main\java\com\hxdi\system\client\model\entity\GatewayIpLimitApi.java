package com.hxdi.system.client.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hxdi.common.core.mybatis.base.entity.AbstractEntity;
import lombok.*;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@TableName("gateway_ip_limit_api")
public class GatewayIpLimitApi extends AbstractEntity {

    /**
     * 策略ID
     */
    private String policyId;

    /**
     * 接口资源ID
     */
    private String apiId;


    private static final long serialVersionUID = 1L;

    /**
     * 获取策略ID
     *
     * @return policy_id - 策略ID
     */
    public String getPolicyId() {
        return policyId;
    }

    /**
     * 设置策略ID
     *
     * @param policyId 策略ID
     */
    public void setPolicyId(String policyId) {
        this.policyId = policyId;
    }

    /**
     * 获取接口资源ID
     *
     * @return api_id - 接口资源ID
     */
    public String getApiId() {
        return apiId;
    }

    /**
     * 设置接口资源ID
     *
     * @param apiId 接口资源ID
     */
    public void setApiId(String apiId) {
        this.apiId = apiId;
    }
}
