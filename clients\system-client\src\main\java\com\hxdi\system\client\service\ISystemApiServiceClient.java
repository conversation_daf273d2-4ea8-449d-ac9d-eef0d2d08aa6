package com.hxdi.system.client.service;

import com.hxdi.common.core.model.ResultBody;
import com.hxdi.system.client.model.entity.SystemApi;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ISystemApiServiceClient {

    /**
     * 获取接口列表
     *
     * @param serviceId
     * @return
     */
    @GetMapping("/api/list")
    ResultBody<List<SystemApi>> getList(@RequestParam(value = "serviceId",required = false) String serviceId);

    /**
     * 获取接口信息
     *
     * @param apiId
     * @return
     */
    @GetMapping("/api/info")
    ResultBody<SystemApi> get(@RequestParam("apiId") String apiId);

    /**
     * 获取服务列表
     * @return
     */
    @GetMapping("/api/service/list")
    ResultBody getServiceList();

    /**
     * 获取批量更新的服务器列表
     * @return
     */
    @GetMapping("/api/batch/update/service/list")
    ResultBody getBatchUpdateServiceList();

}
