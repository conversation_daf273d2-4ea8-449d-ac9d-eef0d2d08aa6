package com.hxdi.common.core.security;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ReflectUtil;
import com.hxdi.common.core.constants.CommonConstants;
import com.hxdi.common.core.model.ColumnKey;
import com.hxdi.common.core.model.UserType;
import com.hxdi.common.core.properties.CommonProperties;
import com.hxdi.common.core.utils.CommonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.AuthorityUtils;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.security.oauth2.provider.OAuth2Request;
import org.springframework.security.oauth2.provider.token.DefaultAccessTokenConverter;
import org.springframework.security.oauth2.provider.token.RemoteTokenServices;
import org.springframework.security.oauth2.provider.token.ResourceServerTokenServices;
import org.springframework.security.oauth2.provider.token.TokenStore;
import org.springframework.security.oauth2.provider.token.store.JwtAccessTokenConverter;
import org.springframework.security.oauth2.provider.token.store.JwtTokenStore;
import org.springframework.security.oauth2.provider.token.store.redis.RedisTokenStore;
import org.springframework.util.Assert;

import java.util.*;

/**
 * 认证信息辅助工具类
 *
 * <AUTHOR>
 * <AUTHOR>
 */
@Slf4j
public class SecurityHelper {

    /**
     * Hold User Info
     */
    private static final ThreadLocal<BaseUserDetails> USER_HOLDER = ThreadLocal.withInitial(() -> getUser());

    /**
     * 构造一个空User对象，需要时可重复使用
     */
    private static final BaseUserDetails EMPTY_USER = new BaseUserDetails();

    public static void cleanUserHolder() {
        USER_HOLDER.remove();
    }

    /**
     * 这段代码出现的原因是因为突发的
     * 未预期到obtainUserId() 方法返回为空，理应会通过withInitial设置值的
     * @return
     */
    public static BaseUserDetails obtainUser() {
        BaseUserDetails user = USER_HOLDER.get();
        if (user == null) {
            user = getUser();
            USER_HOLDER.set(user);
        }

        return user;
    }

    /**
     * 获取认证用户信息
     *
     * @return
     */
    public static BaseUserDetails getUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.isAuthenticated() && authentication instanceof OAuth2Authentication) {
            OAuth2Authentication oAuth2Authentication = (OAuth2Authentication) authentication;
            OAuth2Request clientToken = oAuth2Authentication.getOAuth2Request();
            if (!oAuth2Authentication.isClientOnly()) {
                if (authentication.getPrincipal() instanceof BaseUserDetails) {
                    return (BaseUserDetails) authentication.getPrincipal();
                }
                if (authentication.getPrincipal() instanceof Map) {
                    return BeanUtil.mapToBean((Map) authentication.getPrincipal(), BaseUserDetails.class,true);
                }
            } else {
                BaseUserDetails openUser = new BaseUserDetails();
                openUser.setClientId(clientToken.getClientId());
                openUser.setAuthorities(clientToken.getAuthorities());
                return openUser;
            }
        }
        return null;
    }

    /**
     * 获取认证用户信息
     *
     * @return
     */
    public static BaseUserDetails getUser(Object nullable) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null && authentication.isAuthenticated() && authentication instanceof OAuth2Authentication) {
            OAuth2Authentication oAuth2Authentication = (OAuth2Authentication) authentication;
            OAuth2Request clientToken = oAuth2Authentication.getOAuth2Request();
            if (!oAuth2Authentication.isClientOnly()) {
                if (authentication.getPrincipal() instanceof BaseUserDetails) {
                    return (BaseUserDetails) authentication.getPrincipal();
                }
                if (authentication.getPrincipal() instanceof Map) {
                    return BeanUtil.mapToBean((Map) authentication.getPrincipal(), BaseUserDetails.class,true);
                }
            } else {
                BaseUserDetails openUser = new BaseUserDetails();
                openUser.setClientId(clientToken.getClientId());
                openUser.setAuthorities(clientToken.getAuthorities());
                return openUser;
            }
        }
        return null;
    }


     /**
     * 更新OpenUser
     *
     * @param openUser
     */
    public static void updateUser(TokenStore tokenStore, BaseUserDetails openUser) {
        if (openUser == null) {
            return;
        }
        Assert.notNull(openUser.getClientId(), "客户端ID不能为空");
        Assert.notNull(openUser.getUsername(), "用户名不能为空");
        // 动态更新客户端生成的token
        Collection<OAuth2AccessToken> accessTokens = tokenStore.findTokensByClientIdAndUserName(openUser.getClientId(), openUser.getUsername());
        if (accessTokens != null && !accessTokens.isEmpty()) {
            for (OAuth2AccessToken accessToken : accessTokens) {
                // 由于没有set方法,使用反射机制强制赋值
                OAuth2Authentication oAuth2Authentication = tokenStore.readAuthentication(accessToken);
                if (oAuth2Authentication != null) {
                    Authentication authentication = oAuth2Authentication.getUserAuthentication();
                    ReflectUtil.setFieldValue(authentication, "principal", openUser);
                    // 重新保存
                    tokenStore.storeAccessToken(accessToken, oAuth2Authentication);
                }
            }
        }
    }


    /***
     * 更新客户端权限
     * @param tokenStore
     * @param clientId
     * @param authorities
     */
    public static void updateClientAuthorities(TokenStore tokenStore, String clientId, Collection<? extends GrantedAuthority> authorities) {
        if (authorities == null) {
            return;
        }
        // 动态更新客户端生成的token
        Collection<OAuth2AccessToken> accessTokens = tokenStore.findTokensByClientId(clientId);
        if (accessTokens != null && !accessTokens.isEmpty()) {
            Iterator<OAuth2AccessToken> iterator = accessTokens.iterator();
            while (iterator.hasNext()) {
                OAuth2AccessToken token = iterator.next();
                OAuth2Authentication oAuth2Authentication = tokenStore.readAuthentication(token);
                if (oAuth2Authentication != null && oAuth2Authentication.isClientOnly()) {
                    // 只更新客户端权限
                    // 由于没有set方法,使用反射机制强制赋值
                    ReflectUtil.setFieldValue(oAuth2Authentication, "authorities", authorities);
                    // 重新保存
                    tokenStore.storeAccessToken(token, oAuth2Authentication);
                }
            }
        }
    }


    /**
     * 获取当前用户ID
     * 建议使用obtainUserId方法
     * @return
     */
    public static String getUserId() {
        return obtainUser().getUserId();
    }

    /**
     * 安全的获取当前用户ID
     * @return
     */
    public static Optional<String> obtainUserId() {
        String userId = Optional.ofNullable(obtainUser()).orElse(EMPTY_USER).getUserId();
        return Optional.ofNullable(userId);
    }

    /**
     * 安全的获取当前用户机构ID
     * @return
     */
    public static Optional<String> obtainOrganId() {
        String organId = Optional.ofNullable(obtainUser()).orElse(EMPTY_USER).getOrganId();
        return Optional.ofNullable(organId);
    }

    /**
     * 获取当前租户ID
     * 建议使用obtainTenantId方法
     * @deprecated
     * @return
     */
    public static String getTenantId() {
        String tenantId = obtainUser().getTenantId();
        return CommonUtils.isNotEmpty(tenantId) ? tenantId : null;
    }

    /**
     * 安全的获取当前租户ID
     * @return
     */
    public static Optional<String> obtainTenantId() {
        String tenantId = Optional.ofNullable(obtainUser()).orElse(EMPTY_USER).getTenantId();
        return Optional.ofNullable(tenantId);
    }

    /**
     * 判断用户是否是站点用户
     * organType: 1-管理单位，2-站点
     */
    public static boolean isStationUser(UserDetails ... optional) {
        BaseUserDetails user;
        if (optional != null && optional.length == 1) {
            user = (BaseUserDetails) optional[0];
        } else {
            user = obtainUser();
        }

        if (Integer.valueOf("2").equals(user.getOrganType())) {
            return true;
        }

        return false;
    }

    /**
     * 获取当前用户数据权限
     * @return
     */
    public static List<String> getUserScopes() {
        BaseUserDetails user = obtainUser();
        if (isTenantAdminUser(user)) {
            return new ArrayList<>();
        } else {
            return user.getScopes();
        }
    }

    /**
     * 是否是超管
     * @return
     */
    public static boolean isSuperUser(UserDetails ... optional) {
        BaseUserDetails user;
        if (optional != null && optional.length == 1) {
            user = (BaseUserDetails) optional[0];
        } else {
            user = obtainUser();
        }

        return UserType.SUPER.match(user.getUserType());
    }

    /**
     * 是否是租户管理员
     * @return
     */
    public static boolean isTenantAdminUser(UserDetails ... optional) {
        BaseUserDetails user;
        if (optional != null && optional.length == 1) {
            user = (BaseUserDetails) optional[0];
        } else {
            user = obtainUser();
        }

        return UserType.SUPER.match(user.getUserType()) || UserType.TENANT.match(user.getUserType());
    }

    /**
     * 是否是普通管理员
     * @return
     */
    public static boolean isAdminUser(UserDetails ... optional) {
        BaseUserDetails user;
        if (optional != null && optional.length == 1) {
            user = (BaseUserDetails) optional[0];
        } else {
            user = obtainUser();
        }

        return UserType.SUPER.match(user.getUserType()) || UserType.ADMIN.match(user.getUserType());
    }

    /**
     * 获取用户数据权限使用参数
     * @return
     */
    public static Map<String, Object> getUserDataScopeParams() {
        BaseUserDetails user = SecurityHelper.obtainUser();
        Map<String, Object> params = new HashMap<>(16);
        params.put(ColumnKey.PID.value(), user.getPid());
        params.put(ColumnKey.ORG_ID.value(), user.getOrganId());
        params.put(ColumnKey.MOBILE.value(), user.getMobile());
        params.put(ColumnKey.USER_ID.value(), user.getUserId());
        return params;
    }


    /**
     * 是否拥有权限
     *
     * @param authority
     * @return
     */
    public static Boolean hasAuthority(String authority) {
        BaseUserDetails auth = obtainUser();
        if (auth == null) {
            return false;
        }
        if (AuthorityUtils.authorityListToSet(auth.getAuthorities()).contains(authority)) {
            return true;
        }
        return false;
    }

    /**
     * 构建token转换器
     *
     * @return
     */
    public static DefaultAccessTokenConverter buildAccessTokenConverter() {
        BaseUserConverter userAuthenticationConverter = new BaseUserConverter();
        DefaultAccessTokenConverter accessTokenConverter = new DefaultAccessTokenConverter();
        accessTokenConverter.setUserTokenConverter(userAuthenticationConverter);
        return accessTokenConverter;
    }

    /**
     * 构建jwtToken转换器
     *
     * @param properties
     * @return
     */
    public static JwtAccessTokenConverter buildJwtTokenEnhancer(CommonProperties properties) throws Exception {
        JwtAccessTokenConverter converter = new BaseJwtAccessTokenEnhancer();
        converter.setSigningKey(properties.getJwtSigningKey());
        converter.afterPropertiesSet();
        return converter;
    }

    /**
     * 构建自定义远程Token服务类
     *
     * @param properties
     * @return
     */
    public static RemoteTokenServices buildRemoteTokenServices(CommonProperties properties) {
        // 使用自定义系统用户凭证转换器
        DefaultAccessTokenConverter accessTokenConverter = buildAccessTokenConverter();
        RemoteTokenServices tokenServices = new RemoteTokenServices();
        tokenServices.setCheckTokenEndpointUrl(properties.getTokenInfoUri());
        tokenServices.setClientId(properties.getClientId());
        tokenServices.setClientSecret(properties.getClientSecret());
        tokenServices.setAccessTokenConverter(accessTokenConverter);
        log.info("buildRemoteTokenServices[{}]", tokenServices);
        return tokenServices;
    }

    /**
     * 构建资源服务器JwtToken服务类
     *
     * @param properties
     * @return
     */
    public static ResourceServerTokenServices buildJwtTokenServices(CommonProperties properties) throws Exception {
        // 使用自定义系统用户凭证转换器
        DefaultAccessTokenConverter accessTokenConverter = buildAccessTokenConverter();
        BaseJwtTokenService tokenServices = new BaseJwtTokenService();
        // 这里的签名key 保持和认证中心一致
        JwtAccessTokenConverter converter = buildJwtTokenEnhancer(properties);
        JwtTokenStore jwtTokenStore = new JwtTokenStore(converter);
        tokenServices.setTokenStore(jwtTokenStore);
        tokenServices.setJwtAccessTokenConverter(converter);
        tokenServices.setDefaultAccessTokenConverter(accessTokenConverter);
        log.info("buildJwtTokenServices[{}]", tokenServices);
        return tokenServices;
    }

    /**
     * 构建资源服务器RedisToken服务类
     *
     * @return
     */
    public static ResourceServerTokenServices buildRedisTokenServices(RedisConnectionFactory redisConnectionFactory) {
        BaseRedisTokenService tokenServices = new BaseRedisTokenService();
        // 这里的签名key 保持和认证中心一致
        RedisTokenStore redisTokenStore = new RedisTokenStore(redisConnectionFactory);
        tokenServices.setTokenStore(redisTokenStore);
        log.info("buildRedisTokenServices[{}]", tokenServices);
        return tokenServices;
    }
}
