package com.hxdi.obs.service;

import com.hxdi.common.core.model.MessageDTO;
import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.file.client.model.entity.VideoResource;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @date 2023/2/22 16:34
 * @description 视频资源服务接口
 * @version 1.0
 */
public interface FileResourceService extends IBaseService<VideoResource> {

    /**
     * 保存并存储
     * @param file
     * @return
     */
    VideoResource saveAndStore(MultipartFile file);

    /**
     * 上传指定的本地文件
     * @param filePath
     * @return
     */
    VideoResource saveAndStore(String filePath);

    /**
     * 删除
     * @param resIds
     */
    void clean(String[] resIds);

    /**
     * 下载
     * @param resId
     * @param request
     * @param response
     */
    void download(String resId, HttpServletRequest request, HttpServletResponse response);

    /**
     * 读取IO流
     * @param resId
     * @param request
     * @param response
     */
    void getStream(String resId, HttpServletRequest request, HttpServletResponse response);


    /**
     * 查询资源访问URL
     * @param resId
     * @return
     */
    String getSignedUrl(String resId);

    /**
     * 同步天翼云对象信息到数据库
     *
     * @param prefix    对象 key 前缀，即文件夹名称，例：2023/8 或 2023/8/001
     */
    void syncObsObjectsInfo(String prefix);

    /**
     * 订阅消息，保存视频录制并上传的资源信息
     * @param dto
     */
    void saveVideoRecUploadInfo(MessageDTO dto);

    /**
     * 查询告警视频URL
     * @param alarmId
     * @return
     */
    String getAlarmSignedUrl(String alarmId);
}
