package com.hxdi.gateway.service;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.hxdi.common.core.security.BaseSecurityConstants;
import com.hxdi.gateway.configuration.ApiProperties;
import com.hxdi.gateway.service.feign.GatewayServiceClient;
import com.hxdi.system.client.model.RuntimeSettingProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.security.oauth2.provider.token.TokenStore;
import org.springframework.security.oauth2.provider.token.store.redis.JdkSerializationStrategy;
import org.springframework.security.oauth2.provider.token.store.redis.RedisTokenStore;
import org.springframework.security.oauth2.provider.token.store.redis.RedisTokenStoreSerializationStrategy;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.ClassUtils;
import org.springframework.util.ReflectionUtils;
import org.springframework.web.server.ServerWebExchange;

import java.lang.reflect.Method;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2022/8/31 19:00
 * @description 访问令牌延时，自动退出服务
 * @version 1.0
 */
@Slf4j
@Component
public class AuthTokenService implements InitializingBean {

    /**
     * 空闲状态有效时间
     */
    private static long EXPIRES_VALIDITY_SECONDS = 2 * 60 * 60L;  // 2h

    /**
     * 延迟时间，间隔多少分钟刷新一次登录状态
     */
    private static final long EXPIRES_DELAY_SECONDS = 30 * 60L;  // 30m

    private static final boolean springDataRedis_2_0 = ClassUtils.isPresent(
            "org.springframework.data.redis.connection.RedisStandaloneConfiguration",
            RedisTokenStore.class.getClassLoader());

    private final AntPathMatcher antPathMatcher = new AntPathMatcher();

    private boolean checkAccessAlive = false;

    @Autowired
    private TokenStore tokenStore;

    @Autowired
    private RedisConnectionFactory connectionFactory;

    @Autowired
    private ApiProperties apiProperties;

    @Autowired
    private GatewayServiceClient gatewayServiceClient;

    private RedisTokenStoreSerializationStrategy serializationStrategy = new JdkSerializationStrategy();

    private Method redisConnectionSet_2_0;

    {
        if (springDataRedis_2_0) {
            this.loadRedisConnectionMethods_2_0();
        }
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        RuntimeSettingProperties settings = gatewayServiceClient.getRuntimeSettings().getData();
        if (settings != null) {
            EXPIRES_VALIDITY_SECONDS = settings.getAccessValidity() * 60L;
        }

        checkAccessAlive = apiProperties.getCheckAccessAlive();
    }

    /**
     * 忽略掉相关服务以及排除高频访问接口，减小压力
     */
    @JsonIgnore
    private Set<String> ignoreUri = new HashSet<>(Arrays.asList(new String[]{
            "/system/dict/list/table",
            "/system/user/online/cnt",
            "/admin/remove/token",
            "/**/login/**",
            "/**/check_token/token",
            "/odx/api/**",
            "/bi/**",
            "/msg/**"
    }));

    public boolean ignore(String requestPath) {
        return ignoreUri.stream().anyMatch(s -> antPathMatcher.match(s, requestPath));
    }

    public void check(ServerWebExchange exchange) {
        if (checkAccessAlive) {
            ServerHttpRequest request = exchange.getRequest();
            String requestPath = request.getURI().getPath();

            if (ignore(requestPath)) {
                return;
            }

            if (!request.getHeaders().containsKey("Authorization")) {
                return;
            }

            String accessToken = extractToken(request.getHeaders().getFirst("Authorization"));

            OAuth2AccessToken auth2AccessToken = tokenStore.readAccessToken(accessToken);
            if (auth2AccessToken == null || auth2AccessToken.isExpired()) {
                return;
            }

            Map<String,Object> additionalMap = auth2AccessToken.getAdditionalInformation();
            Date aliveTime = (Date) additionalMap.get(BaseSecurityConstants.ACCESS_ALIVE);

            /*
             * 过渡逻辑，当该服务生效时防止已经登录过的用户无法正常访问，执行一次, 未来可能删除这段代码
             */
            if (aliveTime == null) {
                additionalMap.put(BaseSecurityConstants.ACCESS_ALIVE, new Date());
                tokenStore.storeAccessToken(auth2AccessToken, tokenStore.readAuthentication(accessToken));
            }
            // ---- end

            long keepAliveTime = System.currentTimeMillis() - aliveTime.getTime();
            if (keepAliveTime < (EXPIRES_DELAY_SECONDS * 1000L)) {
                return;
            }

            if (keepAliveTime > (EXPIRES_VALIDITY_SECONDS * 1000L)) {
                // 会话时间已过期，自动退出登录
                revokeToken(auth2AccessToken);
                return;
            }

            additionalMap.put(BaseSecurityConstants.ACCESS_ALIVE, new Date());
            OAuth2Authentication authentication = tokenStore.readAuthentication(accessToken);
            tokenStore.storeAccessToken(auth2AccessToken, authentication);
        }
    }

    public boolean revokeToken(OAuth2AccessToken accessToken) {
        if (accessToken.getRefreshToken() != null) {
            tokenStore.removeRefreshToken(accessToken.getRefreshToken());
        }
        tokenStore.removeAccessToken(accessToken);
        return true;
    }

    private RedisConnection getConnection() {
        return connectionFactory.getConnection();
    }

    private byte[] serializeKey(String key) {
        return serializationStrategy.serialize(key);
    }

    private Long deserializeAccessAlive(byte[] bytes) {
        return serializationStrategy.deserialize(bytes, Long.class);
    }

    private void loadRedisConnectionMethods_2_0() {
        this.redisConnectionSet_2_0 = ReflectionUtils.findMethod(
                RedisConnection.class, "set", byte[].class, byte[].class);
    }

    private String extractToken(String accessToken) {
        return accessToken.substring(7);
    }

}
