package com.hxdi.system.client.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.hxdi.common.core.interceptor.DataSqlParser;
import com.hxdi.common.core.model.DataType;
import com.hxdi.common.core.model.GroupOP;
import com.hxdi.common.core.model.PriviType;
import com.hxdi.common.core.model.RuleOP;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@TableName("system_filter_group")
public class SystemFilterGroup implements Serializable {

    private static final long serialVersionUID = 588070144897423472L;

    @TableId(type = IdType.ASSIGN_ID)
    private String dataPriviGroupId;

    /**
     * 规则组名称
     */
    private String groupName;

    /**
     * 主体：用户ID，组织ID，角色ID
     */
    private String subject;

    /**
     * 主体类型：ROLE, DEPT, USER
     */
    private String type;

    /**
     * 默认AND连接
     */
    private String op;


    @TableField(exist = false)
    private List<SystemFilterRule> rules = new ArrayList<>();

    /**
     * 主体名称
     */
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    @TableField(exist = false)
    private String subjectName;

    /**
     * 转换成可被缓存的对象
     * @return
     */
    public DataSqlParser.DataFilterGroup toCaching() {
        DataSqlParser.DataFilterGroup dataScope = new DataSqlParser.DataFilterGroup();
        dataScope.setOp(GroupOP.parse(op));
        dataScope.setRes(groupName);
        dataScope.setSubject(subject);
        dataScope.setType(PriviType.parse(type));

        rules.forEach(rule -> {
            DataSqlParser.DataFilterRule $rule = new DataSqlParser.DataFilterRule();
            $rule.setField(rule.getField());
            $rule.setVal(rule.getVal());
            $rule.setDataType(DataType.parse(rule.getDataType()));
            $rule.setSqlString(rule.getSqlString());
            $rule.setOp(RuleOP.parse(rule.getOp()));
            dataScope.addRule($rule);
        });

        return dataScope;
    }
}
