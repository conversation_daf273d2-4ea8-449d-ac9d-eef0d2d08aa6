package com.hxdi.system.client.model.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hxdi.common.core.mybatis.base.entity.AbstractEntity;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
 * 系统权限-角色关联
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@TableName("system_authority_role")
public class SystemAuthorityRole implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 权限ID
     */
    private String authorityId;

    /**
     * 角色ID
     */
    private String roleId;

    /**
     * 过期时间:null表示长期
     */
    private Date expireTime;

    /**
     * 服务id
     */
    private String serviceId;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    public Date createTime;

}
