package com.hxdi.msg.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.model.PageParams;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.msg.client.model.entity.MsgInfo;
import com.hxdi.msg.client.model.vo.ClassifyMessageNumber;
import com.hxdi.msg.client.model.vo.MsgTempateSend;
import com.hxdi.msg.client.service.IMsgServiceClient;
import com.hxdi.msg.service.MsgInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * 消息服务控制层
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/message/info")
@Api(tags = "消息服务")
public class MsgInfoController implements IMsgServiceClient {
    /**
     * 服务对象
     */
    @Resource
    private MsgInfoService msgInfoService;


    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @GetMapping("/selectOne")
    @ApiOperation("根据ID查找数据")
    public ResultBody<MsgInfo> selectOne(Integer id) {
        return ResultBody.<MsgInfo>OK().data(msgInfoService.getById(id));
    }

    @PostMapping("/add")
    @ApiOperation("发送消息")
    public ResultBody<Void> saveMessage(@RequestBody MsgInfo msgInfo) {
        msgInfoService.saveMessage(msgInfo);
        return ResultBody.OK();
    }

    @PostMapping("/update")
    @ApiOperation("修改消息(注意：只有没有发送的消息才能被更新)")
    public ResultBody<Void> updateMessage(@RequestBody MsgInfo msgInfo) {
        msgInfoService.updateMessage(msgInfo);
        return ResultBody.OK();
    }

    @GetMapping("/delete")
    @ApiOperation("删除消息")
    public ResultBody<Void> deleteMessage(@RequestParam(value = "id") String id) {
        msgInfoService.deleteMessage(id);
        return ResultBody.OK();
    }

    @GetMapping("/unread")
    @ApiOperation("获取未读消息分页")
    public ResultBody<Page<MsgInfo>> getUnreadMessages(@RequestParam(value = "userId") String userId,
                                                       @RequestParam(value = "alermType", required = false) String alermType,
                                                       @RequestParam(value = "page", defaultValue = "1", required = false) Integer page,
                                                       @RequestParam(value = "limit", defaultValue = "10", required = false) Integer size) {
        PageParams pageParams = new PageParams(page, size);
        return ResultBody.<Page<MsgInfo>>OK().data(msgInfoService.getUnreadMessages(userId, alermType, pageParams));
    }

    @GetMapping("/unreadList")
    @ApiOperation("获取未读消息列表")
    public ResultBody<List<MsgInfo>> getUnreadMessagesList(@RequestParam(value = "userId") String userId,
                                                           @RequestParam(value = "alermType", required = false) String alermType) {
        return ResultBody.<List<MsgInfo>>OK().data(msgInfoService.getUnreadMessages(userId, alermType));
    }

    @GetMapping("/all")
    @ApiOperation("获取收到的所有消息")
    public ResultBody<Page<MsgInfo>> getUserMessages(
            @RequestParam(value = "userId") String userId,
            @RequestParam(value = "title", required = false) String title,
            @RequestParam(value = "publisher", required = false) String publisher,
            @RequestParam(value = "alermType", required = false) String alermType,
            @RequestParam(value = "isRead", required = false) String isRead,
            @RequestParam(value = "page", defaultValue = "1", required = false) Integer page,
            @RequestParam(value = "limit", defaultValue = "10", required = false) Integer limit) {
        PageParams pageParams = new PageParams(page, limit);
        return ResultBody.<Page<MsgInfo>>OK().data(msgInfoService.getUserMessages(userId, title, publisher, isRead, alermType, pageParams));
    }

    @GetMapping("/allSend")
    @ApiOperation("获取发送的所有消息")
    public ResultBody<Page<MsgInfo>> getSendMessages(
            @RequestParam(value = "userId") String userId,
            @RequestParam(value = "title", required = false) String title,
            @RequestParam(value = "state", required = false) String state,
            @RequestParam(value = "alermType", required = false) String alermType,
            @RequestParam(value = "isAdmin", required = false, defaultValue = "false") Boolean isAdmin,
            @RequestParam(value = "page", defaultValue = "1", required = false) Integer page,
            @RequestParam(value = "limit", defaultValue = "10", required = false) Integer limit) {
        PageParams pageParams = new PageParams(page, limit);
        return ResultBody.<Page<MsgInfo>>OK().data(msgInfoService.getSendMessages(userId, title, state, alermType, isAdmin, pageParams));
    }

    @GetMapping("/markAsRead")
    @ApiOperation("标记消息为已读")
    public ResultBody<Void> markAsRead(@RequestParam(value = "msgId") String msgId,
                                       @RequestParam(value = "userId") String userId) {
        msgInfoService.markAsRead(msgId, userId);
        return ResultBody.OK();
    }

    @GetMapping("/markBatchAsRead")
    @ApiOperation("批量标记消息为已读")
    public ResultBody<Void> markBatchAsRead(@RequestParam(value = "msgIds", required = false) String msgIds,
                                            @RequestParam(value = "alermType", required = false) String alermType,
                                            @RequestParam(value = "userId") String userId) {
        if (CommonUtils.isNotBlank(msgIds)) {
            String[] msgIdStr = msgIds.split(",");
            List<String> msgIdList = Arrays.asList(msgIdStr);
            msgInfoService.markBatchAsRead(msgIdList, alermType, userId);
        } else {
            msgInfoService.markBatchAsRead(null, alermType, userId);
        }

        return ResultBody.OK();
    }

    @GetMapping("/markBatchAsReadV2")
    @ApiOperation("批量标记消息为已读（用于自动已读操作）")
    public ResultBody<Void> markBatchAsReadV2(@RequestParam(value = "msgIds") String msgIds,
                                              @RequestParam(value = "userIds") List<String> userIds) {
        msgInfoService.markBatchAsRead(msgIds, userIds);
        return ResultBody.OK();
    }

    @GetMapping("/classifyNumber")
    @ApiOperation("按分类获取未读的消息数量")
    public ResultBody<ClassifyMessageNumber> getClassifyMessageNumber(@RequestParam(value = "userId") String userId) {
        return ResultBody.<ClassifyMessageNumber>OK().data(msgInfoService.getClassifyMessageNumber(userId));
    }

    @PostMapping("/sendMessageByTemplate")
    @ApiOperation("通过模版发送消息(为业务系统提供远程调用发送消息接口)")
    public ResultBody<Void> sendMessageByTemplate(@RequestBody MsgTempateSend send) {
        msgInfoService.sendMsgByTemplate(send.getMsgCodes(), send.getJsonParamsList(), send.getReceivers());
        return ResultBody.OK();
    }

}
