package com.hxdi.msg.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.model.PageParams;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.msg.client.model.entity.EmailConfig;
import com.hxdi.msg.service.EmailConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.Map;

/**
 * 邮件发送配置 前端控制器
 */
@Api(tags = "邮件发送配置")
@RestController
@RequestMapping("/emailConfig")
public class EmailConfigController {

    @Autowired
    private EmailConfigService targetService;

    @ApiOperation("刷新缓存")
    @GetMapping("/refresh")
    public ResultBody refresh() {
        targetService.loadCacheConfig();
        return ResultBody.ok();
    }

    @ApiOperation("获取分页数据")
    @GetMapping("/list")
    public ResultBody<Page<EmailConfig>> list(@RequestParam(required = false) Map map) {
        QueryWrapper<EmailConfig> queryWrapper = new QueryWrapper();
        return ResultBody.ok().data(targetService.page(new PageParams(map), queryWrapper));
    }

    @ApiOperation("根据ID查找数据")
    @GetMapping("/get")
    public ResultBody<EmailConfig> get(@RequestParam("id") String id) {
        EmailConfig entity = targetService.getById(id);
        return ResultBody.ok().data(entity);
    }

    @ApiOperation("添加数据")
    @PostMapping("/add")
    public ResultBody add(@RequestParam(value = "name") String name,
                          @RequestParam(value = "smtpHost") String smtpHost,
                          @RequestParam(value = "smtpUsername") String smtpUsername,
                          @RequestParam(value = "smtpPassword") String smtpPassword) {

        EmailConfig entity = new EmailConfig();
        entity.setName(name);
        entity.setSmtpHost(smtpHost);
        entity.setSmtpUsername(smtpUsername);
        entity.setSmtpPassword(smtpPassword);
        targetService.save(entity);
        return ResultBody.ok();
    }

    @ApiOperation("更新数据")
    @PutMapping("/update")
    public ResultBody add(@RequestParam(value = "configId") String configId,
                          @RequestParam(value = "name") String name,
                          @RequestParam(value = "smtpHost") String smtpHost,
                          @RequestParam(value = "smtpUsername") String smtpUsername,
                          @RequestParam(value = "smtpPassword") String smtpPassword) {

        EmailConfig entity = new EmailConfig();
        entity.setConfigId(configId);
        entity.setName(name);
        entity.setSmtpHost(smtpHost);
        entity.setSmtpUsername(smtpUsername);
        entity.setSmtpPassword(smtpPassword);
        targetService.updateById(entity);
        return ResultBody.ok();
    }


    @ApiOperation("删除数据")
    @DeleteMapping("/remove")
    public ResultBody remove(@RequestParam(value = "id") Long id) {
        targetService.removeById(id);
        return ResultBody.ok();
    }

    @ApiOperation("批量删除数据")
    @DeleteMapping("/batch/remove")
    public ResultBody batchRemove(@RequestParam(value = "ids") String ids) {
        targetService.removeByIds(Arrays.asList(ids.split(",")));
        return ResultBody.ok();
    }
}
