<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdicloud.msg.mapper.MsgSmsUserMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hxdi.msg.client.model.MsgSmsUser">
        <result column="id" property="id" />
        <result column="tpl_id" property="tplId" />
        <result column="user_id" property="userId" />
        <result column="user_name" property="userName" />
        <result column="phone_number" property="phoneNumber" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, tpl_id, user_id, user_name, phone_number
    </sql>

</mapper>
