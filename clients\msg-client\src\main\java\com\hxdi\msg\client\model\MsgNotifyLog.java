package com.hxdi.msg.client.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("msg_notify_log")
public class MsgNotifyLog {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    @TableField("tpl_id")
    private String tplId;

    @TableField("msg_id")
    private String msgId;

    @TableField("content")
    private String content;

    @TableField("create_time")
    private Date createTime;

    @TableField("receive_id")
    private String receiveId;

}
