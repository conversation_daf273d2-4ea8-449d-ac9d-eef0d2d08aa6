package com.hxdi.system.client.model;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/11/7 15:04
 * @description 系统运行时设置
 * @version 1.0
 */
@Getter
@Setter
public class RuntimeSettingProperties implements Serializable {

    /**
     * 限制登录失败次数, -1 表示无限制
     */
    private volatile Integer loginFailCount = -1;

    /**
     * 访问超时，单位:分钟
     */
    private volatile Integer accessValidity = 720;

    /**
     * 密码复杂度=f(x+y)，1-位数>=8, 2-包含数字，4-包含大小写字母，8-包含特殊字符
     * 可能取值范围：1,3,5,7,15几种安全点数，其中
     * 1，3，5对应密码安全级别为低级
     * 7对应密码安全级别为中等
     * 15对应安全级别为高
     */
    private volatile Integer passwordComplexity = 1;

    /**
     * 密码有效天数
     */
    private volatile Integer passwordValidity = 180;

}
