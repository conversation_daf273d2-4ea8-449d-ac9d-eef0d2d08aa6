package com.hxdi.common.core.properties;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;

/**
 * OBS对象存储
 */
@Getter
@Setter
@ConfigurationProperties(prefix = "obs")
@RefreshScope
public class ObsProperties {

    private String accessKey;
    private String secretKey;

    /**
     * 对象存储端点
     */
    private String endpoint;
    /**
     * 对象容器
     */
    private String bucketName;

    /**
     * 录制视频保存路径
     */
    private String recordVideoPath;
}
