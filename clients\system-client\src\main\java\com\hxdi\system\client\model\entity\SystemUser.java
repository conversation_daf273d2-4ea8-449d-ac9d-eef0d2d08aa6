package com.hxdi.system.client.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.hxdi.common.core.constants.CommonConstants;
import com.hxdi.common.core.mybatis.base.entity.AbstractEntity;
import com.hxdi.common.core.utils.JsonConverter;
import com.hxdi.common.core.utils.support.ICache;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * 系统用户-基础信息
 *
 * <AUTHOR>
 */
@Getter
@Setter
@TableName("system_user")
public class SystemUser extends AbstractEntity {
    private static final long serialVersionUID = -735161640894047414L;
    /**
     * 系统用户ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String userId;

    /**
     * 登陆名
     */
    @NotEmpty
    private String userName;

    /**
     * 用户类型:super-admin-超级管理员 ops-admin: 运营管理员，tenant-admin: 租户管理员，admin-普通管理员 normal-普通用户, third-party:第三方用户
     */
    private String userType;

    /**
     * 所属组织
     */
    @NotEmpty
    private String organId;
    private String organCode;

    /**
     * 昵称
     */
    @NotEmpty
    private String nickName;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 手机号
     */
    @NotEmpty
    private String mobile;

    private Date birth;

    /**
     * 描述
     */
    private String remark;

    /**
     * 状态:0-删除 1-正常 2-锁定
     */
    private Integer status;

    /**
     * 创建人id
     */
    @TableField(fill = FieldFill.INSERT)
    private String createId;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateId;

    private String tenantId;


    /**
     * -------------------------------------
     */
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    @TableField(exist = false)
    private String password;

    @TableField(exist = false)
    private String organName;

    @TableField(exist = false)
    private String roleId;
    @TableField(exist = false)
    private String roleName;

    /**
     * 数据角色
     */
    @TableField(exist = false)
    private String dataRoleId;
    @TableField(exist = false)
    private String dataRoleName;


    public String selectOrganName(ICache cache) {
        Optional<SystemOrgan> organ = cache.selectForHash(CommonConstants.ORGAN_INFOS, this.organId);
        if (organ.isPresent()) {
            this.organName = organ.get().getName();
        }

        return this.organName;
    }
}
