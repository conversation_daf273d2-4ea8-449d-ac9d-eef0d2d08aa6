package com.hxdi.common.core.exception;

import com.hxdi.common.core.constants.ErrorCode;

/**
 * 基础错误异常
 *
 * <AUTHOR>
 */
public class BaseException extends RuntimeException {

    private static final long serialVersionUID = 3655050728585279326L;

    private int code = ErrorCode.FAIL.getCode();

    public BaseException() {

    }

    public BaseException(String msg) {
        super(msg);
    }

    public BaseException(int code, String msg) {
        super(msg);
        this.code = code;
    }

    public BaseException(ErrorCode errorCode) {
        this(errorCode.getCode(), errorCode.getMessage());
    }

    public BaseException(String msg, Throwable cause) {
        super(msg, cause);
    }

    public BaseException(int code, String msg, Throwable cause) {
        super(msg, cause);
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }


}
