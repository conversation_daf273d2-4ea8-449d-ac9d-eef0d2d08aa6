<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<html>
<head>
    <meta charset="UTF-8"/>
    <meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no"/>
    <title>开放平台-第三方授权</title>
    <link rel="stylesheet" type="text/css" href="../static/bootstrap/css/bootstrap.min.css" th:href="@{/static/bootstrap/css/bootstrap.min.css}"/>
    <script type="text/javascript" charset="utf-8" src="../static/bootstrap/js/bootstrap.min.js" th:src="@{/static/bootstrap/js/bootstrap.min.js}"></script>
</head>

<body>
    <nav class="navbar navbar-default container-fluid">
        <div class="container">
            <div class="navbar-header">
                <a class="navbar-brand" href="#">开放平台第三方授权</a>
            </div>
            <div class="collapse navbar-collapse" id="bs-example-navbar-collapse-5">
                <p class="navbar-text navbar-right">
                    <a href="#" th:href="@{/login}">登陆</a>
                    <a href="#">授权管理</a>
                    <a href="#">申请接入</a>
                </p>
            </div>
        </div>
    </nav>
    <div style="padding-top: 30px;width: 300px; color: #555; margin:0px auto;">
        <form id='confirmationForm' name='confirmationForm' th:action="@{/oauth/authorize}" method='post'>
        <input name='user_oauth_approval' value='true' type='hidden' />
        <p><a th:href="${app.website}" target="_blank" th:text="${app.appName}">应用</a> 将获得以下权限：</p>
        <ul class="list-group" >
            <li class="list-group-item"> <span >
                <input type="hidden"  th:name="${s.key}" value="true"  th:each="s : ${scopes}" />
                <input type="checkbox" disabled checked="checked"  th:each="s : ${scopes}" /> <label th:text="#{${s.key}}"  th:each="s : ${scopes}"></label> </span></li>
        </ul>
            <p class="help-block">授权后表明你已同意 服务协议</p>
           <button  class="btn btn-success pull-right" type="submit" id="write-email-btn">授权</button></p>
        </form>
    </div>
</body>
</html>
