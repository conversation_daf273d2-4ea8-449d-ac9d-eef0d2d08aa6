package com.hxdi.file.util;

import lombok.extern.slf4j.Slf4j;
import net.coobird.thumbnailator.Thumbnails;
import org.apache.commons.io.IOUtils;

import javax.imageio.ImageIO;
import java.awt.*;
import java.io.*;

/**
 *
 * 图片处理
 *
 * <AUTHOR>
 */
@Slf4j
public class ImageProcessor {

    private static final int DEFAULT_THUMBNAILS_WIDTH = 400;
    private static final int DEFAULT_THUMBNAILS_HEIGHT = 400;


    private Image image;
    private int width;
    private int height;

    private ImageProcessor(){}

    public static ImageProcessor create(){
        return new ImageProcessor();
    }

    /**
     * 按照宽度还是高度进行压缩
     * @param w int 最大宽度
     * @param h int 最大高度
     * @param is
     */
    public byte[] createThumbnails(int w, int h, InputStream is) {

        if (w == 0 || h == 0){
            w = DEFAULT_THUMBNAILS_WIDTH;
            h = DEFAULT_THUMBNAILS_HEIGHT;
        }

        try {
            byte[] bytes = IOUtils.toByteArray(is);
            // 构造Image对象
            image = ImageIO.read(new ByteArrayInputStream(bytes));
            // 得到源图宽
            width = image.getWidth(null);
            // 得到源图长
            height = image.getHeight(null);
            if (width >= height) {
                return resizeByWidth(w, new ByteArrayInputStream(bytes));
            } else {
                return resizeByHeight(h,new ByteArrayInputStream(bytes));
            }
        } catch (Exception e) {
            log.error("生成缩略图异常", e);
        }

        return null;
    }

    /**
     * 按照比例缩放
     * @param scale
     * @param is
     * @param savePath
     */
    public byte[] createThumbnails(double scale, InputStream is, String savePath) {
        try {
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            Thumbnails.of(is).scale(scale).toOutputStream(bos);
            return bos.toByteArray();
        } catch (Exception e) {
            log.error("生成缩略图异常", e);
        }

        return null;
    }

    /**
     * 以宽度为基准，等比例放缩图片
     * @param w int 新宽度
     */
    private byte[] resizeByWidth(int w, InputStream fis) throws IOException {
        int h =  w < width ? (int)(1.0f * w / width * height) : height;
        w = w < width ? w : width;
        return resize(w, h, fis);
    }

    /**
     * 以高度为基准，等比例缩放图片
     * @param h int 新高度
     */
    private byte[] resizeByHeight(int h, InputStream fis) throws IOException {
        int w = h < height ? (int)(1.0f * h / height * width) : width;
        h = h < height ? h : height;
        return resize(w, h, fis);
    }

    /**
     * 强制压缩/放大图片到固定的大小
     * @param fis 原图输入流
     */
    private byte[] resize(int w, int h, InputStream fis) throws IOException {
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        Thumbnails.of(fis).size(w,h).toOutputStream(bos);
        return bos.toByteArray();
    }

    public static void main(String[] args) throws Exception {
        ImageProcessor.create().createThumbnails(0, 0, new FileInputStream("/Users/<USER>/Desktop/5b98b566cf791.jpg"));
    }
}
