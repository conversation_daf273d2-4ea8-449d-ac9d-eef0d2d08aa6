package com.hxdi.obs.configuration;

import com.hxdi.common.core.properties.ObsProperties;
import com.hxdi.obs.component.FssTemplate;
import com.hxdi.obs.util.AbstractFileOperator;
import com.hxdi.obs.util.ObsUtil;
import com.obs.services.ObsClient;
import com.obs.services.ObsConfiguration;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * <AUTHOR>
 * @date 2023/2/24 10:59
 * @description 文件服务配置
 * @version 1.0
 */
@Configuration
public class ObsAutoConfiguration {

    @Autowired
    private ObsProperties obsProperties;

    @Bean
    public ObsClient obsClient() {
        ObsConfiguration config = new ObsConfiguration();
        config.setEndPoint(obsProperties.getEndpoint());
        config.setSocketTimeout(60000);
        config.setConnectionTimeout(12000);
        config.setMaxErrorRetry(1);
        // 设置下载缓冲1MB
        config.setReadBufferSize(1024 * 1024);
        config.setWriteBufferSize(1024 * 1024);

        ObsClient obsClient = new ObsClient(obsProperties.getAccessKey(), obsProperties.getSecretKey(), config);
        return obsClient;
    }

    @Bean
    public ObsUtil obsUtil(ObsClient obsClient) {
        return new ObsUtil(obsClient, multipartAsyncTaskExecutor(), obsProperties.getBucketName());
    }

    @Bean
    public FssTemplate fssTemplate(AbstractFileOperator fileOperator) {
        return new FssTemplate(fileOperator);
    }

    @Bean("multipartAsyncTaskExecutor")
    public Executor multipartAsyncTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(20);
        executor.setMaxPoolSize(32);
        executor.setQueueCapacity(100);
        executor.setKeepAliveSeconds(60);
        executor.setThreadNamePrefix("multipart-async-task-");
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(60);
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        return executor;
    }
}
