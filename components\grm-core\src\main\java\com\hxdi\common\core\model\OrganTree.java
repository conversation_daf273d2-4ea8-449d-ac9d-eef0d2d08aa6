package com.hxdi.common.core.model;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class OrganTree implements ITree<String, OrganTree> {

    private static final long serialVersionUID = -459490118976718615L;
    private String id;
    private String code;
    private String title;
    private Integer type;
    private String parentId;
    private String tenantId;
    private Integer level;
    private Integer seq;
    private String pk;
    private List<OrganTree> children;
}
