package com.hxdi.common.core.model;

/**
 * 数据权限字段KEY，用于从上下文中获取对应的值
 */
public enum ColumnKey implements IEnum<String>{

    /**
     * 手机号
     */
    MOBILE("mobile"),
    /**
     * 父级组织ID
     */
    PID("pid"),

    /**
     * 组织ID
     */
    ORG_ID("organId"),

    /**
     * 父级组织ID和当前组织ID
     * 仅当用户是库级用户时，才能授予父级权限
     */
    PID_ORGID("pid_orgId"),

    /**
     * 用户ID
     */
    USER_ID("userId"),

    NONE("");

    private String code;

    ColumnKey(String code){
        this.code = code;
    }

    @Override
    public String value() {
        return code;
    }

    public static ColumnKey parse(String code) {
        return IEnum.innerParse(code, values());
    }
}
