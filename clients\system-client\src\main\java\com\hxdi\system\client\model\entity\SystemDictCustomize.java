package com.hxdi.system.client.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hxdi.common.core.mybatis.base.entity.AbstractEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * @author: LJY
 * @description:
 * @since: 11:12 2023/2/16
 */
@Getter
@Setter
@ToString
@TableName("system_dict_customize")
public class SystemDictCustomize extends AbstractEntity {
    private static final long serialVersionUID=1L;

    @TableId(type = IdType.ASSIGN_ID)
    private String dictId;
    /**
     * '字典类型：01-字典项，02-字典项值'
     */
    private String dictType;
    /**
     * '字典项'
     */
    private String itemKey;
    /**
     * '父节点'
     */
    private String parentId;
    /**
     * '字典项值'
     */
    private String itemValue;
    /**
     * '文本值'
     */
    private String text;
    /**
     * '有效状态  0无效 1 有效'
     */
    private Integer status;
    /**
     * '排序'
     */
    private Integer seq;
    /**
     * default '0'
     */
    private String tenantId;

    @TableField(exist = false)
    private List<SystemDictCustomize> list;
}
