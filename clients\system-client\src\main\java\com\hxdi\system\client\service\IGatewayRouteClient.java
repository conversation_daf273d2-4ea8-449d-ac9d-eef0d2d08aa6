package com.hxdi.system.client.service;

import com.hxdi.common.core.model.ResultBody;
import com.hxdi.system.client.model.entity.GatewayRoute;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 */
public interface IGatewayRouteClient {

    /**
     * 获取路由详情
     *
     * @param routeId
     * @return
     */
    @GetMapping("/gateway/route/info")
    ResultBody<GatewayRoute> get(@RequestParam("routeId") String routeId);

}
