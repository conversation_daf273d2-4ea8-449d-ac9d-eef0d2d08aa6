<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.hxdi</groupId>
        <artifactId>services</artifactId>
        <version>1.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <packaging>jar</packaging>
    <artifactId>msg-service</artifactId>
    <version>1.0.0</version>
    <description>消息服务器</description>
    <dependencies>
        <dependency>
            <groupId>com.hxdi</groupId>
            <artifactId>grm-core</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>hx-common-data-starter</artifactId>
                    <groupId>com.hx.arch</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.hxdi</groupId>
            <artifactId>grm-common-starter</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.hxdi</groupId>
            <artifactId>grm-cloud-starter</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.hxdi</groupId>
            <artifactId>grm-database-starter</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.hxdi</groupId>
            <artifactId>msg-client</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-mail</artifactId>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>dysmsapi20170525</artifactId>
            <version>2.0.4</version>
        </dependency>
        <dependency>
            <groupId>cn.emay</groupId>
            <artifactId>emay-sms-interface</artifactId>
            <version>1.0.1</version>
        </dependency>
        <dependency>
            <groupId>org.freemarker</groupId>
            <artifactId>freemarker</artifactId>
        </dependency>
    </dependencies>

    <profiles>
        <!-- 开发环境 -->
        <profile>
            <id>dev</id>
            <properties>
                <package.environment>dev</package.environment>
                <tag>SNAPSHOT</tag>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <!-- 测试环境 -->
        <profile>
            <id>test</id>
            <properties>
                <package.environment>test</package.environment>
                <tag>ALPHA</tag>
            </properties>
        </profile>
        <profile>
            <id>uat</id>
            <properties>
                <package.environment>uat</package.environment>
                <tag>BETA</tag>
            </properties>
        </profile>

        <!-- 生产环境 -->
        <profile>
            <id>release</id>
            <properties>
                <package.environment>release</package.environment>
                <tag>RELEASE</tag>
            </properties>
        </profile>
        <!-- 本地环境 -->
        <profile>
            <id>local</id>
            <properties>
                <package.environment>local</package.environment>
                <tag>SNAPSHOT</tag>
            </properties>
        </profile>
    </profiles>

    <build>
        <finalName>${artifactId}-${version}-${tag}</finalName>
        <filters>
            <filter>src/main/package/${package.environment}/app.properties</filter>
        </filters>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
            <resource>
                <directory>src/main/package/${package.environment}</directory>
                <filtering>false</filtering>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <!-- docker打包插件 -->
            <plugin>
                <groupId>com.spotify</groupId>
                <artifactId>dockerfile-maven-plugin</artifactId>
                <configuration>
                    <repository>${docker.image.prefix}/${project.artifactId}</repository>
                    <tag>${project.version}</tag>
                    <buildArgs>
                        <JAR_FILE>target/${project.build.finalName}.jar</JAR_FILE>
                    </buildArgs>
                </configuration>
            </plugin>

            <!--maven deploy 忽略发布插件-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>false</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
