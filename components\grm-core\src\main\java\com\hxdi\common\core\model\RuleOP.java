package com.hxdi.common.core.model;

/**
 * 条件表达式符号
 */
public enum RuleOP implements IEnum<String>{
    EQ("eq", " = "),
    GT("gt", " > "),
    GE("ge", " >= "),
    LT("lt", " < "),
    LE("le", " <= "),
    IN("in", " in(%s) "),
    NOT_IN("not_in", " not in(%s) "),
    LIKE("like", " like concat('%%', '%s', '%%') "),
    R_LIKE("r_like", " like concat('%s', '%%') "),
    L_LIKE("l_like", " like concat('%%', '%s') "),
    ;


    private String value;
    private String exp;

    RuleOP(String value, String exp){
        this.value = value;
        this.exp = exp;
    }

    @Override
    public String value() {
        return value;
    }

    public String exp(){
        return exp;
    }

    public static RuleOP parse(String value) {
        return IEnum.innerParse(value, values());
    }
}
