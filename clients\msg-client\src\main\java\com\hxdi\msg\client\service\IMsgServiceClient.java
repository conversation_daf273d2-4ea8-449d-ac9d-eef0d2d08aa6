package com.hxdi.msg.client.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.msg.client.model.entity.MsgInfo;
import com.hxdi.msg.client.model.vo.ClassifyMessageNumber;
import com.hxdi.msg.client.model.vo.MsgTempateSend;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * @program: hxdicloud_2.0
 * @description: 消息服务接口
 * @author: flying
 * @create: 2025-06-30 13:50
 */
public interface IMsgServiceClient {

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @GetMapping("/message/info/selectOne")
    @ApiOperation("根据ID查找数据")
    ResultBody<MsgInfo> selectOne(Integer id);

    /**
     * 发送消息
     *
     * @param msgInfo
     * @return
     */
    @PostMapping("/message/info/add")
    @ApiOperation("发送消息")
    ResultBody<Void> saveMessage(@RequestBody MsgInfo msgInfo);

    /**
     * 修改消息
     *
     * @param msgInfo
     * @return
     */
    @PostMapping("/message/info/update")
    @ApiOperation("修改消息(注意：只有没有发送的消息才能被更新)")
    ResultBody<Void> updateMessage(@RequestBody MsgInfo msgInfo);

    /**
     * 删除消息
     *
     * @param id
     * @return
     */
    @GetMapping("/message/info/delete")
    @ApiOperation("删除消息")
    ResultBody<Void> deleteMessage(@RequestParam(value = "id") String id);

    /**
     * 获取用户发送的所有消息列表
     *
     * @param userId
     * @param title
     * @param state
     * @param alermType
     * @param isAdmin
     * @return
     */
    @GetMapping("/message/info/allSend")
    @ApiOperation("获取发送的所有消息")
    ResultBody<Page<MsgInfo>> getSendMessages(@RequestParam(value = "userId") String userId,
                                              @RequestParam(value = "title", required = false) String title,
                                              @RequestParam(value = "state", required = false) String state,
                                              @RequestParam(value = "alermType", required = false) String alermType,
                                              @RequestParam(value = "isAdmin", required = false, defaultValue = "false") Boolean isAdmin,
                                              @RequestParam(value = "page", defaultValue = "1", required = false) Integer page,
                                              @RequestParam(value = "limit", defaultValue = "10", required = false) Integer limit);

    /**
     * 获取用户接收的未读消息分页
     *
     * @param userId
     * @param alermType
     * @param page
     * @param size
     * @return
     */
    @GetMapping("/message/info/unread")
    @ApiOperation("获取未读消息分页")
    ResultBody<Page<MsgInfo>> getUnreadMessages(@RequestParam(value = "userId") String userId,
                                                @RequestParam(value = "alermType", required = false) String alermType,
                                                @RequestParam(value = "page", defaultValue = "1", required = false) Integer page,
                                                @RequestParam(value = "size", defaultValue = "10", required = false) Integer size);

    /**
     * 获取用户接收的未读消息列表
     *
     * @param userId
     * @param alermType
     * @return
     */
    @GetMapping("/message/info/unreadList")
    @ApiOperation("获取未读消息列表")
    ResultBody<List<MsgInfo>> getUnreadMessagesList(@RequestParam(value = "userId") String userId,
                                                    @RequestParam(value = "alermType", required = false) String alermType);


    /**
     * 获取用户接收的所有消息列表
     *
     * @param userId
     * @param title
     * @param publisher
     * @param isRead
     * @param alermType
     * @param page
     * @param limit
     * @return
     */
    @GetMapping("/message/info/all")
    @ApiOperation("获取收到的所有消息")
    ResultBody<Page<MsgInfo>> getUserMessages(@RequestParam(value = "userId") String userId,
                                              @RequestParam(value = "title", required = false) String title,
                                              @RequestParam(value = "publisher", required = false) String publisher,
                                              @RequestParam(value = "isRead", required = false) String isRead,
                                              @RequestParam(value = "alermType", required = false) String alermType,
                                              @RequestParam(value = "page", defaultValue = "1", required = false) Integer page,
                                              @RequestParam(value = "limit", defaultValue = "10", required = false) Integer limit);

    /**
     * 获取分类的消息数量
     *
     * @param userId
     * @return
     */
    @GetMapping("/message/info/classifyNumber")
    @ApiOperation("按分类获取未读的消息数量")
    ResultBody<ClassifyMessageNumber> getClassifyMessageNumber(@RequestParam(value = "userId") String userId);

    //----------------------MsgReadMark-----------------------

    /**
     * 标记消息为已读
     *
     * @param msgId
     * @param userId
     * @return
     */
    @GetMapping("/message/info/markAsRead")
    @ApiOperation("标记消息为已读")
    ResultBody<Void> markAsRead(@RequestParam(value = "msgId") String msgId,
                                @RequestParam(value = "userId") String userId);

    /**
     * 批量标记消息为已读
     *
     * @param msgIds
     * @param userId
     * @return
     */
    @GetMapping("/message/info/markBatchAsRead")
    @ApiOperation("批量标记消息为已读")
    ResultBody<Void> markBatchAsRead(@RequestParam(value = "msgIds", required = false) String msgIds,
                                     @RequestParam(value = "alermType", required = false) String alermType,
                                     @RequestParam(value = "userId") String userId);

    /**
     * 批量标记消息为已读（用于自动已读操作）
     *
     * @param msgIds
     * @param userId
     * @return
     */
    @GetMapping("/message/info/markBatchAsReadV2")
    @ApiOperation("批量标记消息为已读（用于自动已读操作）")
    ResultBody<Void> markBatchAsReadV2(@RequestParam(value = "msgIds") String msgIds,
                                       @RequestParam(value = "userId") List<String> userId);


    //----------------------MsgTemplate-----------------------
    //--- 通过模版发送消息(具体发送时间及方式由所选模版决定) --

    @PostMapping("/message/info/sendMessageByTemplate")
    @ApiOperation("通过模版发送消息(为业务系统提供远程调用发送消息接口)")
    ResultBody<Void> sendMessageByTemplate(@RequestBody MsgTempateSend send);
}
