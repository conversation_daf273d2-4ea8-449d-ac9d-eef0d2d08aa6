package com.hxdi.file.controller;

import com.hxdi.database.properties.DatabaseProperties;
import com.hxdi.file.service.impl.ScottServiceImpl;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2023/2/22 16:35
 * @description 文件资源管理
 * @version 1.0
 */
@Api(tags = "租户测试")
//@RestController
//@RequestMapping("/test/tenant")
public class TestTenantController {

    @Autowired
    private ScottServiceImpl scottService;

    @Autowired
    private DatabaseProperties databaseProperties;

//    @ApiOperation("分页查询1")
//    @GetMapping("/scott/page")
//    public ResultBody<Page<Scott>> page(Integer limit, Integer page) {
//        return ResultBody.ok().data(scottService.page(limit, page));
//    }
//
//    @ApiOperation("查询详情")
//    @GetMapping("/scott/detail")
//    public ResultBody<Scott> get(String id) {
//        return ResultBody.ok().data(scottService.get(id));
//    }
//
//    @ApiOperation("查询列表")
//    @GetMapping("/scott/list")
//    public ResultBody<List<Scott>> list() {
//        return ResultBody.ok().data(scottService.lists());
//    }
//
//    @ApiOperation("查询列表-忽略")
//    @GetMapping("/scott/list2")
//    public ResultBody<List<Scott>> list2() {
//        return ResultBody.ok().data(scottService.lists2());
//    }
//
//    @ApiOperation("删除")
//    @DeleteMapping("/scott/delete")
//    public ResultBody del(String id) {
//        scottService.delete(id);
//        return ResultBody.ok();
//    }
//
//    @ApiOperation("删除-by租户")
//    @DeleteMapping("/scott/delete2")
//    public ResultBody del2(String id) {
//        scottService.delete2(id);
//        return ResultBody.ok();
//    }
//
//    @ApiOperation("创建")
//    @PostMapping("/scott/create")
//    public ResultBody create() {
//        Scott scott = new Scott();
//        scott.setName(RandomUtil.randomString(6));
//        scott.setAge(RandomUtil.randomInt(20));
//        scottService.create(scott);
//        return ResultBody.ok();
//    }
//
//    @ApiOperation("更新")
//    @PostMapping("/scott/update")
//    public ResultBody create(String id) {
//        scottService.update(id);
//        return ResultBody.ok();
//    }
//
//    @ApiOperation("创建-by租户")
//    @PostMapping("/scott/create2")
//    public ResultBody create2() {
//        Scott scott = new Scott();
//        scott.setName(RandomUtil.randomString(6));
//        scott.setAge(RandomUtil.randomInt(20));
//        scottService.create2(scott);
//        return ResultBody.ok();
//    }

}
