<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdicloud.msg.mapper.MsgSmsLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hxdi.msg.client.model.MsgSmsLog">
        <id column="id" property="id" />
        <result column="buz_id" property="buzId" />
        <result column="tpl_id" property="tplId" />
        <result column="发送内容" property="content" />
        <result column="creata_time" property="creataTime" />
        <result column="receve_name" property="receveName" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, buz_id, tpl_id, 发送内容, creata_time, receve_name
    </sql>

</mapper>
