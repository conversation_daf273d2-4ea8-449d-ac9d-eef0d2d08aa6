package com.hxdi.common.core.utils.codec;


public class RsaEncryptUtil {


    public final static String PUBLIC_KEY_KEYSTORE = "publicKey.keystore";
    public final static String PRIVATE_KEY_KEYSTORE = "privateKey.keystore";


    public static String getEncryptCode(String origins) throws Exception {
        byte[] cipherData = NetRsaEnccrypt.encrypt(NetRsaEnccrypt.loadPublicKeyByStr(NetRsaEnccrypt.loadPublicKeyByFile()), origins.getBytes());
        String cipher = BaseRsa64.encode(cipherData);
        return cipher;
    }


    public static String getDecryptCode(String decryptCode) throws Exception {
        //公钥加密过程
        byte[] res = NetRsaEnccrypt.decrypt(NetRsaEnccrypt.loadPrivateKeyByStr(NetRsaEnccrypt.loadPrivateKeyByFile()), BaseRsa64.decode(decryptCode));
        return new String(res);
    }

    public static void main(String[] args) throws Exception {
        //产生密钥
        NetRsaEnccrypt.genKeyPair();
        String userName = "123456";
        String encryptCode = RsaEncryptUtil.getEncryptCode(userName);
        System.out.println(encryptCode);
        String decryptCode = RsaEncryptUtil.getDecryptCode(encryptCode);
        System.out.println(decryptCode);
    }

}
