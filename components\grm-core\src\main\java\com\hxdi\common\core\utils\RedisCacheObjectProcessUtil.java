package com.hxdi.common.core.utils;

import com.hxdi.common.core.constants.CommonConstants;
import com.hxdi.common.core.model.cache.CodeTable;
import com.hxdi.common.core.utils.support.ICache;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/3/9 8:11 PM
 * @description redis 缓存对象处理工具类
 * 包含：数据字典、组织、用户（其他可扩展）
 */
public class RedisCacheObjectProcessUtil implements ICache {

    private RedisUtil redisUtil;

    public RedisCacheObjectProcessUtil(RedisUtil redisUtil) {
        this.redisUtil = redisUtil;
    }

    public String getDictName(String itemKey, String itemValue) {
        List tables = (List) redisUtil.hget(CommonConstants.CODE_TABLE, itemKey);
        for (int i = 0; i < tables.size(); i++) {
            CodeTable cacheDict = (CodeTable) tables.get(i);
            if (CommonUtils.equals(cacheDict.getCode(), itemValue)) {
                return cacheDict.getName();
            }
        }

        return null;
    }


    public List<CodeTable> getDict(String itemKey) {
        List<? super CodeTable> tables = Optional.ofNullable((List) redisUtil.hget(CommonConstants.CODE_TABLE, itemKey)).orElseGet(Collections::emptyList);
        return tables.stream().map(table -> {
            CodeTable cacheDict = (CodeTable) table;
            return cacheDict;
        }).collect(Collectors.toList());
    }

    public <R> Optional<R> getUser(String userId) {
        return Optional.ofNullable((R) redisUtil.hget(CommonConstants.USER_INFOS, userId));
    }

    public <R> Optional<R> getOrgan(String organId) {
        return Optional.ofNullable((R) redisUtil.hget(CommonConstants.ORGAN_INFOS, organId));
    }

    public <R> R getAllOrgan() {
        Map map = redisUtil.getMap(CommonConstants.ORGAN_INFOS);
        return (R) map.values().stream().collect(Collectors.toList());
    }

    @Override
    public <R> Optional<R> selectForHash(String key, String item) {
        return CommonUtils.isEmpty(item) ? Optional.empty() : Optional.ofNullable((R) redisUtil.hget(key, item));
    }
}
