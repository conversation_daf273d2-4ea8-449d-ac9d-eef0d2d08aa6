package com.hxdi.system.client.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 系统消息中间表
 */
@Getter
@Setter
@TableName("system_msg_template_user")
public class SystemMsgTemplateUser implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 消息模板id
     */
    private String templateId;

    /**
     * 接收方类型  组织 - ORGAN  角色 - ROLE  用户 - USER  所有人 - ALL
     */
    private String receiveType;

    /**
     * 接收方id
     */
    private String receiverId;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    public Date createTime;

    public SystemMsgTemplateUser(String templateId, String receiveType, String receiverId, Date createTime) {
        this.templateId = templateId;
        this.receiveType = receiveType;
        this.receiverId = receiverId;
        this.createTime = createTime;
    }
}
