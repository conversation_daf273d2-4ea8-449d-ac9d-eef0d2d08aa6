package com.hxdi.msg.controller;

import com.hxdi.common.core.model.ResultBody;
import com.hxdi.msg.client.model.SmsMessage;
import com.hxdi.msg.dispatcher.MessageDispatcher;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Api(tags = "短信")
public class SmsController  {


    @Autowired
    private MessageDispatcher dispatcher;

    @ApiOperation("发送短信")
    @PostMapping("/sms/send")
    public ResultBody<String> send(SmsMessage smsMessage) {
        this.dispatcher.dispatch(smsMessage);
        return ResultBody.ok();
    }


}
