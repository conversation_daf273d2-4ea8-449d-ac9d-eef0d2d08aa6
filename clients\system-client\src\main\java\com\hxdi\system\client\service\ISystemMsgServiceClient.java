package com.hxdi.system.client.service;

import com.hxdi.common.core.model.ResultBody;
import com.hxdi.system.client.model.entity.SystemMsgTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

public interface ISystemMsgServiceClient {

    /**
     * 查询单条
     * @param id
     * @return
     */
    @GetMapping("/systemMsg/template/getById")
    ResultBody<SystemMsgTemplate> getById(@RequestParam("id") String id);

    /**
     * 新增系统消息
     * @param systemMsgTemplate  expectSendTime == null 立即发送
     * @return
     */
    @PostMapping("/systemMsg/template/add")
    ResultBody add(@RequestBody SystemMsgTemplate systemMsgTemplate);

}
