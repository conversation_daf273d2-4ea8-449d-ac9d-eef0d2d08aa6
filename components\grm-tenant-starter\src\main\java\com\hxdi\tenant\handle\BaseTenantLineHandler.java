package com.hxdi.tenant.handle;

import com.baomidou.mybatisplus.extension.plugins.handler.TenantLineHandler;
import com.hxdi.common.core.model.PermColumn;
import com.hxdi.common.core.security.SecurityHelper;
import com.hxdi.database.properties.DatabaseProperties;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.StringValue;
import net.sf.jsqlparser.schema.Column;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/13 16:41
 * @description 租户插件辅助处理类
 * @version 1.0
 */
public class BaseTenantLineHandler implements TenantLineHandler {

    private DatabaseProperties databaseProperties;

    public BaseTenantLineHandler(DatabaseProperties databaseProperties) {
        this.databaseProperties = databaseProperties;
    }

    @Override
    public Expression getTenantId() {
        return new StringValue(SecurityHelper.obtainTenantId().get());
    }

    @Override
    public String getTenantIdColumn() {
        return PermColumn.TENANT_COLUMN.value();
    }

    @Override
    public boolean ignoreTable(String tableName) {
        return !databaseProperties.getIgnoreTables().isEmpty() && databaseProperties.getIgnoreTables().contains(tableName);
    }

    @Override
    public boolean ignoreInsert(List<Column> columns, String tenantIdColumn) {
        return TenantLineHandler.super.ignoreInsert(columns, tenantIdColumn);
    }

    /**
     * 租户ID为空，则忽略对Sql处理
     * @return
     */
    public boolean ignoreTenantIdIsNull(){
        return !SecurityHelper.obtainTenantId().isPresent();
    }
}
