<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.hxdi</groupId>
        <artifactId>components</artifactId>
        <version>1.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>grm-tenant-starter</artifactId>
    <version>1.0.0</version>
    <description>多租户组件</description>
    <packaging>jar</packaging>
    <dependencies>
        <dependency>
            <groupId>com.hxdi</groupId>
            <artifactId>grm-database-starter</artifactId>
            <version>1.0.0</version>
            <optional>true</optional>
        </dependency>
        <!--自定义配置处理器,配置项提示-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
    </dependencies>
</project>
