package com.hxdi.system.client.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Getter
@Setter
@TableName("statistics_user")
public class StatisticsUser implements Serializable {

    private static final long serialVersionUID = -4843601530540252706L;

    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    private String userId;

    private LocalDateTime logonTime;

    private LocalDateTime logoutTime;

    /**
     * 登录次数
     */
    private volatile Integer logonCount;

    /**
     * 在线时长
     */
    private Long onlineTime;

    /**
     * 1-在线，0-离线
     */
    private volatile Integer status;

    private LocalDate dayOfMonth;

    @TableField(exist = false)
    private LocalDateTime requestTime;

}
