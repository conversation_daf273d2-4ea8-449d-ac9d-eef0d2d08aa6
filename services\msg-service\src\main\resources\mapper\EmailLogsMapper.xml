<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdicloud.msg.mapper.EmailLogsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hxdi.msg.client.model.entity.EmailLogs">
        <id column="log_id" property="logId" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="subject" property="subject" />
        <result column="send_to" property="sendTo" />
        <result column="send_cc" property="sendCc" />
        <result column="content" property="content" />
        <result column="attachments" property="attachments" />
        <result column="send_nums" property="sendNums" />
        <result column="error" property="error" />
        <result column="result" property="result" />
        <result column="config" property="config" />
        <result column="tpl_code" property="tplCode" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        log_id, subject, send_to, send_cc, content, attachments, send_nums, error, result, config, tpl_code, create_time, update_time
    </sql>
</mapper>
