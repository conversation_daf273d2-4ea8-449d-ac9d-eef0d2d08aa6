<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.hxdi</groupId>
        <artifactId>services</artifactId>
        <version>1.0.0</version>
    </parent>
    <artifactId>admin-service</artifactId>
    <version>1.0.0</version>
    <packaging>jar</packaging>
    <description>认证授权服务</description>

    <dependencies>
        <dependency>
            <groupId>com.hxdi</groupId>
            <artifactId>grm-core</artifactId>
            <version>1.0.0</version>
            <exclusions>
                <exclusion>
                    <artifactId>hx-common-data-starter</artifactId>
                    <groupId>com.hx.arch</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.hxdi</groupId>
            <artifactId>grm-cloud-starter</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.hxdi</groupId>
            <artifactId>grm-common-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hxdi</groupId>
            <artifactId>system-client</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.hxdi</groupId>
            <artifactId>msg-client</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-jdbc</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-thymeleaf</artifactId>
        </dependency>
        <dependency>
            <groupId>net.sourceforge.nekohtml</groupId>
            <artifactId>nekohtml</artifactId>
            <version>1.9.22</version>
        </dependency>
        <!-- 验证码组件 -->
        <dependency>
            <groupId>com.github.penggle</groupId>
            <artifactId>kaptcha</artifactId>
            <version>2.3.2</version>
        </dependency>

        <!-- 验证码组件 -->
<!--        <dependency>-->
<!--            <groupId>com.hx.arch</groupId>-->
<!--            <artifactId>hx-captcha-spring-boot-starter</artifactId>-->
<!--            <version>1.0.3</version>-->
<!--        </dependency>-->
    </dependencies>

    <profiles>
        <!-- 开发环境 -->
        <profile>
            <id>dev</id>
            <properties>
                <package.environment>dev</package.environment>
                <tag>SNAPSHOT</tag>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <!-- 测试环境 -->
        <profile>
            <id>test</id>
            <properties>
                <package.environment>test</package.environment>
                <tag>ALPHA</tag>
            </properties>
        </profile>
        <!-- 验收环境 -->
        <profile>
            <id>uat</id>
            <properties>
                <package.environment>uat</package.environment>
                <tag>BETA</tag>
            </properties>
        </profile>
        <!-- 生产环境 -->
        <profile>
            <id>release</id>
            <properties>
                <package.environment>release</package.environment>
                <tag>RELEASE</tag>
            </properties>
        </profile>
        <!-- 生产测试环境 -->
        <profile>
            <id>release/beta</id>
            <properties>
                <package.environment>release/beta</package.environment>
                <tag>BETA</tag>
            </properties>
        </profile>
        <!-- 本地环境 -->
        <profile>
            <id>local</id>
            <properties>
                <package.environment>local</package.environment>
                <tag>SNAPSHOT</tag>
            </properties>
        </profile>
    </profiles>

    <build>
        <finalName>${artifactId}-${version}-${tag}</finalName>
        <filters>
            <filter>src/main/package/${package.environment}/app.properties</filter>
        </filters>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
            <resource>
                <directory>src/main/package/${package.environment}</directory>
                <filtering>false</filtering>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <configuration>
                    <nonFilteredFileExtensions>
                        <nonFilteredFileExtension>woff</nonFilteredFileExtension>
                        <nonFilteredFileExtension>eot</nonFilteredFileExtension>
                        <nonFilteredFileExtension>ttf</nonFilteredFileExtension>
                        <nonFilteredFileExtension>svg</nonFilteredFileExtension>
                    </nonFilteredFileExtensions>
                </configuration>
            </plugin>

            <!-- docker打包插件 -->
            <plugin>
                <groupId>com.spotify</groupId>
                <artifactId>dockerfile-maven-plugin</artifactId>
                <configuration>
                    <repository>${docker.image.prefix}/${project.artifactId}</repository>
                    <tag>${project.version}</tag>
                    <buildArgs>
                        <JAR_FILE>target/${project.build.finalName}.jar</JAR_FILE>
                    </buildArgs>
                </configuration>
            </plugin>

            <!--maven deploy 忽略发布插件-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
