package com.hxdi.system.client.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hxdi.common.core.mybatis.base.entity.AbstractEntity;
import com.hxdi.system.client.constants.SystemConstants;
import lombok.*;

import java.util.Date;

/**
 * 系统用户-登录账号
 *
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@TableName("system_account")
public class SystemAccount extends AbstractEntity {

    @TableId(type = IdType.ASSIGN_ID)
    private String accountId;

    /**
     * 系统用户Id
     */
    private String userId;

    /**
     * 账号：手机号、邮箱、用户名、第三方账号ID（openId）
     */
    private String account;

    /**
     * 密码凭证
     */
    private String password;

    /**
     * 登录类型:username-密码、mobile-手机号、email-邮箱、wechat、qq、zfb
     */
    private String accountType;

    /**
     * 注册IP
     */
    private String registerIp;

    /**
     * 状态:0-删除 1-启用 2-锁定
     */
    private Integer status = SystemConstants.ACCOUNT_STATUS_NORMAL;

    /**
     * 密码过期时间
     */
    private Date credentialExpireTime;

    /**
     * 账号域
     */
    private String accDomain;

    private String tenantId;

    public SystemAccount(){}

    public SystemAccount(String userId, String account, String password, String accountType, String accDomain, String registerIp) {
        this.userId = userId;
        this.account = account;
        this.password = password;
        this.accountType = accountType;
        this.accDomain = accDomain;
        this.registerIp = registerIp;
    }


}
