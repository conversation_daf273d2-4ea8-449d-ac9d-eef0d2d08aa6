package com.hxdi.common.core.model;

/**
 * <AUTHOR>
 * @date 2023/9/18 14:30
 * @description 消息类型自定义
 * @version 1.0
 */
public enum MessageType implements IEnum<String>{

    ODX_API_PUSH_CONTRACT,
    ODX_API_PUSH_CONTRACT_TO_SUPERVISION,
    ODX_API_PUSH_CONTRACT_UPDATE_STATE_TO_SUPERVISION,
    ODX_API_PUSH_JTQY,
    ODX_API_PUSH_JGQY,
    ODX_API_PUSH_JGQY_TO_SUPERVISION,
    ODX_API_PUSH_PLAN_TO_SUPERVISION,
    ODX_API_PUSH_RECEIPT_TO_SUPERVISION,
    ODX_API_PUSH_CARINFO_TO_SUPERVISION,
    ODX_API_PUSH_CARINFOVIDEO_TO_SUPERVISION,
    ODX_API_PUSH_OUTSTOCK_UPDATE_STATE_TO_SUPERVISION,
    ODX_API_PUSH_OPERATION_LOG_TO_SUPERVISION,
    ODX_API_PUSH_CHECK_DATA_TO_SUPERVISION,
    ODX_API_PUSH_EVENT_DATA_TO_SUPERVISION,

    /**
     * 实时视频录制状态同步
     */
    ODX_RT_RECORD_CALLBACK,

    /**
     * 告警视频录制
     */
    VIDEO_REC_SAVE,
    /**
     * 手动视频录制
     */
    VIDEO_REC_SAVE_MANUAL,
    /**
     * 录制视频上传
     */
    VIDEO_REC_UPLOAD_INFO,
    ;


    @Override
    public String value() {
        return this.name();
    }

    public static MessageType parse(String type) {
        return IEnum.innerParse(type, values());
    }
}
