package com.hxdi.cloud.autoconfigure;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.*;
import com.fasterxml.jackson.databind.deser.std.DateDeserializers;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.databind.ser.std.DateSerializer;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalTimeSerializer;
import com.hxdi.common.core.constants.CommonConstants;
import com.hxdi.common.core.converter.StringToDateConverter;
import com.hxdi.common.core.converter.StringToLocalDateConverter;
import com.hxdi.common.core.converter.StringToLocalDateTimeConverter;
import com.hxdi.common.core.exception.BaseAccessDeniedHandler;
import com.hxdi.common.core.exception.BaseAuthenticationEntryPoint;
import com.hxdi.common.core.jackson.FastJsonSerializerFeatureCompatibleForJackson;
import com.hxdi.common.core.jackson.SerializerFeature;
import com.hxdi.common.core.utils.CommonUtils;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnWebApplication;
import org.springframework.cloud.client.loadbalancer.LoadBalanced;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.annotation.Order;
import org.springframework.format.FormatterRegistry;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.builders.WebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.util.StringUtils;
import org.springframework.validation.Validator;
import org.springframework.validation.beanvalidation.LocalValidatorFactoryBean;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.ViewControllerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.io.IOException;
import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.TimeZone;
import java.util.concurrent.atomic.AtomicReference;

/**
 * <AUTHOR>
 * @date 2023/2/3 16:45
 * @description
 * @version 1.0
 */
@ConditionalOnWebApplication
@Configuration
@Slf4j
public class DefaultWebAutoConfiguration implements WebMvcConfigurer {

    @LoadBalanced
    @Bean("lbRestTemplate")
    public RestTemplate restTemplate() {
        log.info("Define LoadBalanced 'restTemplate'");
        return new RestTemplate();
    }

    @Bean
    @Primary
    public ObjectMapper objectMapper(){
        ObjectMapper om = new ObjectMapper();
        // 添加这行，确保包含null值
        om.setSerializationInclusion(JsonInclude.Include.ALWAYS);

        // 排序key
        om.configure(SerializationFeature.ORDER_MAP_ENTRIES_BY_KEYS, true);
        //忽略空bean转json错误
        om.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
        //忽略在json字符串中存在，在java类中不存在字段，防止错误。
        om.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        om.configure(MapperFeature.DEFAULT_VIEW_INCLUSION, false);
        om.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);

        om.setTimeZone(TimeZone.getTimeZone(CommonConstants.TIME_ZONE));

        JavaTimeModule javaTimeModule = new JavaTimeModule();
        javaTimeModule.addSerializer(LocalDateTime.class,new LocalDateTimeSerializer(DateTimeFormatter.ofPattern(CommonConstants.DEFAULT_DATE_TIME_FORMAT)));
        javaTimeModule.addSerializer(LocalDate.class,new LocalDateSerializer(DateTimeFormatter.ofPattern(CommonConstants.DEFAULT_DATE_FORMAT)));
        javaTimeModule.addSerializer(LocalTime.class,new LocalTimeSerializer(DateTimeFormatter.ofPattern(CommonConstants.DEFAULT_TIME_FORMAT)));
        javaTimeModule.addDeserializer(LocalDateTime.class,new LocalDateTimeDeserializer(DateTimeFormatter.ofPattern(CommonConstants.DEFAULT_DATE_TIME_FORMAT)));
        javaTimeModule.addDeserializer(LocalDate.class,new LocalDateDeserializer(DateTimeFormatter.ofPattern(CommonConstants.DEFAULT_DATE_FORMAT)));
        javaTimeModule.addDeserializer(LocalTime.class,new LocalTimeDeserializer(DateTimeFormatter.ofPattern(CommonConstants.DEFAULT_TIME_FORMAT)));
        om.registerModule(javaTimeModule);

        log.info("Define ObjectMapper [{}]", om);
        return om;
    }

    @Override
    public void extendMessageConverters(List<HttpMessageConverter<?>> converters) {
        for (HttpMessageConverter<?> messageConverter : converters) {
            if (messageConverter instanceof  MappingJackson2HttpMessageConverter) {
                MappingJackson2HttpMessageConverter jackson2HttpMessageConverter= (MappingJackson2HttpMessageConverter) messageConverter;
                enhanceMessageConvertor(jackson2HttpMessageConverter);
                break;
            }
        }
    }

    /**
     * 增强Json序列化和反序列化操作
     * @param jackson2HttpMessageConverter
     */
    private void enhanceMessageConvertor(MappingJackson2HttpMessageConverter jackson2HttpMessageConverter) {
        ObjectMapper om = jackson2HttpMessageConverter.getObjectMapper();

        SimpleModule simpleModule = new SimpleModule();
        // 自定义序列化：long长度超过18位则认为是雪花ID，转为string，否则为数字
        simpleModule.addSerializer(Long.class, new JsonSerializer<Long>() {
            @Override
            public void serialize(Long value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
                String str = String.valueOf(value);
                if (str.length() >= 18) {
                    gen.writeString(str);
                }else {
                    gen.writeNumber(value);
                }
            }
        });

        // 自定义反序列化：BigDecimal
        simpleModule.addDeserializer(BigDecimal.class, new JsonDeserializer<BigDecimal>() {
            @Override
            public BigDecimal deserialize(JsonParser jsonParser, DeserializationContext deserializationContext) throws IOException {
                String value = jsonParser.getValueAsString();

                // 处理null或空字符串
                if (StringUtils.isEmpty(value) || "{}".equals(value.trim())) {
                    return null;
                }

                return new BigDecimal(value);
            }
        });

        simpleModule.addSerializer(Date.class, new DateSerializer(false, new SimpleDateFormat(CommonConstants.DEFAULT_DATE_TIME_FORMAT)));
        simpleModule.addDeserializer(Date.class, new DateDeserializers.DateDeserializer() {
            protected final DateFormat _customFormat = new SimpleDateFormat(CommonConstants.DEFAULT_DATE_TIME_FORMAT);
            protected final AtomicReference<DateFormat> _reusedCustomFormat = new AtomicReference<>();

            @SneakyThrows
            @Override
            public Date deserialize(JsonParser jsonParser, DeserializationContext ctxt) throws IOException {
                if (CommonUtils.isBlank(jsonParser.getText())) {
                    return null;
                }

                String text = jsonParser.getText().trim();
                DateFormat f = _reusedCustomFormat.getAndSet(null);
                if (f == null) {
                    f = (DateFormat) _customFormat.clone();
                }
                Date d = f.parse(text);
                _reusedCustomFormat.compareAndSet(null, f);
                return d;
            }
        });
        om.registerModule(simpleModule);

        // 兼容fastJson 的一些空值处理
        SerializerFeature[] features = new SerializerFeature[]{
                SerializerFeature.WriteNullListAsEmpty,
                // SerializerFeature.WriteNullStringAsEmpty,
                SerializerFeature.WriteNullBooleanAsFalse,
                SerializerFeature.WriteNullMapAsEmpty
        };
        om.setSerializerFactory(om.getSerializerFactory().withSerializerModifier(new FastJsonSerializerFeatureCompatibleForJackson(features)));
    }

    @Override
    public void addFormatters(FormatterRegistry registry) {
        registry.addConverter(StringToLocalDateConverter.INSTANCE);
        registry.addConverter(StringToLocalDateTimeConverter.INSTANCE);
        registry.addConverter(StringToDateConverter.INSTANCE);
    }

    @Override
    public Validator getValidator() {
//        ResourceBundleMessageSource messageSource = new ResourceBundleMessageSource();
//        messageSource.setDefaultEncoding("utf-8");// 读取配置文件的编码格式
//        messageSource.setCacheMillis(-1);// 缓存时间，-1表示不过期
//        messageSource.setBasename("ValidationMessages");// 配置文件前缀名，设置为Messages,那你的配置文件必须以Messages.properties/Message_en.properties...

        LocalValidatorFactoryBean factoryBean = new LocalValidatorFactoryBean();
//        MessageInterpolatorFactory interpolatorFactory = new MessageInterpolatorFactory();
//        factoryBean.setMessageInterpolator(interpolatorFactory.getObject());
//        factoryBean.setValidationMessageSource(messageSource);

        factoryBean.getValidationPropertyMap().put("hibernate.validator.fail_fast", "true");

        return factoryBean;
    }

    /**
     * 多个WebSecurityConfigurerAdapter
     */
    @Configuration
    @Order(101)
    public static class ApiWebSecurityConfiguration extends WebSecurityConfigurerAdapter {
        @Override
        public void configure(WebSecurity web) {
            web.ignoring().antMatchers(
                    "/error",
                    "/static/**",
                    "/v2/api-docs/**",
                    "/swagger-resources/**",
                    "/swagger-ui.html",
                    "/doc.html",
                    "/webjars/**",
                    "/favicon.ico");// 放开用户接口
        }

        /**
         * 默认安全配置
         * @param http
         * @throws Exception
         */
        @Override
        public void configure(HttpSecurity http) throws Exception {
            http.sessionManagement().sessionCreationPolicy(SessionCreationPolicy.IF_REQUIRED)
                    .and()
                    //为了统一异常处理。每个资源服务器都应该加上。
                    .exceptionHandling()
                    .accessDeniedHandler(new BaseAccessDeniedHandler())
                    .authenticationEntryPoint(new BaseAuthenticationEntryPoint())
                    .and()
                    .csrf().disable();
        }
    }


    /**
     * 静态资源处理器
     *
     * @param registry
     */
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        registry.addResourceHandler("/static/**").addResourceLocations("classpath:/static/");
        registry.addResourceHandler("/swagger-ui.html", "/doc.html", "/v2/**").addResourceLocations("classpath:/META-INF/resources/");
        registry.addResourceHandler("/webjars/**").addResourceLocations("classpath:/META-INF/resources/webjars/");
        registry.addResourceHandler("/favicon.ico").addResourceLocations("classpath:/static/");
    }

    @Override
    public void addViewControllers(ViewControllerRegistry registry) {
        registry.addRedirectViewController("/swagger-ui.html", "/doc.html");
        registry.addRedirectViewController("/swagger-ui/index.html", "/doc.html");
    }
}
