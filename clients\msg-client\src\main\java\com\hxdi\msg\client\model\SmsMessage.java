package com.hxdi.msg.client.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@ApiModel("短信消息")
@Data
public class SmsMessage extends BaseMessage {

    private static final long serialVersionUID = -8924332753124953766L;

    @ApiModelProperty("手机号码")
    private String phoneNum;
    @ApiModelProperty("短信签名")
    private String signName;
    @ApiModelProperty("模板编号")
    private String tplCode;
    @ApiModelProperty("模板参数")
    private String tplParams;
    @ApiModelProperty("模板参数")
    private String template;
    @ApiModelProperty("模板参数")
    private String buzId;
    @ApiModelProperty("模板参数")
    private String receveName;


    public void setTplParams(String tplParams) {
        this.tplParams = tplParams;
    }
}
