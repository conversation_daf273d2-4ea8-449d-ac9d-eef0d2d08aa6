package com.hxdi;

import com.hxdi.transport.sdk.property.ApiProperties;
import com.hxdi.transport.sdk.service.TransportService;
import org.junit.Test;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2023/3/7 10:39
 * @description 测试
 * @version 1.0
 */
public class Demo {

    private static final String USERNAME = "82b68c6a-cb84-487f-817f-33e13d95f3e8";
    private static final String PASSWORD = "87GNo7765G2DC795Yv0UvE150TUzo1";
    private static final String SRT = "a6e69684-47fc-49bb-b929-725abd14d6d6";
    private static final String CID = "9f148783-ba36-4bdd-9cb4-fa225aea74d1";
    private static final String BASE = "https://openapi-test.sinoiov.cn";

    public TransportService buildTransportService() {
        ApiProperties apiProperties = new ApiProperties();
        apiProperties.setCid(CID);
        apiProperties.setSrt(SRT);
        apiProperties.setBaseUrl(BASE);
        return new TransportService(apiProperties);
    }

    /**
     * 运输规划服务
     */
    @Test
    public void transTimeManage() {
        try {
            TransportService transportService = buildTransportService();
            Map<String, Object> map = new HashMap<>(16);
            map.put("vnos", "陕YH0008_2");
            map.put("timeNearby", 24);
            map.put("startAreaCode", "431102");
            map.put("endAreaCode", "450324");


            String url = "/save/apis/transTimeManageV3";
            Optional<String> response = transportService.postHttps(url, map);
            System.out.println("返回:" + response.get());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 运输规划服务
     */
    @Test
    public void visualRoute() {
        try {
            TransportService transportService = buildTransportService();
            Map<String, Object> map = new HashMap<>(16);
            map.put("vclN", "陕YH0008");
            map.put("vco", "2");
            map.put("qryBtm", "2023-03-01");
            map.put("qryEtm", "2023-03-03");
            map.put("startAreaCode", "431102");
            map.put("endAreaCode", "450324");


            String url = "/save/apis/visualRoute";
            Optional<String> response = transportService.postHttps(url, map);
            System.out.println("返回:" + response.get());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}

