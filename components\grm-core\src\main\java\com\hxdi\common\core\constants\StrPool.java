package com.hxdi.common.core.constants;

/**
 * <AUTHOR>
 * @date 2023/4/6 15:56
 * @description 魔法值定义常量池
 * @version 1.0
 */
public interface StrPool {

    /**
     * 符号
     */
    interface Symbol {
        String DOT = ".";
        String COMMA = ",";
        String HYPHEN = "-";
        String UNDERLINE = "_";
        String SLASH = "/";
        String POUND = "#";
        String COLON = ":";
    }

    interface Number {
        Integer ZERO = 0;
        Integer ONE = 1;

        String ZERO_STR = "0";
        String ONE_STR = "1";
    }

    interface State {
        Integer ENABLE = 1;
        Integer DISABLE = 0;
        Integer DELETE = 7;

        String ENABLE_STR = "1";
        String DISABLE_STR = "0";
        String DELETE_STR = "7";
    }

    String AUTHORIZED_TYPE = "Bearer";
    String ACCESS_TOKEN = "access_token";

}
