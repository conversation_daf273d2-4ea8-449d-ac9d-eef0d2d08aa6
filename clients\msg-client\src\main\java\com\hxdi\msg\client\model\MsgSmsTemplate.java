package com.hxdi.msg.client.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-03
 */
@Data
@TableName("msg_sms_template")
public class MsgSmsTemplate  {

    private static final long serialVersionUID = 1L;

    @TableId(value = "tpl_id", type = IdType.ASSIGN_ID)
    private String tplId;

    @TableField("name")
    private String name;

    @TableField("code")
    private String code;

    @TableField("template")
    private String template;

    @TableField("params")
    private String params;

    /**
     * 接收方类型：ORGAN - 组织 / ROLE - 角色 / USER - 用户
     */
    @TableField("receive_type")
    private String receiveType;

    /**
     * 具体的接收方标识  ,拼接
     */
    @TableField("receivers")
    private String receivers;

    /**
     * 具体的接收方名称  ,拼接
     */
    @TableField("receivers_name")
    private String receiversName;

    @TableField("create_time")
    private Date createTime;

    @TableField("update_time")
    private Date updateTime;

    @TableField("status")
    private String status;

    @TableField("subject")
    private String subject;

    @TableField("configurable")
    private Boolean configurable;

    @TableField(exist = false)
    private List<MsgSmsUser> userList;

}
