package com.hxdi.file.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.net.HttpHeaders;
import com.hxdi.common.core.exception.BizExp;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.common.core.properties.FssProperties;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.common.core.utils.WebUtil;
import com.hxdi.common.core.utils.codec.MD5Util;
import com.hxdi.file.client.model.entity.FileResource;
import com.hxdi.file.component.FssTemplate;
import com.hxdi.file.mapper.FileResourceMapper;
import com.hxdi.file.service.FileResourceService;
import com.hxdi.file.util.FileType;
import com.hxdi.file.util.dto.FileParams;
import com.hxdi.file.util.dto.FileWriteResult;
import com.hxdi.watermark.handle.WatermarkHandle;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.FileUrlResource;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/2/22 17:51
 * @description 文件管理实现
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Slf4j
public class FileResourceServiceImpl extends BaseServiceImpl<FileResourceMapper, FileResource> implements FileResourceService {

    private static final long EXPIRE_DAYS = 1 * 24 * 60 * 60 * 1000L;

    private static final long TIME = System.currentTimeMillis();

    private FssTemplate fssTemplate;

    private FssProperties fssProperties;

    private WatermarkHandle watermarkHandle;

    @Autowired
    public FileResourceServiceImpl(FssTemplate fssTemplate, WatermarkHandle watermarkHandle, FssProperties fssProperties) {
        this.fssTemplate = fssTemplate;
        this.watermarkHandle = watermarkHandle;
        this.fssProperties = fssProperties;
    }

    @Override
    public void preview(String resId, HttpServletRequest request, HttpServletResponse response) {
        FileResource resource = baseMapper.selectResourcePathAndType(resId);
        if (resource != null) {
            // 检查缓存头
            String etag = resource.getEtag();
            boolean cacheNotAvailable = checkHeaderCacheByEtag(etag, request, response);

            if (cacheNotAvailable) {
                Optional<InputStream> optionalIs = fssTemplate.loadObject(new FileParams(resource.getPath()));
                if (optionalIs.isPresent()) {
                    response.setHeader(HttpHeaders.ETAG, resource.getEtag());
                    response.addDateHeader(HttpHeaders.LAST_MODIFIED, TIME);
                    response.addDateHeader(HttpHeaders.EXPIRES, System.currentTimeMillis() + EXPIRE_DAYS);
                    response.setHeader(HttpHeaders.CONTENT_TYPE,
                            CommonUtils.isEmpty(resource.getContentType()) ? MediaType.APPLICATION_OCTET_STREAM_VALUE : resource.getContentType());
                    response.setCharacterEncoding("UTF-8");
                    WebUtil.write(response, optionalIs.get());
                }
            }
        }
    }

    @Override
    public List<FileResource> queryResInfo(String[] resIds) {
        List<FileResource> resources = baseMapper.selectBatchIds(Arrays.asList(resIds));
        if (resources.isEmpty()) {
            BizExp.pop("资源不存在");
        }

        resources.forEach(resource -> {
            resource.setUrl(fssTemplate.getObjectUrl(new FileParams(resource.getPath())).orElse(null));
            resource.setPath("");
        });
        return resources;
    }

    @Override
    public FileResource saveAndStore(MultipartFile file, String category) {
        FileResource resource = new FileResource();
        resource.setOriginalFilename(file.getOriginalFilename());
        resource.setExtname(StringUtils.substringAfterLast(file.getOriginalFilename(), "."));
        resource.setByteLen(file.getSize());
        resource.setCategory(category);

        try (InputStream is = file.getInputStream()) {
            // IO流只能一次性读取，通过byte[]缓存数据
            byte[] bytes = IOUtils.toByteArray(is);
            String etag = MD5Util.streamToMD5(new ByteArrayInputStream(bytes));
            // 检查文件是否已经上传过，避免重复上传
            Optional<FileResource> optional = checkExists(etag);
            if (optional.isPresent()) {
                resource.setEtag(etag);
                resource.setPath(optional.get().getPath());
                resource.setContentType(optional.get().getContentType());
            } else {
                // 是否开启水印，开启就调用水印组件服务接口，添加水印之后返回
                if (fssProperties.isEnableWatermark() && FileType.COVER.support(resource.getExtname())) {
                    ByteArrayOutputStream baos = (ByteArrayOutputStream) watermarkHandle.watermark(file);
                    bytes = baos.toByteArray();
                    resource.setByteLen((long) bytes.length);
                }

                Optional<FileWriteResult> result = fssTemplate.storeObject(new ByteArrayInputStream(bytes), new FileParams(resource.getOriginalFilename(), resource.getCategory()));
                if (!result.isPresent()) {
                    throw new IOException();
                }
                resource.setEtag(etag);
                resource.setPath(result.get().getObjectName());
                resource.setContentType(result.get().getContentType());
            }
        } catch (IOException e) {
            BizExp.pop("文件上传失败", e);
        }

        baseMapper.insert(resource);
        return resource;
    }

    @Override
    public FileResource readAndStore(String fileUrl) {
        FileResource fileResource = new FileResource();
        InputStream httpInputStream = null;

        try {
            URL url = new URL(fileUrl);
            log.info("正在读取资源[{}]", url);
            FileUrlResource urlResource = new FileUrlResource(url);
            fileResource.setOriginalFilename(urlResource.getFilename());
            fileResource.setExtname(StringUtils.substringAfterLast(fileResource.getOriginalFilename(), "."));

            httpInputStream = urlResource.getInputStream();

            // IO流只能一次性读取，通过byte[]缓存数据
            byte[] bytes = IOUtils.toByteArray(httpInputStream);

            fileResource.setByteLen(Long.valueOf(bytes.length));

            String etag = MD5Util.streamToMD5(new ByteArrayInputStream(bytes));
            // 检查文件是否已经上传过，避免重复上传
            Optional<FileResource> optional = checkExists(etag);
            if (optional.isPresent()) {
                fileResource.setEtag(etag);
                fileResource.setPath(optional.get().getPath());
                fileResource.setContentType(optional.get().getContentType());
            } else {
                Optional<FileWriteResult> result = fssTemplate.storeObject(new ByteArrayInputStream(bytes), new FileParams(fileResource.getOriginalFilename()));
                if (!result.isPresent()) {
                    throw new IOException();
                }
                fileResource.setEtag(etag);
                fileResource.setPath(result.get().getObjectName());
                fileResource.setContentType(result.get().getContentType());
            }
        } catch (MalformedURLException e1) {
            BizExp.pop("URL地址不合法");
        } catch (IOException e2) {
            BizExp.pop("文件上传失败,读取资源并上传IO异常", e2);
        } finally {
            if (httpInputStream != null) {
                try {
                    httpInputStream.close();
                } catch (Exception e) {
                    log.error("httpInputStream 关闭异常!!!");
                }
            }
        }

        baseMapper.insert(fileResource);
        return fileResource;
    }

    @Override
    public void clean(String[] resIds) {
        List<FileResource> resources = baseMapper.selectBatchIds(Arrays.asList(resIds));
        resources.forEach(resource -> {
            long countRef = countByEtag(resource.getEtag());
            // 当引用计数大于1时，不删除目标文件
            if (countRef == 1L) {
                fssTemplate.removeObject(new FileParams(resource.getPath()));
            }

            baseMapper.deleteById(resource.getId());
        });
    }

    @Override
    public void download(String resId, HttpServletRequest request, HttpServletResponse response) {
        FileResource resource = baseMapper.selectById(resId);
        if (resource == null) {
            BizExp.pop("资源不存在");
        }

        Optional<InputStream> optional = fssTemplate.loadObject(new FileParams(resource.getPath()));
        if (optional.isPresent()) {
            WebUtil.setFileDownloadHeader(response, resource.getOriginalFilename());
            WebUtil.write(response, optional.get());
        }
    }

    @Override
    public void getStream(String resId, HttpServletRequest request, HttpServletResponse response) {
        FileResource resource = baseMapper.selectById(resId);
        if (resource == null) {
            BizExp.pop("资源不存在");
        }

        Optional<InputStream> optional = fssTemplate.loadObject(new FileParams(resource.getPath()));
        if (optional.isPresent()) {
            String encodedFilename = "";
            try {
                encodedFilename = URLEncoder.encode(resource.getOriginalFilename(), "UTF-8");
            } catch (Exception e) {
            }

            response.setHeader("Origin-Name", encodedFilename);
            response.setHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_OCTET_STREAM_VALUE);
            response.setCharacterEncoding("UTF-8");
            WebUtil.write(response, optional.get());
        }
    }

    @Override
    public void downloadZip(String[] resIds, HttpServletRequest request, HttpServletResponse response) {
        List<FileResource> resources = baseMapper.selectBatchIds(Arrays.asList(resIds));
        if (resources.isEmpty()) {
            BizExp.pop("资源不存在");
        }

        List<String> paths = new ArrayList();
        List<InputStream> ins = new ArrayList<>();
        for (FileResource resource : resources) {
            Optional<InputStream> optionalIn = fssTemplate.loadObject(new FileParams(resource.getPath()));
            if (!optionalIn.isPresent()) {
                continue;
            }

            paths.add(resource.getOriginalFilename());
            ins.add(optionalIn.get());
        }

        OutputStream os = zip(paths, ins);
        WebUtil.setFileDownloadHeader(response, CommonUtils.genUUID() + ".zip");
        WebUtil.write(response, new ByteArrayInputStream(((ByteArrayOutputStream) os).toByteArray()));
    }

    /**
     * 压缩
     *
     * @param paths
     * @param ins
     */
    private OutputStream zip(List<String> paths, List<InputStream> ins) {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        try (
                BufferedOutputStream bos = new BufferedOutputStream(baos);
                ZipOutputStream zos = new ZipOutputStream(bos);
        ) {
            for (int i = 0, size = paths.size(); i < size; i++) {
                zos.putNextEntry(new ZipEntry(paths.get(i)));
                IOUtils.copy(ins.get(i), zos);
                zos.closeEntry();
            }
        } catch (Exception e) {
            BizExp.pop("资源压缩失败", e);
        }

        return baos;
    }

    /**
     * 检查文件对象是否已存在，避免重复上传浪费空间
     *
     * @param etag
     * @return
     */
    private Optional<FileResource> checkExists(String etag) {
        return Optional.ofNullable(
                baseMapper.selectOne(Wrappers.<FileResource>lambdaQuery()
                        .select(FileResource::getPath, FileResource::getEtag, FileResource::getContentType)
                        .eq(FileResource::getEtag, etag)
                        .orderByDesc(FileResource::getUpdateTime)
                        .last("limit 1")));
    }

    /**
     * 检查资源引用情况
     *
     * @param etag
     * @return
     */
    private Long countByEtag(String etag) {
        return baseMapper.selectCount(Wrappers.<FileResource>lambdaQuery().eq(FileResource::getEtag, etag));
    }

    /**
     * 检查web缓存是否失效
     *
     * @param etag
     * @param request
     * @param response
     * @return
     */
    private boolean checkHeaderCacheByEtag(String etag, HttpServletRequest request, HttpServletResponse response) {
        // if over expire date, see the Etag;
        // if ETags no any modified
        String ifNoneMatchHeader = request.getHeader("If-None-Match");
        if (ifNoneMatchHeader != null && ifNoneMatchHeader.equals(etag)) {
            response.setStatus(HttpServletResponse.SC_NOT_MODIFIED);
            return false;
        }

        return true;
    }
}
