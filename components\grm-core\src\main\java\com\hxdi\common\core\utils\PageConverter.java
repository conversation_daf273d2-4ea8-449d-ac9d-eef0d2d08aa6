package com.hxdi.common.core.utils;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/2/3 15:03
 * @description
 * @version 1.0
 */
public class PageConverter {

    /**
     * 简单转换分页对象
     * @param page
     * @param clazz
     * @param <S>
     * @param <T>
     * @return
     */
    public static <S,T> Page<T> convertBean(Page<S> page, Class<T> clazz){

        Page<T> convertPage = new Page<>();
        convertPage.setCurrent(page.getCurrent());
        convertPage.setSize(page.getSize());
        convertPage.setTotal(page.getTotal());

        List<S> records = page.getRecords();
        if(records != null && records.size() > 0){
            List<T> newRecords = CachedBeanCopyUtils.copyCollection(records, new ArrayList(), clazz);
            convertPage.setRecords(newRecords);
        }

        return convertPage;
    }

    /**
     * 将分页对象包装成复杂对象返回
     * @param page
     * @param mapFunction
     * @param <S>
     * @param <T>
     * @return
     */
    public static <S,T> Page<T> lambdaConvertBean(Page<S> page, Function<S, T> mapFunction){

        Page<T> convertPage = new Page<>();
        convertPage.setCurrent(page.getCurrent());
        convertPage.setSize(page.getSize());
        convertPage.setTotal(page.getTotal());

        List<S> records = page.getRecords();
        if(records != null && records.size() > 0){
            List<T> newRecords = records.stream().map(mapFunction::apply).collect(Collectors.toList());
            convertPage.setRecords(newRecords);
        }

        return convertPage;
    }
}
