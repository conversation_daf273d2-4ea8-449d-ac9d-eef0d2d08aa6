<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport"
          content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no" />
    <title>应急物资储备管理平台 - 登录</title>
    <link rel="stylesheet" type="text/css" href="../static/bootstrap/css/bootstrap.min.css"
          th:href="@{/static/bootstrap/css/bootstrap.min.css}" />
    <link rel="stylesheet" type="text/css" href="../static/css/login.css" th:href="@{/static/css/login.css}" />
    <script type="text/javascript" charset="utf-8" src="../static/bootstrap/js/bootstrap.min.js"
            th:src="@{/static/bootstrap/js/bootstrap.min.js}"></script>
</head>

<body>
<div class="m-login-bg">
    <!--<div class="login-img">-->
    <!--<img th:src="@{/static/images/logo.png}" class="imgloginclass"/>-->
    <!--</div>-->

    <div class="m-login">

        <!-- <div class="m-login-mwc">
           <img th:src="@{/static/images/bg-left.png}" class="imgleftclass"/>
    </div> -->
        <div class="m-login-warp">
            <div class="card">
                <div class="card-header">
                    <h3 style="text-align: center">浙江省应急物资储备管理平台</h3>
                </div><br>
                <!-- <div class="card-header"> <h3 style="text-align: center">应急救灾物资仓储管理系统</h3></div><br>-->
                <div class="card-block" style="height: 200px; width: 325px; margin: 30px auto">
                    <form role="form" method="post" action="/login">
                        <div class="form-group">
                            <div class="input-group">
                                <span class="input-group-addon"><img th:src="@{/static/images/form.png}" /></span>
                                <input type="text" name="username" required placeholder="登录名" autocomplete="off"
                                       class="form-control ">
                            </div>
                        </div><br>
                        <div class="form-group">
                            <div class="input-group">
                                <span class="input-group-addon"><img th:src="@{/static/images/user.png}" /></span>
                                <input type="password" name="password" required placeholder="密码" autocomplete="off"
                                       class="form-control ">
                            </div>
                        </div>
                        <div class="form-group">
                            <p id="error" class="help-block" style="opacity:0;color: red; text-align: center;">登录名或密码错误！
                            </p>
                        </div>
                        <div class="form-group m-login-btn">
                            <button class="btn btn-primary btn-block" type="submit">登录</button>
                        </div>
                    </form>
                </div>
                <!--<p class="copyright">统一认证登录</p>-->
            </div>
        </div>
    </div>
</div>
</body>
<script>
    window.onload = function () {
        var error = window.location.search.substring(1);
        if (error && error == 'error') {
            //window.location.href = '/error';
            document.getElementById('error').style.opacity = 1;
            return;
        }
    }
</script>

</html>