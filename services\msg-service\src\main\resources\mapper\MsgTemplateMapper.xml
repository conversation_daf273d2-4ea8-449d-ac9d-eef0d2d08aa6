<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.msg.mapper.MsgTemplateMapper">
  <resultMap id="BaseResultMap" type="com.hxdi.msg.client.model.entity.MsgTemplate">
    <!--@mbg.generated-->
    <!--@Table MSG_TEMPLATE-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="MSG_TYPE" jdbcType="VARCHAR" property="msgType" />
    <result column="MSG_CODE" jdbcType="VARCHAR" property="msgCode" />
    <result column="TPL_NAME" jdbcType="VARCHAR" property="tplName" />
    <result column="TITLE" jdbcType="VARCHAR" property="title" />
    <result column="CONTENT" jdbcType="VARCHAR" property="content" />
    <result column="TRI_TYPE" jdbcType="VARCHAR" property="triType" />
    <result column="TRI_PARAMS" jdbcType="VARCHAR" property="triParams" />
    <result column="AUTO_READ" jdbcType="INTEGER" property="autoRead" />
    <result column="ALERM_TYPE" jdbcType="INTEGER" property="alermType" />
    <result column="URLS" jdbcType="VARCHAR" property="urls" />
    <result column="MODE" jdbcType="INTEGER" property="mode" />
    <result column="ENABLED" jdbcType="INTEGER" property="enabled" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="CREATE_ID" jdbcType="VARCHAR" property="createId" />
    <result column="UPDATE_ID" jdbcType="VARCHAR" property="updateId" />
    <result column="TENANT_ID" jdbcType="VARCHAR" property="tenantId" />
    <result column="DATA_HIERARCHY_ID" jdbcType="VARCHAR" property="dataHierarchyId" />
    <result column="EXTEND" jdbcType="VARCHAR" property="extend" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, MSG_TYPE, MSG_CODE, TPL_NAME, TITLE, CONTENT, TRI_TYPE, TRI_PARAMS, AUTO_READ, 
    ALERM_TYPE, URLS, "MODE", ENABLED, CREATE_TIME, UPDATE_TIME, CREATE_ID, UPDATE_ID, 
    TENANT_ID, DATA_HIERARCHY_ID, EXTEND
  </sql>
</mapper>