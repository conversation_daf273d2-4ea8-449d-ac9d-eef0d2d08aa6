package com.hxdi.common.core.exception;

import com.hxdi.common.core.constants.ErrorCode;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.utils.CommonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.oauth2.common.exceptions.InvalidGrantException;
import org.springframework.security.oauth2.common.exceptions.InvalidTokenException;
import org.springframework.security.oauth2.common.exceptions.OAuth2Exception;
import org.springframework.security.web.util.ThrowableAnalyzer;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 统一异常处理器
 *
 * <AUTHOR>
 * @date 2017/7/3
 */
@ControllerAdvice
@ResponseBody
@Slf4j
public class GlobalExceptionHandler {

    private static ThrowableAnalyzer throwableAnalyzer = new ThrowableAnalyzer();

    /**
     * 统一异常处理
     * AuthenticationException
     *
     * @param ex
     * @param request
     * @param response
     * @return
     */
    @ExceptionHandler({AuthenticationException.class})
    public static ResultBody authenticationException(Exception ex, HttpServletRequest request, HttpServletResponse response) {
        ResultBody resultBody = resolveException(ex, request.getRequestURI());
        return resultBody;
    }

    /**
     * AccessDeniedException
     *
     * @param ex
     * @param request
     * @param response
     * @return
     */
    @ExceptionHandler({AccessDeniedException.class})
    public static ResultBody accessDeniedException(Exception ex, HttpServletRequest request, HttpServletResponse response) {
        ResultBody resultBody = resolveException(ex, request.getRequestURI());
        return resultBody;
    }


    @ExceptionHandler({OAuth2Exception.class, InvalidTokenException.class, InvalidGrantException.class})
    public static ResultBody oauth2Exception(Exception ex, HttpServletRequest request, HttpServletResponse response) {
        ResultBody resultBody = resolveException(ex, request.getRequestURI());
        return resultBody;
    }

    /**
     * 处理 json 请求体调用接口校验失败抛出的异常
     * @param ex
     * @param request
     * @return
     */
    @ExceptionHandler({MethodArgumentNotValidException.class})
    public static ResultBody methodArgumentNotValidException(MethodArgumentNotValidException ex, HttpServletRequest request) {
        List<FieldError> errors = ex.getBindingResult().getFieldErrors();
        String errMsg = errors.stream()
                .map(DefaultMessageSourceResolvable::getDefaultMessage)
                .collect(Collectors.joining(","));
        return resolveException(new BaseException(ErrorCode.ARGUMENT_ERROR.getCode(), errMsg), request.getRequestURI());
    }

    /**
     * 处理 form data方式调用接口校验失败抛出的异常
     * @param ex
     * @param request
     * @return
     */
    @ExceptionHandler({BindException.class})
    public static ResultBody bindException(BindException ex, HttpServletRequest request) {
        List<FieldError> errors = ex.getBindingResult().getFieldErrors();
        String errMsg = errors.stream()
                .map(DefaultMessageSourceResolvable::getDefaultMessage)
                .collect(Collectors.joining(","));
        return resolveException(new BaseException(ErrorCode.ARGUMENT_ERROR.getCode(), errMsg), request.getRequestURI());
    }

    /**
     * 自定义异常
     *
     * @param ex
     * @param request
     * @param response
     * @return
     */
    @ExceptionHandler({BaseException.class})
    public static ResultBody systemException(Exception ex, HttpServletRequest request, HttpServletResponse response) {
        ResultBody resultBody = resolveException(ex, request.getRequestURI());
        return resultBody;
    }

    /**
     * 其他异常
     *
     * @param ex
     * @param request
     * @param response
     * @return
     */
    @ExceptionHandler({Exception.class})
    public static ResultBody exception(Exception ex, HttpServletRequest request, HttpServletResponse response) {
        ResultBody resultBody = resolveException(ex, request.getRequestURI());
        return resultBody;
    }

    /**
     * 静态解析异常。可以直接调用
     *
     * @param e
     * @return
     */
    public static ResultBody resolveException(Exception e, String path) {
        String message = e.getMessage();
        HttpStatus httpStatus = HttpStatus.INTERNAL_SERVER_ERROR;
        ErrorCode code = ErrorCode.getResultEnum(message);
        Throwable[] causeChain = throwableAnalyzer.determineCauseChain(e);
        Exception ase = (OAuth2Exception) throwableAnalyzer.getFirstThrowableOfType(OAuth2Exception.class, causeChain);

        if (ase != null) {
            if (CommonUtils.isNotEmpty(message) && message.contains("#Bad credentials#")) {
                code = ErrorCode.BAD_CREDENTIALS;
                return buildBody(e, ErrorCode.BAD_CREDENTIALS.getCode(), message.substring(17), path, httpStatus);
            } else if ("Bad credentials".contains(message)) {
                code = ErrorCode.BAD_CREDENTIALS;
            } else if ("User is disabled".contains(message)) {
                code = ErrorCode.ACCOUNT_DISABLED;
            } else if ("User account is locked".contains(message)) {
                code = ErrorCode.ACCOUNT_LOCKED;
            } else if ("User credentials have expired".contains(message)) {
                code = ErrorCode.CREDENTIALS_EXPIRED;
            } else {
                code = ErrorCode.UNAUTHORIZED;
                httpStatus = HttpStatus.UNAUTHORIZED;
            }
            return buildBody(e, code, path, httpStatus);
        }

        ase = (AuthenticationException) throwableAnalyzer.getFirstThrowableOfType(AuthenticationException.class, causeChain);
        if (ase != null) {
            //httpStatus = HttpStatus.UNAUTHORIZED;
            if (code == ErrorCode.ACCOUNT_NOT_EXIST) {
                return buildBody(e, ErrorCode.ACCOUNT_NOT_EXIST, path, httpStatus);
            } else {
                return buildBody(e, ErrorCode.UNAUTHORIZED, path, httpStatus);
            }
        }

        ase = (AccessDeniedException) throwableAnalyzer.getFirstThrowableOfType(AccessDeniedException.class, causeChain);
        if (ase != null && ase instanceof AccessDeniedException) {
            httpStatus = HttpStatus.FORBIDDEN;
            return buildBody(e, ErrorCode.ACCESS_DENIED, path, httpStatus);
        }

        ase = (BaseException) throwableAnalyzer.getFirstThrowableOfType(BaseException.class, causeChain);
        if (ase != null && ase instanceof BaseException) {
            BaseException baseException = (BaseException) e;
            return buildBody(e, baseException.getCode(), baseException.getMessage(), path, httpStatus);
        }

        ase = (BaseFeginException) throwableAnalyzer.getFirstThrowableOfType(BaseFeginException.class, causeChain);
        if (ase != null && ase instanceof BaseFeginException) {
            BaseFeginException feignException = (BaseFeginException) ase;
            return buildBody(e, feignException.getCode(), feignException.getMessage(), path, httpStatus);
        }

        ase = (BaseSignatureException) throwableAnalyzer.getFirstThrowableOfType(BaseSignatureException.class, causeChain);
        if (ase != null && ase instanceof BaseSignatureException) {
            httpStatus = HttpStatus.BAD_REQUEST;
            code = ErrorCode.SIGNATURE_DENIED;
            return buildBody(e, code, path, httpStatus);
        }

        return buildBody(e, code.getCode(), code.getMessage(), path, httpStatus);
    }

    private static ResultBody buildBody(Exception e, ErrorCode errorCode, String path, HttpStatus httpStatus) {
        ResultBody resultBody = ResultBody.failed().code(errorCode.getCode()).msg(errorCode.getMessage()).path(path).httpStatus(httpStatus.value());
        log.error("==>{}", resultBody, e);
        return resultBody;
    }

    private static ResultBody buildBody(Exception e, Integer code, String message, String path, HttpStatus httpStatus) {
        ResultBody resultBody = ResultBody.failed().code(code).msg(message).put("exp", e.getClass().getName()).path(path).httpStatus(httpStatus.value());
        log.error("==>{}", resultBody, e);
        return resultBody;
    }

}
