package com.hxdi.site.controller;

import com.alibaba.fastjson.JSONObject;
import com.hxdi.common.autoconfigure.ServerConfiguration;
import com.hxdi.common.core.constants.SecurityClientSecret;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.security.SecurityHelper;
import com.hxdi.common.core.utils.RestUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.security.oauth2.provider.token.TokenStore;
import org.springframework.security.web.authentication.logout.SecurityContextLogoutHandler;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.security.Principal;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * @author: liuyadu
 * @date: 2018/11/9 15:43
 * @description:
 */
@Api(tags = "用户认证中心")
@RestController
public class LoginController {

    @Autowired
    private TokenStore tokenStore;
    @Autowired
    private RestUtil restUtil;
    @Autowired
    private ServerConfiguration serverConfiguration;

    @ApiOperation("获取用户基础信息")
    @GetMapping("/current/user")
    public ResultBody getUserProfile() {
        return ResultBody.ok().data(SecurityHelper.getUser());
    }

    @ApiOperation("获取当前登录用户信息-SSO单点登录")
    @GetMapping("/current/user/sso")
    public Principal principal(Principal principal) {
        return principal;
    }

    @ApiOperation(value = "获取用户访问令牌", notes = "基于oauth2密码模式登录,无需签名,返回access_token")
    @PostMapping("/login/token")
    public Object getLoginToken(@RequestParam String username,
                                @RequestParam String password,
                                @RequestParam String ApiKey,
                                @RequestHeader HttpHeaders httpHeaders) throws Exception {
        JSONObject result = getToken(username, password, ApiKey, httpHeaders);
        if (result.containsKey("access_token")) {
            return ResultBody.ok().data(result);
        } else {
            return result;
        }
    }

    @ApiOperation("更新访问令牌")
    @PostMapping("/refresh/token")
    public Object refreshToken(@RequestParam String ApiKey, @RequestParam String refreshToken, @RequestHeader HttpHeaders httpHeaders) throws Exception {
        JSONObject result = getRefreshedToken(ApiKey, refreshToken, httpHeaders);
        if (result.containsKey("access_token")) {
            return ResultBody.ok().data(result);
        } else {
            return result;
        }
    }

    @ApiOperation("退出移除令牌")
    @PostMapping("/logout/token")
    public ResultBody removeToken(@RequestParam String token, HttpServletRequest request) {
        tokenStore.removeAccessToken(tokenStore.readAccessToken(token));
        new SecurityContextLogoutHandler().logout(request, null, null);
        return ResultBody.ok();
    }


    /**
     * 生成 oauth2 token
     *
     * @param userName
     * @param password
     * @param ApiKey
     * @return
     */
    public JSONObject getToken(String userName, String password, String ApiKey, HttpHeaders headers) {
        String url = "http://127.0.0.1:7211/oauth/token";
        // 使用oauth2密码模式登录.
        Map<String, Object> postParameters = new LinkedHashMap<>();
        postParameters.put("username", userName);
        postParameters.put("password", password);
        postParameters.put("client_id", ApiKey);
        postParameters.put("client_secret", SecurityClientSecret.clientSecret);
        postParameters.put("grant_type", "password");
        // 添加参数区分,第三方登录
        // postParameters.put("login_type", type);
        // 使用客户端的请求头,发起请求
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        // 强制移除 原来的请求头,防止token失效
        headers.remove(HttpHeaders.AUTHORIZATION);
        JSONObject result = restUtil.post(url, headers.toSingleValueMap(), postParameters, JSONObject.class);
        return result;
    }

    private JSONObject getRefreshedToken(String apiKey, String refreshToken, HttpHeaders headers){
        String url = "http://127.0.0.1:7211/oauth/token";
        Map<String, Object> postParameters = new LinkedHashMap<>();
        postParameters.put("client_id", apiKey);
        postParameters.put("client_secret", SecurityClientSecret.clientSecret);
        postParameters.put("grant_type", "refresh_token");
        postParameters.put("refresh_token", refreshToken);
        // 使用客户端的请求头,发起请求
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        // 强制移除 原来的请求头,防止token失效
        headers.remove(HttpHeaders.AUTHORIZATION);
        JSONObject result = restUtil.post(url, headers.toSingleValueMap(), postParameters, JSONObject.class);
        return result;
    }

}
