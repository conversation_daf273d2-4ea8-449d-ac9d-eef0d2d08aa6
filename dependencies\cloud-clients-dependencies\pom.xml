<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.hxdi</groupId>
        <artifactId>dependencies</artifactId>
        <version>1.0.0</version>
    </parent>
    <artifactId>cloud-clients-dependencies</artifactId>
    <description>业务组件定义</description>
    <version>1.0.0</version>
    <packaging>pom</packaging>
    <properties>
        <!--自定义版本-->
        <grm.core.version>1.0.0</grm.core.version>
        <grm.system-client.version>1.0.0</grm.system-client.version>
        <grm.msg-client.version>1.0.0</grm.msg-client.version>
        <grm.job-client.version>1.0.0</grm.job-client.version>
    </properties>
    <dependencyManagement>
        <dependencies>
            <!--公共组件引用-->
            <dependency>
                <groupId>com.hxdi</groupId>
                <artifactId>grm-core</artifactId>
                <version>${grm.core.version}</version>
            </dependency>
            <dependency>
                <groupId>com.hxdi</groupId>
                <artifactId>grm-common-starter</artifactId>
                <version>${grm.core.version}</version>
            </dependency>
            <dependency>
                <groupId>com.hxdi</groupId>
                <artifactId>job-client</artifactId>
                <version>${grm.job-client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.hxdi</groupId>
                <artifactId>msg-client</artifactId>
                <version>${grm.msg-client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.hxdi</groupId>
                <artifactId>system-client</artifactId>
                <version>${grm.system-client.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

</project>
