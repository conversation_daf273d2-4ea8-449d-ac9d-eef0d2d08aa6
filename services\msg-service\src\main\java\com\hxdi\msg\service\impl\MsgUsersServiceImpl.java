package com.hxdi.msg.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hxdi.common.core.utils.CommonUtils;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hxdi.msg.client.model.entity.MsgUsers;
import com.hxdi.msg.mapper.MsgUsersMapper;
import com.hxdi.msg.service.MsgUsersService;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Transactional(rollbackFor = Exception.class)
public class MsgUsersServiceImpl extends ServiceImpl<MsgUsersMapper, MsgUsers> implements MsgUsersService{

    @Override
    public void saveMsgUsers(String msgId, List<String> userIds) {
        Date now = new Date();

        List<MsgUsers> msgUsersList = userIds.stream().map(userId -> {
            MsgUsers msgUser = new MsgUsers();
            msgUser.setMsgId(msgId);
            msgUser.setUserId(userId);
            msgUser.setCreateTime(now);
            return msgUser;
        }).collect(Collectors.toList());
        saveBatch(msgUsersList);
    }

    @Override
    public boolean removeMsgUsers(List<String> msgId) {
        return remove(new LambdaQueryWrapper<MsgUsers>().in(MsgUsers::getMsgId, msgId));
    }

    @Override
    public List<MsgUsers> selectUserIdsByMsgId(List<String> msgId) {
        if(CommonUtils.isEmpty(msgId)){
            return new ArrayList<>();
        }
        LambdaQueryWrapper<MsgUsers> queryWrapper = new LambdaQueryWrapper<MsgUsers>()
                .in(MsgUsers::getMsgId, msgId)
                .orderByDesc(MsgUsers::getCreateTime);
        return this.list(queryWrapper);
    }
}
