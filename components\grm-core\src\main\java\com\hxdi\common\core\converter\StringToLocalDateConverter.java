package com.hxdi.common.core.converter;

import org.springframework.core.convert.converter.Converter;
import org.springframework.lang.NonNull;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * @date 2023/7/21 11:43
 * @description
 * @version 1.0
 */
public enum StringToLocalDateConverter implements Converter<String, LocalDate> {

    INSTANCE;

    private static final DateTimeFormatter dt = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    @NonNull
    @Override
    public LocalDate convert(String source) {
        return LocalDate.parse(source, dt);
    }
}
