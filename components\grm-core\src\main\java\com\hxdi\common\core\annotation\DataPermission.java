package com.hxdi.common.core.annotation;

import com.hxdi.common.core.model.ColumnKey;

import java.lang.annotation.*;

/**
 * AOP实现数据权限控制,不支持复杂sql拦截处理，常用于前端列表+分页查询场景
 * <AUTHOR>
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface DataPermission {

    /**
     * 表别名
     * @return
     */
    String alias() default "";

    /**
     * 数据权限字段名
     * @return
     */
    String column() default "";

    /**
     * 数据权限字段键，用于获取值
     * @return
     */
    ColumnKey key() default ColumnKey.NONE;

}
