package com.hxdi.msg.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hxdi.common.core.model.PageParams;
import com.hxdi.msg.client.model.entity.MsgInfo;
import com.hxdi.msg.client.model.vo.ClassifyMessageNumber;

import java.util.List;

/**
 * 消息聚合服务类
 */
public interface MsgInfoService extends IService<MsgInfo> {

    /**
     * 获取用户接收的未读消息列表
     *
     * @param userId     用户ID
     * @param alermType  消息类型
     * @param pageParams 分页参数
     * @return 未读消息列表
     */
    Page<MsgInfo> getUnreadMessages(String userId, String alermType, PageParams pageParams);

    /**
     * 获取用户接收的未读消息列表
     *
     * @param userId    用户ID
     * @param alermType 消息类型
     * @return 未读消息列表
     */
    List<MsgInfo> getUnreadMessages(String userId, String alermType);

    /**
     * 获取用户接收的所有消息列表
     *
     * @param userId     用户ID
     * @param pageParams 分页参数
     * @param title      标题
     * @param publisher  发布人
     * @param alermType  消息类型
     * @return 用户消息列表
     */
    Page<MsgInfo> getUserMessages(String userId, String title, String publisher, String isRead, String alermType, PageParams pageParams);

    /**
     * 保存消息
     *
     * @param msgInfo 消息信息
     */
    void saveMessage(MsgInfo msgInfo);

    /**
     * 修改消息(注意：只有没有发送的消息才能被更新)
     *
     * @param msgInfo 消息信息
     */
    void updateMessage(MsgInfo msgInfo);

    /**
     * 删除消息
     *
     * @param id
     */
    void deleteMessage(String id);

    /**
     * 获取用户发送的所有消息列表
     *
     * @param userId
     * @return
     */
    Page<MsgInfo> getSendMessages(String userId, String title, String state, String alermType, Boolean isAdmin, PageParams pageParams);


    /**
     * 更新消息发送状态及消息发送时间(取当前系统时间)
     *
     * @param id
     * @param status
     */
    void updateStateAndSendTime(String id, int status);


    //----------------------MsgReadMark-----------------------

    /**
     * 标记消息为已读
     *
     * @param msgId  消息ID
     * @param userId 用户ID
     */
    void markAsRead(String msgId, String userId);

    /**
     * 批量标记消息为已读（用户操作）
     *
     * @param msgIds 消息ID列表
     * @param userId 用户ID
     */
    void markBatchAsRead(List<String> msgIds, String alermType, String userId);

    /**
     * 批量标记消息为已读（用于自动已读操作）
     *
     * @param msgIds 消息ID
     * @param userId 用户ID列表
     */
    void markBatchAsRead(String msgIds, List<String> userId);


    /**
     * 获取分类的消息数量
     *
     * @param userId 用户ID
     * @return
     */
    ClassifyMessageNumber getClassifyMessageNumber(String userId);

    //----------------------MsgTemplate-----------------------
    //--- 通过模版发送消息(具体发送时间及方式由所选模版决定) --

    /**
     * 通过模版发送消息
     *
     * @param msgCodes       消息编码
     * @param jsonParamsList 消息参数
     * @param receivers      接收人
     */
    void sendMsgByTemplate(List<String> msgCodes, List<String> jsonParamsList, List<String> receivers);

}
