package com.hxdi.system.client.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hxdi.common.core.mybatis.base.entity.AbstractEntity;
import lombok.*;

/**
 * 网关动态路由
 *
 * @author: liuyadu
 * @date: 2018/10/24 16:21
 * @description:
 */
@Getter
@Setter
@ToString
@TableName("gateway_route")
public class GatewayRoute extends AbstractEntity {
    private static final long serialVersionUID = -2952097064941740301L;

    /**
     * 路由ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String routeId;

    /**
     * 路由名称
     */
    private String routeName;

    /**
     * 路径
     */
    private String path;

    /**
     * 服务ID
     */
    private String serviceId;

    /**
     * 完整地址
     */
    private String url;

    /**
     * 忽略前缀
     */
    private Integer stripPrefix;

    /**
     * 0-不重试 1-重试
     */
    private Integer retryable;

    /**
     * 状态:0-无效 1-有效
     */
    private Integer status;

    /**
     * 默认数据0-否 1-是 禁止删除
     */
    private Integer isPersist;

    /**
     * 路由说明
     */
    private String routeDesc;
}
