server:
    port: ${app.port}
spring:
    application:
        name: ${artifactId}
    cloud:
        #手动配置Bus id,
        bus:
            id: ${artifactId}:${app.port}
        nacos:
            config:
                namespace: ${config.namespace}
                server-addr: ${config.server-addr}
                group: ${config.group}
                shared-configs:
                    - dataId: common.properties
                      refresh: true
                    - dataId: base_db.properties
                      refresh: false
                    - dataId: redis.properties
                      refresh: false
                    - dataId: rabbitmq.properties
                      refresh: false
            discovery:
                namespace: ${config.namespace}
                server-addr: ${discovery.server-addr}
                # false:不注册该服务，仅限本地调试使用
                register-enabled: ${non.debug}
            username: ${auth.username}
            password: ${auth.password}
    main:
        allow-bean-definition-overriding: true
    #解决restful 404错误 spring.mvc.throw-exception-if-no-handler-found=true spring.resources.add-mappings=false
    mvc:
        throw-exception-if-no-handler-found: true
    resources:
        add-mappings: false
    profiles:
        active: ${profile.name}
    # 文件上传限制
    servlet:
      multipart:
        max-file-size: 100MB
        max-request-size: 100MB

management:
    endpoints:
        web:
            exposure:
                include: '*'

knife4j:
    enable: ${api.debug}
    setting:
        enableFooter: false
        enableFooterCustom: true
        footerCustomContent: "Apache License 2.0 | Copyright 2022-2023 [粮食]"

cloud:
    swagger2:
        enabled: ${api.debug}
        description: 文件服务
        title: 文件服务

mybatis-plus:
    typeAliasesPackage: com.hxdi.file.client.model.entity
    mapper-locations: classpath:mapper/*.xml
    configuration:
        cache-enabled: false
        log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
