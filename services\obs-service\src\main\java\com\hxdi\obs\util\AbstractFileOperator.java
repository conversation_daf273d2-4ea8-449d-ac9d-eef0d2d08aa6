package com.hxdi.obs.util;

import com.hxdi.common.core.constants.ErrorCode;
import com.hxdi.common.core.exception.BizExp;
import com.hxdi.common.core.utils.Strcat;
import com.hxdi.obs.util.dto.FileParams;
import com.hxdi.obs.util.dto.FileWriteResult;
import com.obs.services.model.ObjectListing;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.io.InputStream;
import java.net.URL;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2023/2/27 18:44
 * @description 抽象对象管理服务
 * @version 1.0
 */
public abstract class AbstractFileOperator {


    /**
     * 流式上传
     * @param is
     * @param params
     * @return
     */
    public abstract Optional<FileWriteResult> storeObject(InputStream is, FileParams params);

    /**
     * 网络流上传
     * @param url
     * @param params
     * @return
     */
    public abstract Optional<FileWriteResult> storeObject(URL url, FileParams params);

    /**
     * 文件上传
     * @param file
     * @param params
     * @return
     */
    public abstract Optional<FileWriteResult> storeObject(File file, FileParams params);

    /**
     * 分段上传
     * @param file
     * @param params
     * @return
     */
    public abstract Optional<FileWriteResult> multipartStoreObject(File file, FileParams params);

    /**
     * 流式下载
     * @param params
     * @return
     */
    public abstract Optional<InputStream> loadObject(FileParams params);

    /**
     * 分页列举对象
     * @param params
     * @return
     */
    public abstract Optional<List<ObjectListing>> listObjects(FileParams params);

    /**
     * 获取文件对象访问地址
     * @param params
     * @return
     */
    public abstract Optional<String> getObjectSignedUrl(FileParams params);

    /**
     * 删除文件对象
     * @param params
     */
    public abstract void removeObject(FileParams params);

    /**
     * 批量删除文件对象
     * @param params
     */
    public abstract void removeObjects(FileParams params);

    /**
     * 对原始对象重新命名，保证对象的唯一性，避免错误替换的情况。
     * 防止同一目录文件存放太多
     * @param originalName
     * @return
     */
    protected String rename(String originalName) {
        LocalDate date = LocalDate.now();
        String path_0 = String.valueOf(date.getYear());
        String path_1 = String.valueOf(date.getMonthValue());
        String uniqueName;
        String[] splitNames = originalName.split("\\.");
        if (splitNames.length > 1) {
            uniqueName = Strcat.join(splitNames[0], "_", String.valueOf(System.currentTimeMillis()), ".", splitNames[splitNames.length - 1]).toString();
        } else {
            uniqueName = Strcat.join(originalName, "_", String.valueOf(System.currentTimeMillis())).toString();
        }
        return Strcat.joinWithDelimiter("/", path_0, path_1, uniqueName).toString();
    }

    /**
     * 检查上传的文件类型是否支持
     * @param originalName
     */
    protected void isSupportUploadingFile(String originalName) {
        final String extName = StringUtils.substringAfterLast(originalName, ".");
        boolean support = FileType.anySupport(extName);
        if (!support) {
            BizExp.pop(ErrorCode.FILE_TYPE_UNKNOWN);
        }
    }

    /**
     * 解析content-type
     * @param originalName
     * @return
     */
    protected String parseContentType(String originalName) {
//        final String extName = StringUtils.substringAfterLast(originalName, ".");
//        String contentType = MediaType.APPLICATION_OCTET_STREAM_VALUE;
//        if (FileType.VIDEO.support(extName)) {
//            contentType = "video/" + extName;
//        }
//        return contentType;

        return MimeTypes.getInstance().getMimetype(originalName);
    }
}
