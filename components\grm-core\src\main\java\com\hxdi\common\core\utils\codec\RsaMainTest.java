package com.hxdi.common.core.utils.codec;

public class RsaMainTest {
    public static void main(String[] args) throws Exception {

        String filepath = "F:/tmp/";

//        NetRsaEnccrypt.genKeyPair(filepath);


        System.out.println("--------------公钥加密私钥解密过程-------------------");
        String plainText = "ihep_公钥加密私钥解密";
        //公钥加密过程
        byte[] cipherData = NetRsaEnccrypt.encrypt(NetRsaEnccrypt.loadPublicKeyByStr(NetRsaEnccrypt.loadPublicKeyByFile()), plainText.getBytes());
        String cipher = BaseRsa64.encode(cipherData);
        //私钥解密过程
        byte[] res = NetRsaEnccrypt.decrypt(NetRsaEnccrypt.loadPrivateKeyByStr(NetRsaEnccrypt.loadPrivateKeyByFile()), BaseRsa64.decode(cipher));
        String restr = new String(res);
        System.out.println("原文：" + plainText);
        System.out.println("加密：" + cipher);
        System.out.println("解密：" + restr);
        System.out.println();

        System.out.println("--------------私钥加密公钥解密过程-------------------");
        plainText = "ihep_私钥加密公钥解密";
        //私钥加密过程
        cipherData = NetRsaEnccrypt.encrypt(NetRsaEnccrypt.loadPrivateKeyByStr(NetRsaEnccrypt.loadPrivateKeyByFile()), plainText.getBytes());
        cipher = BaseRsa64.encode(cipherData);
        //公钥解密过程
        res = NetRsaEnccrypt.decrypt(NetRsaEnccrypt.loadPublicKeyByStr(NetRsaEnccrypt.loadPublicKeyByFile()), BaseRsa64.decode(cipher));
        restr = new String(res);
        System.out.println("原文：" + plainText);
        System.out.println("加密：" + cipher);
        System.out.println("解密：" + restr);
        System.out.println();

        System.out.println("---------------私钥签名过程------------------");
        String content = "ihep_这是用于签名的原始数据";
        String signstr = RSASignature.sign(content, NetRsaEnccrypt.loadPrivateKeyByFile());
        System.out.println("签名原串：" + content);
        System.out.println("签名串：" + signstr);
        System.out.println();

        System.out.println("---------------公钥校验签名------------------");
        System.out.println("签名原串：" + content);
        System.out.println("签名串：" + signstr);

        System.out.println("验签结果：" + RSASignature.doCheck(content, signstr, NetRsaEnccrypt.loadPublicKeyByFile()));
        System.out.println();
    }
}
