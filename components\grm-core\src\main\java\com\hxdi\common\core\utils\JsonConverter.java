package com.hxdi.common.core.utils;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jdk8.Jdk8Module;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.hxdi.common.core.constants.CommonConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collections;
import java.util.Map;
import java.util.TimeZone;

/**
 * <AUTHOR>
 * @since 2019-12-29
 */
public class JsonConverter {

    private static Logger logger = LoggerFactory.getLogger(JsonConverter.class);
    private static ObjectMapper om = new ObjectMapper();

    static{
        //忽略空值的序列化行为
        om.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        //该属性设置主要是取消将对象的时间默认转换timesstamps(时间戳)形式
        om.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
        //该属性设置主要是将忽略空bean转json错误
        om.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
        //忽略反序列化未知的值
        om.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        //om.setDateFormat(new SimpleDateFormat(DateTimeUtil.DATE_FORMAT));
        om.registerModule(new JavaTimeModule());
        om.registerModule(new Jdk8Module());
        om.setTimeZone(TimeZone.getTimeZone(CommonConstants.TIME_ZONE));
    }

    /**
     * 反序列化
     * @param json
     * @param clazz
     * @param <T>
     * @return
     */
    public static <T> T parse(String json, Class<T> clazz) {

        if(CommonUtils.isEmpty(json) || clazz == null) return null;

        T t = null;
        try {
            return clazz == String.class ? (T)json : om.readValue(json, clazz);
        } catch (Exception e) {
            logger.error("反序列化失败", e);
        }
        return t;
    }

    /**
     * 序列化实例
     * @param bean
     * @param <T>
     * @return
     */
    public static <T> String toJson(T bean) {

        try {
            if (bean == null) return null;
            return bean instanceof String ? (String)bean : om.writeValueAsString(bean);
        } catch (Exception e) {
            logger.error("序列化失败", e);
        }
        return null;
    }

    /**
     * 格式化输出序列化实例之后的json
     * @param bean
     * @param <T>
     * @return
     */
    public static <T> String toJsonWithPretty(T bean){
        try {
            if (bean == null) return null;
            return bean instanceof String ? (String)bean : om.writerWithDefaultPrettyPrinter().writeValueAsString(bean);
        } catch (Exception e) {
            logger.error("序列化失败", e);
        }
        return null;
    }

    /**
     * 通过   TypeReference
     * @param json
     * @param typeReference
     * @param <T>
     * @return
     */
    public static <T> T parse(String json, TypeReference<T> typeReference) {
        if (CommonUtils.isEmpty(json) || typeReference == null) return null;

        try {
            return (typeReference.getType().equals(String.class) ? (T)json : om.readValue(json, typeReference));
        } catch (Exception e) {
            logger.error("反序列化失败", e);
            return null;
        }
    }

    /**
     * 通过javaType 来处理多泛型的转换
     * @param json
     * @param collectionClazz
     * @param elements
     * @param <T>
     * @return
     */
    public static <T> T parse(String json, Class<T> collectionClazz, Class<?>...elements) {
        JavaType javaType = om.getTypeFactory().constructParametricType(collectionClazz, elements);

        try {
            return om.readValue(json, javaType);
        } catch (Exception e) {
            logger.error("反序列化失败", e);
            return null;
        }
    }


    /**
     * 将实体对象转换为Map。
     * 如果转换失败或输入为null，则返回空映射。
     */
    public static Map<String, Object> convertToMap(Object entity) {
        if (entity == null) {
            return Collections.emptyMap();
        }
        try {
            // 将实体转换为Map<String, Object>
            return om.convertValue(entity, new TypeReference<Map<String, Object>>() {});
        } catch (IllegalArgumentException e) {
            // 记录转换错误信息
            logger.error("Failed to convert entity to map. Entity: {}", entity, e);
            return Collections.emptyMap(); // Return empty map on error
        }
    }

    /**
     * 将Map转换为实体对象。
     * 如果转换失败或输入为null，则返回null。
     */
    public static <T> T convertFromMap(Map<String, Object> map, Class<T> clazz){
        if (map==null||map.isEmpty()){
            return null;
        }
        if (clazz == null) {
            logger.warn("Target class cannot be null for map conversion.");
            return null;
        }
        try {
            // 将Map<String, Object>转换为实体
            return om.convertValue(map, clazz);
        } catch (IllegalArgumentException e) {
            // 记录转换错误信息
            logger.error("Failed to convert map to entity. Map: {}, Target Class: {}", map, clazz.getName(), e);
            return null;
        }
    }

}
