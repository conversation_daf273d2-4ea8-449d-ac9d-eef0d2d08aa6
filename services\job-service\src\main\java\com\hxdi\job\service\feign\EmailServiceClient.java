package com.hxdi.job.service.feign;

import com.hxdi.msg.client.service.IEmailServiceClient;
import com.hxdi.msg.client.constatns.MsgConstants;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

/**
 * @author: liu<PERSON><PERSON>
 * @date: 2019/4/1 12:57
 * @description:
 */
@Component
@FeignClient(value = MsgConstants.MSG_SERVICE)
public interface EmailServiceClient extends IEmailServiceClient {
}
