package com.hxdi.system.client.service;

import com.hxdi.common.core.model.ResultBody;
import com.hxdi.system.client.model.entity.SystemRole;
import com.hxdi.system.client.model.entity.SystemRoleUser;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ISystemRoleServiceClient {

    /**
     * 获取角色列表
     *
     * @return
     */
    @GetMapping("/role/list")
    ResultBody<List<SystemRole>> getList();

    /**
     * 获取角色详情
     */
    @GetMapping("/role/info")
    ResultBody<SystemRole> get(@RequestParam(value = "roleId") String roleId);

    /**
     * 获取角色成员列表
     *
     * @param roleId
     * @return
     */
    @GetMapping("/role/users")
    ResultBody<List<SystemRoleUser>> getRoleUsers(@RequestParam(value = "roleId") String roleId);

}
