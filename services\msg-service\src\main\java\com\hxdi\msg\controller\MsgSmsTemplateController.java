package com.hxdi.msg.controller;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.model.PageParams;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.utils.CachedBeanCopyUtils;
import com.hxdi.msg.client.model.MsgSmsConditon;
import com.hxdi.msg.client.model.MsgSmsTemplate;
import com.hxdi.msg.client.model.MsgSmsUser;
import com.hxdi.msg.client.model.SmsMessage;
import com.hxdi.msg.client.model.entity.EmailLogs;
import com.hxdi.msg.client.service.ISmsServiceClient;
import com.hxdi.msg.service.IMsgSmsTemplateService;
import com.hxdi.msg.service.IMsgSmsUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import com.hxdi.common.core.mybatis.base.controller.BaseController;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-03
 */
@Api(tags = "短信模板及发送接口")
@RestController
@RequestMapping("/msgSmsTemplate")
public class MsgSmsTemplateController implements ISmsServiceClient {

    @Autowired
    private IMsgSmsTemplateService msgSmsTemplateService;

    @Autowired
    private IMsgSmsUserService smsUserService;


    @ApiOperation("添加数据")
    @PostMapping("/add")
    public ResultBody add(@RequestBody MsgSmsTemplate msgSmsTemplate) {
        List<MsgSmsUser> userList = msgSmsTemplate.getUserList();
        msgSmsTemplate.setCreateTime(new Date());
        msgSmsTemplate.setCreateTime(new Date());
        msgSmsTemplateService.save(msgSmsTemplate);
        if (!CollectionUtils.isEmpty(userList)){
            userList.forEach(msgSmsUser -> msgSmsUser.setTplId(msgSmsTemplate.getTplId()));
        }
        smsUserService.saveBatch(userList);
        return ResultBody.ok();
    }

    @ApiOperation("添加数据")
    @PostMapping("/update")
    public ResultBody update(@RequestBody MsgSmsTemplate msgSmsTemplate) {
        List<MsgSmsUser> userList = msgSmsTemplate.getUserList();
        msgSmsTemplate.setUpdateTime(new Date());
        msgSmsTemplateService.updateById(msgSmsTemplate);
        //先删除后插
        smsUserService.remove(new QueryWrapper<MsgSmsUser>().eq("tpl_id", msgSmsTemplate.getTplId()));
        if (!CollectionUtils.isEmpty(userList)){
            userList.forEach(msgSmsUser -> msgSmsUser.setTplId(msgSmsTemplate.getTplId()));
        }
        smsUserService.saveBatch(userList);
        return ResultBody.ok();
    }

    @ApiOperation("修改状态")
    @GetMapping("/updateStatus")
    public ResultBody update(String tplId,String status) {
        MsgSmsTemplate msgSmsTemplate=new MsgSmsTemplate();
        msgSmsTemplate.setTplId(tplId);
        msgSmsTemplate.setStatus(status);
        msgSmsTemplateService.updateById(msgSmsTemplate);
        return ResultBody.ok();
    }

    @ApiOperation("获取分页数据")
    @GetMapping("/pageList")
    public ResultBody<Page<MsgSmsTemplate>> pageList(@RequestParam(required = false) Map map) {
        Page<MsgSmsTemplate> page=msgSmsTemplateService.pageList(PageParams.build(map));
        List<MsgSmsTemplate> records = page.getRecords();
        if (!CollectionUtils.isEmpty(records)){
            for (MsgSmsTemplate msgSmsTemplate : records) {
                List<MsgSmsUser> list = smsUserService.list(new QueryWrapper<MsgSmsUser>().eq("tpl_id", msgSmsTemplate.getTplId()));
                msgSmsTemplate.setUserList(list);
            }
        }
        return ResultBody.ok().data(page);
    }

    @Override
    @ApiOperation("获取消息配置模板列表")
    @PostMapping("/configPageList")
    public ResultBody<Page<MsgSmsTemplate>> configPageList(@RequestBody MsgSmsConditon condition) {
        Page<MsgSmsTemplate> page = msgSmsTemplateService.configPageList(condition);
        return ResultBody.ok().data(page);
    }

    @Override
    @ApiOperation("发送消息")
    @PostMapping("/sendMsg")
    public ResultBody sendMsg(@RequestBody Map<String,String> map) {
        msgSmsTemplateService.sendMsg(map);
        return ResultBody.ok();
    }

    @Override
    @ApiOperation("发送消息通知")
    @PostMapping("/sendMsgNotification")
    public ResultBody sendMsgNotification(@RequestBody Map<String,String> map) {
        msgSmsTemplateService.sendMsgNotification(map);
        return ResultBody.ok();
    }




}
