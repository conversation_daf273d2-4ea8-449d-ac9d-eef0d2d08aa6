package com.hxdi.common.core.model;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/8/30 15:55
 * @description 消息传输对象封装格式
 * @version 1.0
 */
@Getter
@Setter
@ToString
public class MessageDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 附加数据，可以用于消息返回时关联数据
     */
    private Map<String, Object> additionalMap = new HashMap<>();

    private Object payload;

    /**
     * 匹配主题：如 push.contract  or push/supervision/contract
     */
    private String topic;

    /**
     * 数据操作标记
     */
    private OptType type = OptType.NONE;

    public MessageDTO(){}

    public MessageDTO(String topic){
        this.topic = topic;
    }
    public MessageDTO(String topic, Object payload, OptType type) {
        this.payload = payload;
        this.topic = topic;
        this.type = type;
    }

}
