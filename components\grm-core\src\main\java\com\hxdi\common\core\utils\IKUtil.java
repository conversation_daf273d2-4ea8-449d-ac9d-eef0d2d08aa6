package com.hxdi.common.core.utils;

import org.wltea.analyzer.core.IKSegmenter;
import org.wltea.analyzer.core.Lexeme;

import java.io.IOException;
import java.io.StringReader;
import java.util.ArrayList;
import java.util.List;

/**
 * IK分词器工具类
 * <AUTHOR>
 */
public class IKUtil {

    /**
     * 分词
     *
     * @param content
     * @return
     */
    public static List<String> spilt(String content) {
        List<String> res = new ArrayList<>();
        StringReader stringReader = new StringReader(content);
        IKSegmenter ikSegmenter = new IKSegmenter(stringReader, true);
        try {
            Lexeme lexeme = null;
            while ((lexeme = ikSegmenter.next()) != null) {
                res.add(lexeme.getLexemeText());
            }
        }catch (IOException e){
        }

        return res;
    }

}
