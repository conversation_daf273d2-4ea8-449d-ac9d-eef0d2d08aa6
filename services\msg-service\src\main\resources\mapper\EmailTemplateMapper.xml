<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdicloud.msg.mapper.EmailTemplateMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hxdi.msg.client.model.entity.EmailTemplate">
        <id column="tpl_id" property="tplId" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="name" property="name" />
        <result column="code" property="code" />
        <result column="config_id" property="configId" />
        <result column="template" property="template" />
        <result column="params" property="params" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        tpl_id, name, code, config_id, template, params, create_time, update_time
    </sql>


</mapper>
