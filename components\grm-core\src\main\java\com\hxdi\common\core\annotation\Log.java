package com.hxdi.common.core.annotation;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 操作日志注解
 * 用于标记需要记录操作日志的方法
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Api(value = "操作日志注解")
public @interface Log {

    /**
     * 操作描述
     */
    @ApiModelProperty(value = "操作描述")
    String value() default "";

    /**
     * 是否保存请求参数
     */
    @ApiModelProperty(value = "是否保存请求参数")
    boolean saveRequestData() default false;

    /**
     * 是否保存响应数据
     */
    @ApiModelProperty(value = "是否保存响应数据")
    boolean saveResponseData() default false;
} 