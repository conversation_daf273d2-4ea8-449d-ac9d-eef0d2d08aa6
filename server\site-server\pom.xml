<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.hxdi</groupId>
        <artifactId>server</artifactId>
        <version>1.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>site-server</artifactId>
    <packaging>jar</packaging>
    <version>1.0.0</version>
    <description>站点资源服务和认证服务</description>
    <dependencies>
        <dependency>
            <groupId>com.hxdi</groupId>
            <artifactId>grm-core</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-data-elasticsearch</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>hx-common-data-starter</artifactId>
                    <groupId>com.hx.arch</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.hxdi</groupId>
            <artifactId>grm-cloud-starter</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.hxdi</groupId>
            <artifactId>grm-common-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hxdi</groupId>
            <artifactId>system-client</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-thymeleaf</artifactId>
        </dependency>
        <dependency>
            <groupId>net.sourceforge.nekohtml</groupId>
            <artifactId>nekohtml</artifactId>
            <version>1.9.22</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.security.oauth</groupId>
            <artifactId>spring-security-oauth2</artifactId>
            <version>2.3.4.RELEASE</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>

    <profiles>
        <!-- 开发环境 -->
        <profile>
            <id>dev</id>
            <properties>
                <package.environment>dev</package.environment>
                <tag>SNAPSHOT</tag>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <!-- 测试环境 -->
        <profile>
            <id>test</id>
            <properties>
                <package.environment>test</package.environment>
                <tag>ALPHA</tag>
            </properties>
        </profile>
        <!-- 生产环境 -->
        <profile>
            <id>release</id>
            <properties>
                <package.environment>release</package.environment>
                <tag>RELEASE</tag>
            </properties>
        </profile>
        <profile>
            <id>release/beta</id>
            <properties>
                <package.environment>release/beta</package.environment>
                <tag>BETA</tag>
            </properties>
        </profile>
    </profiles>

    <build>
        <finalName>${artifactId}-${version}-${tag}</finalName>
        <filters>
            <filter>src/main/package/${package.environment}/app.properties</filter>
        </filters>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
            <resource>
                <directory>src/main/package/${package.environment}</directory>
                <filtering>false</filtering>
            </resource>
        </resources>

        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
