package com.hxdi.msg.controller;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.msg.client.model.entity.MsgTemplate;
import com.hxdi.msg.client.model.vo.MsgTempateSend;
import com.hxdi.msg.client.model.vo.MsgTemplateCondition;
import com.hxdi.msg.service.MsgTemplateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
* 消息模版控制层
*
* <AUTHOR>
*/
@RestController
@RequestMapping("/msgTemplate")
@Api(tags = "消息模板")
public class MsgTemplateController {
/**
* 服务对象
*/
    @Resource
    private MsgTemplateService msgTemplateService;

    /**
    * 通过主键查询单条数据
    *
    * @param id 主键
    * @return 单条数据
    */
    @GetMapping("/getOne")
    @ApiOperation("根据ID查找模版")
    public ResultBody<MsgTemplate> selectOne(@RequestParam("id") String id) {
        return ResultBody.ok().data(msgTemplateService.getById(id));
    }

    @GetMapping("/getByCondition")
    @ApiOperation("根据条件查找模版列表")
    public ResultBody<List<MsgTemplate>> getByCondition(MsgTemplateCondition condition) {
        return ResultBody.ok().data(msgTemplateService.getListByCondition(condition));
    }

    @GetMapping("/getPageByCondition")
    @ApiOperation("根据条件查找模版分页")
    public ResultBody<IPage<MsgTemplate>> getPageByCondition(MsgTemplateCondition condition) {
        return ResultBody.ok().data(msgTemplateService.getPageByCondition(condition));
    }

    @PostMapping("/add")
    @ApiOperation("添加模版")
    public ResultBody<Void> add(@RequestBody MsgTemplate msgTemplate) {
        msgTemplateService.addV1(msgTemplate);
        return ResultBody.ok();
    }

    @PostMapping("/update")
    @ApiOperation("修改模版")
    public ResultBody<Void> update(@RequestBody MsgTemplate msgTemplate) {
        msgTemplateService.updateV1(msgTemplate);
        return ResultBody.ok();
    }

    @GetMapping("/delete")
    @ApiOperation("删除模版")
    public ResultBody<Void> delete(@RequestParam("id") String id) {
        msgTemplateService.deleteV1(id);
        return ResultBody.ok();
    }

    @GetMapping("/changeStatus")
    @ApiOperation("修改模版启用状态")
    public ResultBody<Void> changeStatus(@RequestParam("id") String id, @RequestParam("status") Integer status) {
        msgTemplateService.changeStatus(id, status);

        return ResultBody.ok();
    }


}
