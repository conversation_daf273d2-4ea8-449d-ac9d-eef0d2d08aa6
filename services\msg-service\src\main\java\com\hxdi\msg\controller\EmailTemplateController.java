package com.hxdi.msg.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.model.PageParams;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.msg.client.model.entity.EmailTemplate;
import com.hxdi.msg.service.EmailTemplateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.Map;

@Api(tags = "邮件模板配置")
@RestController
@RequestMapping("/emailTemplate")
public class EmailTemplateController {

    @Autowired
    private EmailTemplateService targetService;

    @ApiOperation("获取分页数据")
    @GetMapping("/list")
    public ResultBody<Page<EmailTemplate>> list(@RequestParam(required = false) Map map) {
        QueryWrapper<EmailTemplate> queryWrapper = new QueryWrapper();
        return ResultBody.ok().data(targetService.page(new PageParams(map), queryWrapper));
    }

    @ApiOperation("根据ID查找数据")
    @GetMapping("/get")
    public ResultBody<EmailTemplate> get(@RequestParam("id") Long id) {
        EmailTemplate entity = targetService.getById(id);
        return ResultBody.ok().data(entity);
    }

    @ApiOperation("添加数据")
    @PostMapping("/add")
    public ResultBody add(@RequestParam(value = "name") String name,
                          @RequestParam(value = "code") String code,
                          @RequestParam(value = "configId") String configId,
                          @RequestParam(value = "template") String template,
                          @RequestParam(value = "params") String params) {

        EmailTemplate entity = new EmailTemplate();
        entity.setName(name);
        entity.setCode(code);
        entity.setConfigId(configId);
        entity.setTemplate(template);
        entity.setParams(params);
        targetService.save(entity);
        return ResultBody.ok();
    }

    @ApiOperation("更新数据")
    @PutMapping("/update")
    public ResultBody add(@RequestParam(value = "tplId") String tplId,
                          @RequestParam(value = "name") String name,
                          @RequestParam(value = "code") String code,
                          @RequestParam(value = "configId") String configId,
                          @RequestParam(value = "template") String template,
                          @RequestParam(value = "params") String params) {

        EmailTemplate entity = new EmailTemplate();
        entity.setTplId(tplId);
        entity.setName(name);
        entity.setCode(code);
        entity.setConfigId(configId);
        entity.setTemplate(template);
        entity.setParams(params);
        targetService.updateById(entity);
        return ResultBody.ok();
    }

    @ApiOperation("删除数据")
    @DeleteMapping("/remove")
    public ResultBody remove(@RequestParam(value = "id") String id) {
        targetService.removeById(id);
        return ResultBody.ok();
    }

    @ApiOperation("批量删除数据")
    @DeleteMapping("/batch/remove")
    public ResultBody batchRemove(@RequestParam(value = "ids") String ids) {
        targetService.removeByIds(Arrays.asList(ids.split(",")));
        return ResultBody.ok();
    }

}
