/*
 * Copyright (c) 2011-2022, bao<PERSON><PERSON><PERSON> (<EMAIL>).
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.hxdi.database.interceptor;

import com.baomidou.mybatisplus.core.toolkit.PluginUtils;
import com.baomidou.mybatisplus.extension.parser.JsqlParserSupport;
import com.baomidou.mybatisplus.extension.plugins.inner.InnerInterceptor;
import com.hxdi.common.core.exception.BaseException;
import com.hxdi.common.core.interceptor.DataPermissionAspect;
import com.hxdi.common.core.interceptor.DataSqlParser;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.operators.conditional.AndExpression;
import net.sf.jsqlparser.statement.select.PlainSelect;
import net.sf.jsqlparser.statement.select.Select;
import net.sf.jsqlparser.statement.select.SelectBody;
import net.sf.jsqlparser.statement.select.SetOperationList;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;

import java.sql.SQLException;
import java.util.List;

/**
 * <数据权限插件>
 * <AUTHOR>
 * @version 1.0
 * @since 2025/4/10 22:35
 */
@Slf4j
public class CustomDataPermissionInterceptor extends JsqlParserSupport implements InnerInterceptor {

    private DefaultDataPermissionHandler dataPermissionHandler;

    public CustomDataPermissionInterceptor(DefaultDataPermissionHandler dataPermissionHandler) {
        this.dataPermissionHandler = dataPermissionHandler;
    }

    @Override
    public void beforeQuery(Executor executor, MappedStatement ms, Object parameter, RowBounds rowBounds, ResultHandler resultHandler, BoundSql boundSql) throws SQLException {
        DataSqlParser sqlParser = DataPermissionAspect.getScopesContext();
        if (sqlParser == null){
            return;
        }

        PluginUtils.MPBoundSql mpBs = PluginUtils.mpBoundSql(boundSql);
        mpBs.sql(parserSingle(mpBs.sql(), ms.getId()));
    }

    @Override
    protected void processSelect(Select select, int index, String sql, Object obj) {
        SelectBody selectBody = select.getSelectBody();
        if (selectBody instanceof PlainSelect) {
            this.setWhere((PlainSelect) selectBody, (String) obj);
        } else if (selectBody instanceof SetOperationList) {
            SetOperationList setOperationList = (SetOperationList) selectBody;
            List<SelectBody> selectBodyList = setOperationList.getSelects();
            selectBodyList.forEach(s -> this.setWhere((PlainSelect) s, (String) obj));
        }
    }

    /**
     * 设置 where 条件
     *
     * @param plainSelect  查询对象
     * @param mappedStatementId 执行sql id
     */
    protected void setWhere(PlainSelect plainSelect, String mappedStatementId) {
        try {
            DataSqlParser customSqlParser = DataPermissionAspect.getScopesContext();
            if (null != customSqlParser) {
                Expression whereSql = plainSelect.getWhere();
                Expression customSqlSegment = customSqlParser.getSqlSegment();
                if (whereSql == null) {
                    plainSelect.setWhere(customSqlSegment);
                } else {
                    plainSelect.setWhere(new AndExpression(whereSql, customSqlSegment));
                }
            }
        } catch (Exception e) {
            throw new BaseException("数据权限SQL解析异常!!!");
        }
    }
}
