package com.hxdi.common.core.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;
import java.util.UUID;

/**
 * @program: nmjl-service
 * @description: 数据变更消息
 * @author: 王贝强
 * @create: 2025-03-27 09:36
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class DataSyncMessage implements Serializable {
    private String messageId = UUID.randomUUID().toString(); // 自动生成唯一ID
    private String entityType;
    private String entityId;
    private String operation;
    private Map<String, Object> data;
    private Date version;
    private Long timestamp = System.currentTimeMillis();
}
