package com.hxdi.common.core.model;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.io.Serializable;

public class QueryCondition implements Serializable {

    /**
     * 页码
     */
    private int page = 1;
    /**
     * 记录数
     */
    private int limit = 10;

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getLimit() {
        return limit;
    }

    public void setLimit(int limit) {
        this.limit = limit;
    }

    /**
     * 创建分页对象
     * @return
     */
    public <T> Page<T> newPage() {
        return new Page<>(this.page, this.limit);
    }

}
