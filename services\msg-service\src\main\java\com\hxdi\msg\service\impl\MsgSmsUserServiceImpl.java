package com.hxdi.msg.service.impl;

import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.msg.client.model.MsgSmsUser;
import com.hxdi.msg.mapper.MsgSmsUserMapper;
import com.hxdi.msg.service.IMsgSmsUserService;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-03
 */
@Service
public class MsgSmsUserServiceImpl extends BaseServiceImpl<MsgSmsUserMapper, MsgSmsUser> implements IMsgSmsUserService {

}
