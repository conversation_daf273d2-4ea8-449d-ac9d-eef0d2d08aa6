package com.hxdi.system.client.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

@Getter
@Setter
@ToString
@TableName("temp_import_error_log")
public class TempImportErrorLog implements Serializable {

    private static final long serialVersionUID = -2209032544085738042L;

    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 数据类型：ORG，USER
     */
    private String objType;

    /**
     * 序列化json字符串
     */
    private String jsonContent;

    /**
     * 错误原因
     */
    private String err;
    private String batchId;

    public TempImportErrorLog(){}

    public TempImportErrorLog (String jsonContent, String objType, String batchId, String errMsg) {
        this.jsonContent = jsonContent;
        this.objType = objType;
        this.batchId = batchId;
        this.err = errMsg;
    }
}
