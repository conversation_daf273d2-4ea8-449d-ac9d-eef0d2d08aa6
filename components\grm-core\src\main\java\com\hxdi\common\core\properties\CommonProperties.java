package com.hxdi.common.core.properties;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@ConfigurationProperties(prefix = "hxc.common")
@RefreshScope
public class CommonProperties {
    /**
     * 网关客户端Id
     */
    private String clientId;
    /**
     * 网关客户端密钥
     */
    private String clientSecret;
    /**
     * 网关服务地址
     */
    private String apiServerAddr;

    /**
     * 平台认证服务地址
     */
    private String authServerAddr;

    /**
     * 后台部署地址
     */
    private String adminServerAddr;

    /**
     * 认证范围
     */
    private String scope;
    /**
     * 获取token
     */
    private String accessTokenUri;
    /**
     * 认证地址
     */
    private String userAuthorizationUri;
    /**
     * 校验token地址
     */
    private String tokenInfoUri;
    /**
     * 获取用户信息地址
     */
    private String userInfoUri;

    /**
     * jwt签名key
     */
    private String jwtSigningKey;

    /**
     * 第三方账号注册登记地址
     */
    private String thirdPartyRegistryUri;

    /**
     * 微信小程序审核后台开关
     */
    private boolean wxEnable = false;

    /**
     * 是否开启调用加工侧系统接口
     * 生产上开启，其他环境默认关闭
     */
    private boolean jgcOpenApiEnable = false;

    /**
     * 是否开启调用监管平台系统接口
     * 生产上开启，其他环境默认关闭
     */
    private boolean jgptOpenApiEnable = false;


    /**
     * 调试开关，用于控制通用组件是否生效
     * 默认是开启的，准生产环境Nacos配置为false
     */
    private boolean debugEnable = true;
}
