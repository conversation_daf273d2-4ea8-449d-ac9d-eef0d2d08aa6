package com.hxdi.common.core.properties;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;

/**
 * <AUTHOR>
 * @date 2023/2/23 13:50
 * @description 文件资源管理服务参数配置
 * @version 1.0
 */
@Getter
@Setter
@ConfigurationProperties(prefix = "fss")
@RefreshScope
public class FssProperties {

    public final static String MINIO = "minio";

    public final static String LOCAL= "local";

    /**
     * 文件存储服务实现类型:
     * minio: 基于minio分布式对象存储服务实现
     * local: 基于简单本地文件对象操作实现
     */
    private String type;

    private String accessKey;
    private String secretKey;

    /**
     * 保存根路径
     */
    private String root;

    /**
     * 文件资源服务器端点
     */
    private String endpoint;

    /**
     * 对象容器
     */
    private String bucketName;

    /**
     * 资源访问地址
     */
    private String resourceUrl;

    /**
     * 是否开启添加水印
     */
    private boolean enableWatermark = false;
}
