package com.hxdi.msg.config;

import com.hxdi.common.core.constants.CommonConstants;
import com.hxdi.msg.service.IMsgSmsTemplateService;
import com.hxdi.msg.service.SmsSender;
import com.hxdi.msg.service.impl.EMaySmsSenderImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.messaging.handler.annotation.Payload;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Configuration
@Slf4j
public class SmsConfiguration {

	@Autowired
	private IMsgSmsTemplateService msgSmsTemplateService;

	@Bean
	@ConditionalOnClass({ EMaySmsSenderImpl.class })
	public SmsSender smsSender() {
		EMaySmsSenderImpl sender = new EMaySmsSenderImpl();
		return sender;
	}
}
