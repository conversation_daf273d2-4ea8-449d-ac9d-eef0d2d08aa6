package com.hxdi.system.client.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 系统消息表
 */
@Getter
@Setter
@TableName("system_msg_template")
public class SystemMsgTemplate implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 发送方id
     */
    private String senderId;

    /**
     * 发送方名称
     */
    private String senderName;

    /**
     * 接收方类型  组织 - ORGAN  角色 - ROLE  用户 - USER  所有人 - ALL
     */
    private String receiveType;

    /**
     * 接收方id  ","拼接
     */
    private String receivers;

    /**
     * 接收方名称  ","拼接
     */
    private String receiversName;

    /**
     * 通知类型  1 - 系统公告   2 - 工作通知
     */
    private Integer type;

    /**
     * 通知标题
     */
    private String title;

    /**
     * 通知内容
     */
    private String contents;

    /**
     * 发布时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date expectSendTime;

    /**
     * 发布状态
     */
    private Integer sendState;

    /**
     * 数据权限
     */
    private String dataHierarchyId;

    /**
     * 租户id
     */
    private String tenantId;

    /**
     * 是否可配置
     */
    private Boolean configurable = true;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    public Date createTime;

    /**
     * 修改时间 - 最后一次修改模板的时间, 更新发送状态时不更新该时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    public Date updateTime;

    /**
     *   ------------------
     */

    @TableField(exist = false)
    public Integer readState;

    /**
     * 业务系统消息类型  2 - 库点新建通知
     */
    @TableField(exist = false)
    public Integer refType;



    public SystemMsgTemplate() {}

    /**
     * 业务添加系统消息构造器
     * @param receiveType
     * @param receivers
     * @param receiversName
     * @param type
     * @param title
     * @param contents
     * @param expectSendTime
     */
    public SystemMsgTemplate(String receiveType, String receivers, String receiversName, Integer type, String title, String contents, Date expectSendTime) {
        this.receiveType = receiveType;
        this.receivers = receivers;
        this.receiversName = receiversName;
        this.type = type;
        this.title = title;
        this.contents = contents;
        this.expectSendTime = expectSendTime;
        this.configurable = false;
    }

    public static class RefIdConstants {
        // 密码过期提醒模板id
        public static final String TEMPLATE_ID_PWD_EXPIRED = "001";
        // 库点新建通知模板
        public static final String TEMPLATE_ID_WAREHOUSE_ADD = "002";
        // 储备计划通知模板
        public static final String TEMPLATE_ID_STORE_PLAN = "003";
    }

}
