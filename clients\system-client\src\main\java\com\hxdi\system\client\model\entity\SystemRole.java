package com.hxdi.system.client.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hxdi.common.core.mybatis.base.entity.AbstractEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * 系统角色-基础信息
 *
 * @author: liuyadu
 * @date: 2018/10/24 16:21
 * @description:
 */
@Getter
@Setter
@ToString
@TableName("system_role")
public class SystemRole extends AbstractEntity {

    private static final long serialVersionUID = 5197785628543375591L;
    /**
     * 角色ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String roleId;

    /**
     * 角色编码
     */
    @NotEmpty(message = "角色编码不能为空")
    private String roleCode;

    /**
     * 角色名称
     */
    @NotEmpty(message = "角色名称不能为空")
    private String roleName;

    /**
     * 角色类型：0-通用，1-普通，2-系统数据角色（默认），3-自定义数据角色
     */
    @NotNull(message = "角色类型不能为空")
    private Integer roleType;

    /**
     * 角色描述
     */
    private String remark;

    /**
     * 状态:0-无效 1-有效
     */
    private Integer status;

    /**
     * 默认数据0-否 1-是 禁止删除
     */
    private Integer isPersist;

    /**
     * 自定义数据权限范围
     */
    private String dataScope;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 父角色，角色分层控制
     */
    private String parentId;

    /**
     * 角色路径
     */
    private String rolePath;

    /**
     * 是否显示 0-否 1-是
     */
    private Integer isVisible;

    /**
     * 是否数据角色
     * @return
     */
    public boolean isDataRole() {
        return roleType == 2 || roleType == 3;
    }

    /**
     * 是否是功能角色
     * @return
     */
    public boolean isFuncRole() {
        return roleType == 0 || roleType == 1;
    }
}
