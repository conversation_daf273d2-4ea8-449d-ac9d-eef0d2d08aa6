package com.hxdi.system.client.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.system.client.model.entity.GatewayAccessLogs;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Map;

/**
 * <AUTHOR>
 */
public interface IGatewayAccessLogsClient {

    /**
     * 获取服务列表
     *
     * @return
     */
    @GetMapping("/gateway/access/logs")
    ResultBody<Page<GatewayAccessLogs>> getPage(@RequestParam("map") Map<String, Object> map);

}
