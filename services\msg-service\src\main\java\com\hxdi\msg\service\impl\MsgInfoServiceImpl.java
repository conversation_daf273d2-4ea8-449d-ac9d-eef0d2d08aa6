package com.hxdi.msg.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hxdi.common.core.exception.BaseException;
import com.hxdi.common.core.model.PageParams;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.msg.client.model.entity.MsgInfo;
import com.hxdi.msg.client.model.entity.MsgTask;
import com.hxdi.msg.client.model.entity.MsgUsers;
import com.hxdi.msg.client.model.vo.ClassifyMessageNumber;
import com.hxdi.msg.enums.MsgEnum;
import com.hxdi.msg.mapper.MsgInfoMapper;
import com.hxdi.msg.service.*;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Transactional(rollbackFor = Exception.class)
public class MsgInfoServiceImpl extends ServiceImpl<MsgInfoMapper, MsgInfo> implements MsgInfoService {

    @Resource
    private MsgUsersService msgUsersService;

    @Resource
    private MsgReadMarkService msgReadMarkService;

    @Resource
    private MsgTemplateService msgTemplateService;

    @Lazy
    @Resource
    private MsgTaskService msgTaskService;

    @Override
    public Page<MsgInfo> getUnreadMessages(String userId, String alermType, PageParams pageParams) {
        return baseMapper.selectUnreadMessagesPageForUser(pageParams, alermType, userId, MsgEnum.MSG_USER_ALL.getCode());
    }

    @Override
    public List<MsgInfo> getUnreadMessages(String userId, String alermType) {
        return baseMapper.selectUnreadMessagesListForUser(alermType, userId, MsgEnum.MSG_USER_ALL.getCode());
    }

    @Override
    public Page<MsgInfo> getUserMessages(String userId, String title, String publisher, String isRead, String alermType, PageParams pageParams) {
        return baseMapper.selectUserMessages(pageParams, title, publisher, isRead, alermType, userId, MsgEnum.MSG_USER_ALL.getCode());
    }

    @Override
    public void saveMessage(MsgInfo msgInfo) {

        // 如果发送给所有人，则添加特殊的用户标识
        if (msgInfo.getRcvType() == 1) {
            msgInfo.setReceiver(MsgEnum.MSG_USER_ALL.getCode());
        } else {
            //获取接收人列表
            List<String> userIds = Arrays.asList(msgInfo.getReceiver().split(","));
            if (userIds.isEmpty()) {
                throw new BaseException("消息接收人不能为空!");
            }
        }

        // 保存消息
        msgInfo.setCreateTime(new Date());
        msgInfo.setUpdateTime(msgInfo.getCreateTime());
        msgInfo.setState(0);
        baseMapper.insert(msgInfo);
        if (msgInfo.getId() == null) {
            throw new BaseException("消息保存失败: " + msgInfo);
        }
        Date now = new Date();
        if (msgInfo.getScheduledTime() == null || msgInfo.getScheduledTime().compareTo(now) <= 0) {
            // 如果计划发送时间为空或已经过了计划发送时间，则立即发送
            msgInfo.setScheduledTime(now); // 确保有发送时间
            msgTaskService.sendMsg(Collections.singletonList(msgInfo), null);
        } else {
            //新增一个定时发送任务
            msgTaskService.addTask(msgInfo);
        }
    }

    @Override
    public void updateMessage(MsgInfo msgInfo) {
        // 如果发送给所有人，则添加特殊的用户标识
        List<String> userIds = new ArrayList<>();

        if (msgInfo.getRcvType() == 1) {
            msgInfo.setReceiver(MsgEnum.MSG_USER_ALL.getCode());
            userIds.add(MsgEnum.MSG_USER_ALL.getCode());
        } else {
            //获取接收人列表
            userIds = Arrays.asList(msgInfo.getReceiver().split(","));
            if (userIds.isEmpty()) {
                throw new BaseException("消息接收人不能为空!");
            }
        }
        //先查询原消息
        MsgInfo oldMsg = baseMapper.selectById(msgInfo.getId());
        if (oldMsg == null) {
            throw new BaseException("待更新的消息不存在!");
        }
        if (oldMsg.getState() == 1) {
            throw new BaseException("待更新的消息已发送，无法修改!");
        }
        if (!Objects.equals(oldMsg.getPublisher(), msgInfo.getPublisher())) {
            throw new BaseException("待更新的消息不是当前用户发布，无法修改!");
        }

        // 更新消息
        msgInfo.setUpdateTime(new Date());
        baseMapper.updateById(msgInfo);
        // 删除原消息与用户的关系
        msgTaskService.updateTask(msgInfo);
    }

    @Override
    public void deleteMessage(String id) {
        MsgInfo msgInfo = baseMapper.selectById(id);
        if (msgInfo == null) {
            throw new BaseException("待删除的消息不存在!");
        }
        //删除消息与用户的关系
        msgUsersService.removeMsgUsers(Collections.singletonList(id));
        //删除定时发送任务
        msgTaskService.removeTask(msgInfo);
        //删除消息
        baseMapper.deleteById(id);
    }

    @Override
    public Page<MsgInfo> getSendMessages(String userId, String title, String state, String alermType, Boolean isAdmin, PageParams pageParams) {
        //查询所有有效的消息
        LambdaQueryWrapper<MsgInfo> wrapper = new LambdaQueryWrapper<MsgInfo>()
                .eq(MsgInfo::getEnabled, 1);

        if (CommonUtils.isNotEmpty(alermType)) {
            //根据消息类型查询
            wrapper.eq(MsgInfo::getAlermType, alermType);
        }

        if (CommonUtils.isNotEmpty(title)) {
            //根据标题查询
            wrapper.like(MsgInfo::getTitle, "%" + title + "%");
        }

        if (CommonUtils.isNotEmpty(state)) {
            //根据发送状态查询
            wrapper.eq(MsgInfo::getState, state);
        }

        if (!isAdmin) {
            //普通用户只能查询自己发布的消息
            wrapper.eq(MsgInfo::getPublisher, userId);
        }

        wrapper.orderByDesc(MsgInfo::getCreateTime);

        Page<MsgInfo> page = new Page<>(pageParams.getCurrent(), pageParams.getLimit());
        Page<MsgInfo> infoPage = baseMapper.selectPage(page, wrapper);
        List<MsgInfo> records = infoPage.getRecords();

        if (records.isEmpty()) {
            return infoPage;
        }

        // 查询所有消息对应的接收人
        List<String> msgIds = records.stream().map(MsgInfo::getId).collect(Collectors.toList());
        List<MsgUsers> msgUsersList = msgUsersService.selectUserIdsByMsgId(msgIds);
        List<MsgTask> msgTaskList = msgTaskService.list(new LambdaQueryWrapper<MsgTask>().in(MsgTask::getMsgId, msgIds));

        // 构建消息ID到接收人的映射
        Map<String, List<String>> msgToUsersMap = msgUsersList.stream()
                .collect(Collectors.groupingBy(MsgUsers::getMsgId,
                        Collectors.mapping(MsgUsers::getUserId, Collectors.toList())));

        // 构建任务消息ID到接收人的映射
        Map<String, String> msgTaskUserMap = msgTaskList.stream()
                .collect(Collectors.toMap(MsgTask::getMsgId, MsgTask::getReceiver, (v1, v2) -> v1));

        // 为每条消息设置接收人
        records.forEach(msgInfo -> {
            String msgId = msgInfo.getId();
            // 优先使用任务中的接收人信息
            if (msgTaskUserMap.containsKey(msgId)) {
                msgInfo.setReceiver(msgTaskUserMap.get(msgId));
            } else {
                List<String> userIds = msgToUsersMap.getOrDefault(msgId, Collections.emptyList());
                msgInfo.setReceiver(String.join(",", userIds));
            }
        });
        infoPage.setRecords(records);
        return infoPage;
    }

    @Override
    public void updateStateAndSendTime(String id, int status) {
        Date now = new Date();
        baseMapper.updateStateAndSendTime(id, status, now);
    }

    //----------------------MsgReadMark-----------------------

    @Override
    public void markAsRead(String msgId, String userId) {
        msgReadMarkService.markAsRead(msgId, userId);
    }

    @Override
    public void markBatchAsRead(List<String> msgIds, String alermType, String userId) {
        if (alermType != null) {
            //已读某一类型的消息
            List<String> msgIdList = this.getUnreadMessages(userId, alermType).stream().map(MsgInfo::getId).collect(Collectors.toList());
            msgReadMarkService.markBatchAsRead(msgIdList, userId);
        } else if (msgIds == null) {
            //已读所有消息
            List<String> msgIdList = this.getUnreadMessages(userId, null).stream().map(MsgInfo::getId).collect(Collectors.toList());
            msgReadMarkService.markBatchAsRead(msgIdList, userId);
        } else {
            //已读指定消息
            msgReadMarkService.markBatchAsRead(msgIds, userId);
        }

    }

    @Override
    public void markBatchAsRead(String msgIds, List<String> userId) {
        msgReadMarkService.markBatchAsRead(msgIds, userId);
    }

    @Override
    public ClassifyMessageNumber getClassifyMessageNumber(String userId) {
        return msgReadMarkService.getClassifyMessageNumber(userId);
    }

    @Override
    public void sendMsgByTemplate(List<String> msgCodes, List<String> jsonParamsList, List<String> receivers) {
        msgTemplateService.sendMsgByTemplate(msgCodes, jsonParamsList, receivers);
    }
}
