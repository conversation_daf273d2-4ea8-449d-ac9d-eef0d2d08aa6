package com.hxdi.gateway.filter;

import cn.hutool.core.collection.ConcurrentHashSet;
import com.google.common.collect.Maps;
import com.hxdi.common.core.constants.CommonConstants;
import com.hxdi.common.core.constants.ErrorCode;
import com.hxdi.common.core.exception.BaseSignatureException;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.utils.SignatureUtil;
import com.hxdi.gateway.configuration.ApiProperties;
import com.hxdi.gateway.exception.JsonSignatureDeniedHandler;
import com.hxdi.gateway.filter.context.GatewayContext;
import com.hxdi.gateway.service.feign.SystemAppServiceClient;
import com.hxdi.system.client.model.entity.SystemApp;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.security.oauth2.provider.token.store.redis.JdkSerializationStrategy;
import org.springframework.security.oauth2.provider.token.store.redis.RedisTokenStoreSerializationStrategy;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.server.ServerWebExchange;
import org.springframework.web.server.WebFilter;
import org.springframework.web.server.WebFilterChain;
import reactor.core.publisher.Mono;

import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2023/8/12 10:29
 * @description 开放接口鉴权-签名
 * @version 1.0
 */
public class PreSignatureFilter implements WebFilter {
    private JsonSignatureDeniedHandler signatureDeniedHandler;
    private SystemAppServiceClient systemAppServiceClient;
    private ApiProperties apiProperties;
    private static final AntPathMatcher pathMatch = new AntPathMatcher();

    private final Set<String> signUrls = new ConcurrentHashSet<>();

    private RedisConnectionFactory redisConnectionFactory;

    private RedisTokenStoreSerializationStrategy serializationStrategy = new JdkSerializationStrategy();

    public PreSignatureFilter(SystemAppServiceClient systemAppServiceClient, ApiProperties apiProperties, JsonSignatureDeniedHandler signatureDeniedHandler, RedisConnectionFactory redisConnectionFactory) {
        this.apiProperties = apiProperties;
        this.systemAppServiceClient = systemAppServiceClient;
        this.signatureDeniedHandler = signatureDeniedHandler;
        this.redisConnectionFactory = redisConnectionFactory;

        if (apiProperties.getSignUrls() != null) {
            signUrls.addAll(apiProperties.getSignUrls());
        }
    }

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, WebFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        String requestPath = request.getURI().getPath();
        if (apiProperties.getCheckSign() && notIgnore(requestPath)) {
            try {
                Map<String, String> params = Maps.newHashMap();
                GatewayContext gatewayContext = exchange.getAttribute(GatewayContext.CACHE_GATEWAY_CONTEXT);
                // 排除文件上传
                if (gatewayContext != null) {
                    params = gatewayContext.getAllRequestData().toSingleValueMap();
                }
                // 验证请求参数
                SignatureUtil.validateParams(params);
                //开始验证签名
                if (systemAppServiceClient != null) {
                    String apiKey = params.get(CommonConstants.SIGN_API_KEY);
                    // 获取客户端信息
//                    String key = CommonConstants.APP_API_KEY_PREFIX + apiKey;
//                    if (redisUtil.hasKey(key)) {
//                        return (SystemApp) redisUtil.get(key);
//                    }
                    ResultBody<SystemApp> resultBody = systemAppServiceClient.getByApiKey(apiKey);
                    SystemApp app = resultBody.getData();
                    if (app == null) {
                        return signatureDeniedHandler.handle(exchange, new BaseSignatureException(ErrorCode.ACCESS_DENIED.getCode(), "无效的ApiKey"));
                    }
                    // 服务器验证签名结果
                    if (!SignatureUtil.validateSign(params, app.getSecretKey())) {
                        return signatureDeniedHandler.handle(exchange, new BaseSignatureException(ErrorCode.ACCESS_DENIED.getCode(), "签名验证失败!"));
                    }
                }
            } catch (Exception ex) {
                return signatureDeniedHandler.handle(exchange, new BaseSignatureException(ErrorCode.ERROR.getCode(), ex.getMessage()));
            }
        }
        return chain.filter(exchange);
    }

    /**
     * 是否忽略验证签名
     * @param requestPath
     * @return
     */
    protected boolean notIgnore(String requestPath) {
        for (String path : signUrls) {
            if (pathMatch.match(path, requestPath)) {
                return true;
            }
        }
        return false;
    }
}
