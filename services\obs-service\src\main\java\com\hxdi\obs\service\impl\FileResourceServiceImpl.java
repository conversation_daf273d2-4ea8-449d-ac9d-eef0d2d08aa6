package com.hxdi.obs.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.net.HttpHeaders;
import com.hxdi.common.core.exception.BizExp;
import com.hxdi.common.core.model.MessageDTO;
import com.hxdi.common.core.mybatis.base.service.impl.BaseServiceImpl;
import com.hxdi.common.core.properties.ObsProperties;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.common.core.utils.WebUtil;
import com.hxdi.common.core.utils.codec.MD5Util;
import com.hxdi.file.client.model.entity.VideoResource;
import com.hxdi.obs.component.FssTemplate;
import com.hxdi.obs.mapper.FileResourceMapper;
import com.hxdi.obs.service.FileResourceService;
import com.hxdi.obs.util.ObsUtil;
import com.hxdi.obs.util.dto.FileParams;
import com.hxdi.obs.util.dto.FileWriteResult;
import com.obs.services.model.ObjectListing;
import com.obs.services.model.ObjectMetadata;
import com.obs.services.model.ObsObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.MediaTypeFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/2/22 17:51
 * @description 文件管理实现
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Slf4j
public class FileResourceServiceImpl extends BaseServiceImpl<FileResourceMapper, VideoResource> implements FileResourceService {

    private static final long EXPIRE_DAYS = 1 * 24 * 60 * 60 * 1000L;

    private static final long TIME = System.currentTimeMillis();

    private FssTemplate fssTemplate;

    private ObsProperties obsProperties;

    private ObsUtil obsUtil;

    private static final List<String> SYNC_BIZ_WHITE_LIST = Arrays.asList("001", "003", "LNWL01");

    @Autowired
    public FileResourceServiceImpl(FssTemplate fssTemplate, ObsProperties obsProperties, ObsUtil obsUtil) {
        this.fssTemplate = fssTemplate;
        this.obsProperties = obsProperties;
        this.obsUtil = obsUtil;
    }

        public static void main(String[] args) {
        File localFile = new File("/Users/<USER>/Desktop/hiv00462.mp4");
        if (!localFile.exists()) {
            BizExp.pop("本地文件不存在");
        }

        System.out.println(localFile.getName());
        System.out.println(StringUtils.substringAfterLast(localFile.getName(), "."));
        System.out.println(localFile.length());
    }

    @Override
    public VideoResource saveAndStore(MultipartFile file) {
        VideoResource resource = new VideoResource();
        resource.setOriginalFilename(file.getOriginalFilename());
        resource.setExtname(StringUtils.substringAfterLast(file.getOriginalFilename(), "."));
        resource.setByteLen(file.getSize());

        try (InputStream is = file.getInputStream()) {
            // IO流只能一次性读取，通过byte[]缓存数据
            byte[] bytes = IOUtils.toByteArray(is);
            String etag = MD5Util.streamToMD5(new ByteArrayInputStream(bytes));
            // 检查文件是否已经上传过，避免重复上传
            Optional<VideoResource> optional = checkExists(etag);
            if (optional.isPresent()) {
                resource.setEtag(etag);
                resource.setPath(optional.get().getPath());
                resource.setContentType(optional.get().getContentType());
            } else {
                Optional<FileWriteResult> result = fssTemplate.storeObject(new ByteArrayInputStream(bytes), new FileParams(resource.getOriginalFilename()));
                if (!result.isPresent()) {
                    throw new IOException();
                }
                resource.setEtag(etag);
                resource.setPath(result.get().getObjectName());
                resource.setContentType(result.get().getContentType());
            }
        } catch (IOException e) {
            BizExp.pop("文件上传失败", e);
        }

        baseMapper.insert(resource);
        return resource;
    }

    @Override
    public VideoResource saveAndStore(String filePath) {
        VideoResource resource = new VideoResource();
        File localFile = new File(filePath);
        if (!localFile.exists()) {
            BizExp.pop("本地文件不存在");
        }

        resource.setOriginalFilename(localFile.getName());
        resource.setExtname(StringUtils.substringAfterLast(localFile.getName(), "."));
        resource.setByteLen(localFile.length());

        try {
            Optional<FileWriteResult> result = fssTemplate.multipartStoreObject(localFile, new FileParams(resource.getOriginalFilename()));
            if (!result.isPresent()) {
                throw new IOException();
            }
            resource.setEtag(trimEtag(result.get().getEtag()));
            resource.setPath(result.get().getObjectName());
            resource.setContentType(result.get().getContentType());
        } catch (IOException e) {
            BizExp.pop("文件上传失败", e);
        }

        baseMapper.insert(resource);
        return resource;
    }

    @Override
    public void clean(String[] resIds) {
        List<VideoResource> resources = baseMapper.selectBatchIds(Arrays.asList(resIds));
        resources.forEach(resource -> {
            long countRef = countByEtag(resource.getEtag());
            // 当引用计数大于1时，不删除目标文件
            if (countRef == 1L) {
                fssTemplate.removeObject(new FileParams(resource.getPath()));
            }

            baseMapper.deleteById(resource.getId());
        });
    }

    @Override
    public void download(String resId, HttpServletRequest request, HttpServletResponse response) {
        VideoResource resource = baseMapper.selectById(resId);
        if (resource == null) {
            BizExp.pop("资源不存在");
        }

        Optional<InputStream> optional = fssTemplate.loadObject(new FileParams(resource.getPath()));
        if (optional.isPresent()) {
            WebUtil.setFileDownloadHeader(response, resource.getOriginalFilename());
            WebUtil.write(response, optional.get());
        }
    }

    @Override
    public void getStream(String resId, HttpServletRequest request, HttpServletResponse response) {
        VideoResource resource = baseMapper.selectById(resId);
        if (resource == null) {
            BizExp.pop("资源不存在");
        }

        Optional<InputStream> optional = fssTemplate.loadObject(new FileParams(resource.getPath()));
        if (optional.isPresent()) {
            String encodedFilename = "";
            try {
                encodedFilename = URLEncoder.encode(resource.getOriginalFilename(), "UTF-8");
            } catch (Exception e) {
            }

            response.setHeader("Origin-Name", encodedFilename);
            response.setHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_OCTET_STREAM_VALUE);
            response.setCharacterEncoding("UTF-8");
            WebUtil.write(response, optional.get());
        }
    }

    @Override
    public String getSignedUrl(String resId) {
        VideoResource resource = baseMapper.selectResourcePathAndType(resId);
        Optional<String> optional = fssTemplate.getObjectSignedUrl(new FileParams(resource.getPath()));
        if (optional.isPresent()) {
            return optional.get();
        }

        return "";
    }

    @Override
    public String getAlarmSignedUrl(String alarmId) {
        VideoResource resource = baseMapper.selectVideoAlarmResource(alarmId);
        if (resource != null) {
            Optional<String> optional = fssTemplate.getObjectSignedUrl(new FileParams(resource.getPath()));
            if (optional.isPresent()) {
                return optional.get();
            }
        }

        return "";
    }

    @Override
    public void syncObsObjectsInfo(String prefix) {
        FileParams fileParams = new FileParams();
        fileParams.setObjectName(prefix);
        Optional<List<ObjectListing>> objectListingList = obsUtil.listObjects(fileParams);
        if (!objectListingList.isPresent()) {
            return;
        }
        for (ObjectListing objectListing : objectListingList.get()) {
            List<ObsObject> obsObjectList = objectListing.getObjects();
            List<VideoResource> videoResourceList = new ArrayList<>();
            for (ObsObject obsObject : obsObjectList) {
                String objectKey = obsObject.getObjectKey();
                String dirName = objectKey.substring(0, objectKey.lastIndexOf("/"));
                String originalFilename = objectKey.substring(objectKey.lastIndexOf("/") + 1);
                String[] dirArr = dirName.split("/");
                // 跳过文件夹信息同步到数据库、不符合业务名规范的文件
                if (StrUtil.isBlank(originalFilename) || dirArr.length <= 2 || !SYNC_BIZ_WHITE_LIST.contains(dirArr[2])) {
                    continue;
                }
                // 查询数据库中是否存在该文件，若存在则跳过该文件同步
                ObjectMetadata metadata = obsObject.getMetadata();
                String etag = trimEtag(metadata.getEtag());
                long count = this.count(Wrappers.<VideoResource>lambdaQuery().eq(VideoResource::getEtag, etag));
                if (count > 0) {
                    continue;
                }
                // 根据天翼云对象原始数据，设置实体类信息
                String biz = dirArr[2];
                VideoResource videoResource = new VideoResource();
                videoResource.setOriginalFilename(originalFilename);
                videoResource.setExtname(objectKey.substring(objectKey.lastIndexOf(".") + 1));
                videoResource.setUrl("");
                videoResource.setPath(objectKey);
                videoResource.setByteLen(metadata.getContentLength());
                videoResource.setEtag(etag);
                // 视频来源
                videoResource.setOrigin(dirArr[2]);
                // 搜索关键字：车牌号，设备编号
                if (dirArr.length >= 4) {
                    videoResource.setSearchKey(dirArr[3]);
                }
                String[] fileNameSplitArr = originalFilename.split("_");
                // 如果没有四层目录且文件名包含业务，则从文件名中解析 searchKey
                if (StrUtil.isBlank(videoResource.getSearchKey()) && fileNameSplitArr.length > 1) {
                    videoResource.setSearchKey(fileNameSplitArr[0]);
                }
                videoResource.setCreateId("");
                videoResource.setCreateTime(new Date());
                videoResource.setUpdateTime(new Date());
                videoResource.setTenantId("");
                // 根据不同业务的视频命名规范，解析不同字段值
                SimpleDateFormat sdf;
                switch (biz) {
                    case "001":
                        videoResource.setContentType(MediaTypeFactory.getMediaType(originalFilename)
                                .orElse(MediaType.valueOf("video/mp4")).toString());
                        // 视频记录时间
                        videoResource.setRecTime(null);
                        break;
                    case "003":
                        videoResource.setContentType(MediaTypeFactory.getMediaType(originalFilename)
                                .orElse(MediaType.valueOf("binary/octet-stream")).toString());
                        // 视频记录时间
                        if (fileNameSplitArr.length <= 2) {
                            videoResource.setRecTime(null);
                            break;
                        }
                        String dateStr1 = fileNameSplitArr[2];
                        sdf = new SimpleDateFormat("yyyyMMddHHmmss");
                        try {
                            videoResource.setRecTime(sdf.parse(dateStr1));
                        } catch (ParseException e) {
                            e.printStackTrace();
                        }
                        break;
                    case "LNWL01":
                        videoResource.setContentType(MediaTypeFactory.getMediaType(originalFilename)
                                .orElse(MediaType.valueOf("video/mp4")).toString());
                        // 从文件名长度判断是否包含设备编号，存在则设置searchKey为设备编号，不存在则保留searchKey为车牌号
                        if (fileNameSplitArr.length > 2) {
                            videoResource.setSearchKey(fileNameSplitArr[1]);
                        }
                        // 视频记录时间
                        if (fileNameSplitArr.length <= 2) {
                            videoResource.setRecTime(null);
                            break;
                        }
                        String dateStr2 = fileNameSplitArr[2];
                        sdf = new SimpleDateFormat("yyMMddHHmmss");
                        try {
                            videoResource.setRecTime(sdf.parse(dateStr2));
                        } catch (ParseException e) {
                            e.printStackTrace();
                        }
                        break;
                    default:
                        break;
                }
                videoResourceList.add(videoResource);
            }
            this.saveBatch(videoResourceList);
        }
    }

    @Override
    public void saveVideoRecUploadInfo(MessageDTO dto) {
        Map<String, Object> data = (Map<String, Object>) dto.getPayload();
        VideoResource resource = new VideoResource();
        resource.setOriginalFilename((String) data.get("originalFilename"));
        resource.setExtname(StringUtils.substringAfterLast(resource.getOriginalFilename(), "."));
        resource.setByteLen((Long) data.get("byteLen"));
        resource.setEtag(trimEtag((String) data.get("etag")));
        resource.setPath((String) data.get("objectKey"));
        resource.setContentType((String) data.get("contentType"));
        resource.setAlarmId((String) data.get("alarmId"));
        resource.setOrigin((String) data.get("origin"));
        resource.setRecTime((Date) data.get("alarmTime"));
        resource.setSearchKey((String) data.get("devSn"));
        resource.setTenantId((String) data.get("tenantId"));
        baseMapper.insert(resource);
    }

    /**
     * 检查文件对象是否已存在，避免重复上传浪费空间
     *
     * @param etag
     * @return
     */
    private Optional<VideoResource> checkExists(String etag) {
        return Optional.ofNullable(
                baseMapper.selectOne(Wrappers.<VideoResource>lambdaQuery()
                        .select(VideoResource::getPath, VideoResource::getEtag, VideoResource::getContentType)
                        .eq(VideoResource::getEtag, etag)
                        .orderByDesc(VideoResource::getUpdateTime)
                        .last("limit 1")));
    }

    /**
     * 检查资源引用情况
     *
     * @param etag
     * @return
     */
    private Long countByEtag(String etag) {
        return baseMapper.selectCount(Wrappers.<VideoResource>lambdaQuery().eq(VideoResource::getEtag, etag));
    }

    /**
     * 去掉多余的符号
     * @param etag
     * @return
     */
    private String trimEtag(String etag) {
        if (CommonUtils.isNotEmpty(etag) && etag.length() > 32) {
            etag = etag.substring(1, 33);
        }

        return etag;
    }

}
