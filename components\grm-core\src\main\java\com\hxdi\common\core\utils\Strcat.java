package com.hxdi.common.core.utils;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2019/12/12 1:43 下午
 * @description 字符串连接工具处理类
 * @version 1.0
 */
public class Strcat {

    private Strcat(){}

    public static Strcat.StrcatBuilder join(String ... s){
        return new StrcatBuilder().join(s);
    }

    public static Strcat.StrcatBuilder joinWithDelimiter(String delimiter, String ... s){
        return new StrcatBuilder(delimiter).join(s);
    }

    public static Strcat.StrcatBuilder joinWithDelimiterExt(String delimiter, String prefix, String suffix, String ... s){
        return new StrcatBuilder(delimiter, prefix, suffix).join(s);
    }

    public static class StrcatBuilder{

        private String delimiter;
        private String prefix;
        private String suffix;

        public StrcatBuilder(String delimiter) {
            this.delimiter = delimiter;
        }

        public StrcatBuilder(String delimiter, String prefix, String suffix) {
            this(delimiter);
            this.prefix = prefix;
            this.suffix = suffix;
        }

        public StrcatBuilder() {
        }

        private StringBuilder sb = new StringBuilder();

        public StrcatBuilder join(String ... s){

            if (delimiter != null){
                for (int i = 0; i < s.length; i++) {
                    append(delimiter);
                    append(s[i]);
                }

                if (sb.indexOf(delimiter) == 0){
                    sb.delete(0, delimiter.length());
                }
            } else {
                Arrays.stream(s).forEach(s1 -> append(s1));
            }

            return this;
        }

        private void append(String s){
            sb.append(s == null ? "" : s);
        }

        @Override
        public String toString(){
            if (CommonUtils.isNotBlank(prefix)) {
                sb.insert(0, prefix);
            }

            if (CommonUtils.isNotBlank(suffix)) {
                sb.append(suffix);
            }
            return sb.toString();
        }
    }

    public static void main(String[] args) {
        System.out.println(Strcat.joinWithDelimiter("###","hello", "world", null).join("!").toString());
        System.out.println(Strcat.joinWithDelimiterExt("#","(", ")","hello", "world", null).join("!").toString());
    }
}
