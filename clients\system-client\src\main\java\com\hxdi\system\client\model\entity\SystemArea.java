package com.hxdi.system.client.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.io.Serializable;

@Getter
@Setter
@ToString
@TableName("system_area")
public class SystemArea implements Serializable {

    private static final long serialVersionUID=1L;

    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    private String areaName;

    private String parentId;

    private String abbrName;

    private Integer level;

    private String cityCode;

    private String zipCode;

    private String mergerName;

    private String lon;

    private String lat;

    private String pinyin;

    private Integer sorts;

    @TableField(exist = false)
    private String uniqueId;

}
