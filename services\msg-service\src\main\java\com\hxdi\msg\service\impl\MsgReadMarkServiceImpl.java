package com.hxdi.msg.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.msg.client.model.vo.ClassifyMessageNumber;
import com.hxdi.msg.enums.MsgEnum;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hxdi.msg.mapper.MsgReadMarkMapper;
import com.hxdi.msg.client.model.entity.MsgReadMark;
import com.hxdi.msg.service.MsgReadMarkService;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Transactional(rollbackFor = Exception.class)
public class MsgReadMarkServiceImpl extends ServiceImpl<MsgReadMarkMapper, MsgReadMark> implements MsgReadMarkService{

    @Override
    public void markAsRead(String msgId, String userId) {
        if(isMessageRead(Collections.singletonList(msgId), Collections.singletonList(userId))>=1){
            return;
        }
        //标记消息为已读
        MsgReadMark msgReadMark = new MsgReadMark();
        msgReadMark.setMsgId(msgId);
        msgReadMark.setUserId(userId);
        msgReadMark.setReadTime(new Date());
        save(msgReadMark);
    }

    @Override
    public void markBatchAsRead(List<String> msgIds, String userId) {
        baseMapper.markBatchAsRead_1(msgIds, userId);
    }

    @Override
    public void markBatchAsRead(String msgIds, List<String> userIdList) {
        if(userIdList.contains(MsgEnum.MSG_USER_ALL.getCode())){
            baseMapper.markBatchAsRead_1(Collections.singletonList(msgIds), MsgEnum.MSG_USER_ALL.getCode());
        }else {
            Date now = new Date();
            List<MsgReadMark> msgReadMarks = userIdList.stream().map(id -> {
                MsgReadMark msgReadMark = new MsgReadMark();
                msgReadMark.setMsgId(msgIds);
                msgReadMark.setUserId(id);
                msgReadMark.setReadTime(now);
                return msgReadMark;
            }).collect(Collectors.toList());
            this.saveBatch(msgReadMarks);
        }
    }

    @Override
    public ClassifyMessageNumber getClassifyMessageNumber(String userId) {
        return baseMapper.selectClassifyMessageNumber(userId, MsgEnum.MSG_USER_ALL.getCode());
    }

    /**
     * 检查消息是否已读
     * @param msgId 消息ID
     * @param userId 用户ID
     * @return 是否已读
     */
    public int isMessageRead(List<String> msgId, List<String> userId) {
        return Math.toIntExact(baseMapper.selectCount(new QueryWrapper<MsgReadMark>()
                .in(CommonUtils.isNotEmpty(msgId),"MSG_ID", msgId)
                .in(CommonUtils.isNotEmpty(msgId),"USER_ID", userId)
        ));
    }
}
