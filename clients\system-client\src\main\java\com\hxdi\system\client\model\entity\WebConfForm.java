package com.hxdi.system.client.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * @ClassName: WebConfForm
 * @Author: *
 * @Description:前端配置表单配置数据表
 * @Date: Wed Jul 21 10:47:41 CST 2021
 */
@Getter
@Setter
@TableName("WEB_CONF_FORM")
public class WebConfForm implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "ID",type = IdType.AUTO)
    protected Integer id;

    @TableField("CODE")
    private String code;

    @TableField("FORM_GROUP")
    private String formGroup;

    @TableField("NAME")
    private String name;

    @TableField("CONFIG_DATA")
    private String configData;

    @TableField("FORM_TYPE")
    private String formType;

    @TableField("METHOD")
    private String method;

    @TableField("URL")
    private String url;

    @TableField("LAYOUT")
    private String layout;

    @TableField("LINK_DATA")
    private String linkData;

    @TableField("STATUS")
    protected String status;

    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    protected Date updateTime;

    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    protected Date createTime;

    @TableField("DATA_HIERARCHY_ID")
    private String dataHierarchyId;
}
