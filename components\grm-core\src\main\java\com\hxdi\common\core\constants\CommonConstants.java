package com.hxdi.common.core.constants;

/**
 * <AUTHOR>
 */
public interface CommonConstants {


    /**
     * 默认超级管理员账号
     */
    String ROOT = "su-adm";
    /**
     * 系统定时任务默认使用账号
     */
    String SCHEDULER = "scott";

    String WECHAT_PW_PREFIX = "WX_";

    String SEQ_ORG_ID = "SEQ_ORG_ID";

    /**
     * 默认最小页码
     */
    int MIN_PAGE = 0;
    /**
     * 最大显示条数
     */
    int MAX_LIMIT = 999;
    /**
     * 默认页码
     */
    int DEFAULT_PAGE = 1;
    /**
     * 默认显示条数
     */
    int DEFAULT_LIMIT = 10;
    /**
     * 页码 KEY
     */
    String PAGE_KEY = "page";
    /**
     * 显示条数 KEY
     */
    String PAGE_LIMIT_KEY = "limit";
    /**
     * 排序字段 KEY
     */
    String PAGE_SORT_KEY = "sort";
    /**
     * 排序方向 KEY
     */
    String PAGE_ORDER_KEY = "order";

    /**
     * 客户端ID KEY
     */
    String SIGN_API_KEY = "ApiKey";

    /**
     * 客户端秘钥 KEY
     */
    String SIGN_SECRET_KEY = "SecretKey";

    /**
     * 随机字符串 KEY
     */
    String SIGN_NONCE_KEY = "Nonce";
    /**
     * 时间戳 KEY
     */
    String SIGN_TIMESTAMP_KEY = "Timestamp";
    /**
     * 签名类型 KEY
     */
    String SIGN_SIGN_TYPE_KEY = "SignType";
    /**
     * 签名结果 KEY
     */
    String SIGN_SIGN_KEY = "Sign";

    /**
     * 资源扫描
     */
    String API_RESOURCE = "hxc_api_resource";

    /**
     * 访问日志
     */
    String API_ACCESS_LOGS = "queue.api.access.logs";

    String AMQ_ERROR_TOPIC = "error.topic";

    String QUEUE_ERROR = "queue.error.bus";

    String ERROR_key = "error.key";

    /**
     * 业务服务中通常可以快速处理的消息
     */
    String QUEUE_COMMON_BIZ = "queue.common.biz";

    /**
     * 业务服务中消息体通常比较大，计算耗时消息处理
     */
    String QUEUE_DELAY_BIZ = "queue.delay.biz";

    /**
     * 在线用户统计
     */
    String QUEUE_ONLINE_STAT = "queue.online.stat";

    /**
     * ODX消息总线队列
     */
    String QUEUE_ODX_MESSAGE_BUS = "queue.odx.message.bus";

    /**
     * ODX业务信息消息总线队列
     */
    String QUEUE_ODX_BUZ_MESSAGE_BUS = "queue.odx.buz.message.bus";

    /**
     * 视频服务消息总线队列
     */
    String QUEUE_VIDEO_REC_MESSAGE_BUS = "queue.video.rec.message.bus";

    /**
     * 对象存储服务消息总线队列
     */
    String QUEUE_OBS_MESSAGE_BUS = "queue.obs.message.bus";


    /**
     * 系统通知消息总线队列
     */
    String QUEUE_SYSTEM_MSG = "queue.system.message";

    /**
     * 登录日志
     */
    String ACCOUNT_LOGS = "account_logs";

    String CODE_TABLE = "code_table";

    String ORGAN_INFOS = "organ_infos";

    String USER_INFOS = "user_infos";

    String DATA_SCOPE = "data_scope";

    String APP_PREFIX = "hxc_app:";
    String APP_API_KEY_PREFIX = "hxc_app_api_key:";
    String APP_CLIENT_PREFIX = "hxc_app_client:";

    String SYSTEM_VERSION = "SYSTEM_VERSION";

    /**
     * redis过期监听 - 业务类型/业务场景
     */
    String SYSTEM_MSG_TEMPLATE_SEND = "systemMsgTemplate/send/";


    String DEFAULT_DATE_TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";
    String DEFAULT_DATE_FORMAT = "yyyy-MM-dd";
    String DEFAULT_TIME_FORMAT = "HH:mm:ss";

    String TIME_ZONE = "GMT+8";

    String RELEASE_VERSION = "release";
    String TEST_VERSION = "test";
    String BETA_VERSION = "beta";


    String CLASSIFICATION_RESOURCE = "classification_resource";

    String CATALOG_RESOURCE = "catalog_resource";

    String ORGANIZATION_RESOURCE = "organization_resource";

    String STOREHOUSE_RESOURCE = "storehouse_resource";

    String STORE_LOCATION_RESOURCE = "store_location_resource";

    //数据同步交换机
    String MQ_DATASYNC_EXCHANGE = "nmjl_data_synchronization";

    //数据同步key
    String MQ_BASE_DATASYNC_KEY = "mnjl.basic";
    String MQ_BUSINESS_DATASYNC_KEY = "mnjl.business";

    // 数据同步消息总线(基础服务——>业务服务)
    String MQ_BASE_QUEUE_BUS = "mnjl_basic_queue";
    // 数据同步消息总线(业务服务——>基础服务)
    String MQ_BUSINESS_QUEUE_BUS = "mnjl_business_queue";
}
