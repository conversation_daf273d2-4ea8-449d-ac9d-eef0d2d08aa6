package com.hxdi.obs.controller;

import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.mybatis.base.controller.BaseController;
import com.hxdi.file.client.model.entity.FileResource;
import com.hxdi.file.client.model.entity.VideoResource;
import com.hxdi.obs.service.FileResourceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @date 2023/2/22 16:35
 * @description 文件资源管理
 * @version 1.0
 */
@Api(tags = "文件资源管理")
@RestController
@RequestMapping("/video")
public class FileResourceController extends BaseController<FileResourceService, VideoResource> {

    @ApiOperation("上传文件")
    @PostMapping("/upload")
    public ResultBody<FileResource> uploadFile(@RequestParam("file") MultipartFile file) {
        return ResultBody.ok().data(bizService.saveAndStore(file));
    }

    @ApiOperation("本地大文件上传")
    @PostMapping("/upload/local/file")
    public ResultBody<FileResource> uploadLocalLargeFile(@RequestParam("filePath") String filePath) {
        return ResultBody.ok().data(bizService.saveAndStore(filePath));
    }

    @ApiOperation("删除资源")
    @DeleteMapping("/remove")
    public ResultBody remove(@RequestParam("resIds") String resIds) {
        bizService.clean(StringUtils.commaDelimitedListToStringArray(resIds));
        return ResultBody.ok();
    }

    @ApiOperation("下载文件")
    @GetMapping("/download")
    public void download(@RequestParam("resId") String resId, HttpServletRequest request, HttpServletResponse response) {
        bizService.download(resId, request, response);
    }

    @ApiOperation("获取授权URL")
    @GetMapping("/signedUrl")
    public ResultBody<String> getSignedUrl(@RequestParam("resId") String resId) {
        return ResultBody.ok().data(bizService.getSignedUrl(resId));
    }

    @ApiOperation("获取告警URL")
    @GetMapping("/alarm/signedUrl")
    ResultBody<String> getAlarmSignedUrl(@RequestParam("alarmId") String alarmId) {
        return ResultBody.ok().data(bizService.getAlarmSignedUrl(alarmId));
    }

    @ApiOperation("读取流")
    @GetMapping("/stream")
    public void getStream(@RequestParam("resId") String resId, HttpServletRequest request, HttpServletResponse response) {
        bizService.getStream(resId, request, response);
    }
}
