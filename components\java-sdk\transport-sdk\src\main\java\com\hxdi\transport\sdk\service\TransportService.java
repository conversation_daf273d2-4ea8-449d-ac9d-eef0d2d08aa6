package com.hxdi.transport.sdk.service;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.hxdi.transport.sdk.constant.Constants;
import com.hxdi.transport.sdk.property.ApiProperties;
import com.hxdi.transport.sdk.sign.SignCommonUtil;
import com.hxdi.transport.sdk.util.HttpsUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2023/3/7 14:17
 * @description 物流服务调用类
 * @version 1.0
 */
@Slf4j
public class TransportService {

    private static final String CID = "cid";
    private static final String SRT = "srt";
    private static final String SIGN = "sign";
    private static final String STATUS = "status";
    private static final String SUCCESS_CODE = "1001";
    private static final String SUCCESS_EMPTY_CODE = "1006";

    private int connectTimeout = 6000;
    private int readTimeout = 12000;

    private ApiProperties apiProperties;

    public TransportService(ApiProperties apiProperties) {
        this.apiProperties = apiProperties;
        if (apiProperties.getCid() == null || Constants.EMPTY.equals(apiProperties.getCid().trim())) {
            throw new IllegalArgumentException("clientId is required");
        }

        if (apiProperties.getSrt() == null || Constants.EMPTY.equals(apiProperties.getSrt().trim())) {
            throw new IllegalArgumentException("srt is required");
        }
    }

    public TransportService(ApiProperties apiProperties, int connectTimeout, int readTimeout) {
        this(apiProperties);
        this.connectTimeout = (connectTimeout > 0) ? connectTimeout : 6000;
        this.readTimeout = (readTimeout > 0) ? readTimeout : 12000;
    }

    public Optional<String> postHttps(String url, Map<String, Object> params) throws RuntimeException {
        if (url == null || Constants.EMPTY.equals(url.trim())
                || params == null || params.isEmpty()) {
            throw new IllegalArgumentException("url or param is required");
        }

        params.put(CID, apiProperties.getCid());
        params.remove(SRT);

        String sign = SignCommonUtil.signWithParamsOnly(params, apiProperties.getSrt());
        params.put(SIGN, sign);

        Optional<String> respContent = HttpsUtils.doPost(buildUrl(url), buildFormData(params), this.connectTimeout, this.readTimeout);
        if (respContent.isPresent()) {
            JSONObject json = new JSONObject(respContent.get());
            String status = json.get(STATUS).toString();
            /*
             * 1001 : 请求成功
             * 1002 : 参数错误
             * 1006 : 无结果
             */
            if (SUCCESS_CODE.equals(status) || SUCCESS_EMPTY_CODE.equals(status)) {
                return respContent;
            }

            log.error("物流服务请求失败，{}", respContent.get());
            throw new RuntimeException(StrUtil.format("物流服务请求失败, {}: {}", json.get(STATUS), json.get("result")));
        }

        throw new RuntimeException("物流服务接口无响应");
    }

    private String buildUrl(String path) {
        if (!path.startsWith(Constants.SEPARATOR)) {
            path = Constants.SEPARATOR + path;
        }
        return apiProperties.getBaseUrl() + path;
    }

    private String buildFormData(Map<String, Object> params) {
        StringBuilder sb = new StringBuilder(200);
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            sb.append(entry.getKey())
                .append("=")
                .append(entry.getValue().toString())
                .append("&");
        }
        sb.deleteCharAt(sb.length() - 1);
        return sb.toString();
    }
}
