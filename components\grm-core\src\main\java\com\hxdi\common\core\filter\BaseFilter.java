package com.hxdi.common.core.filter;


import com.hxdi.common.core.security.SecurityHelper;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

/**
 * <AUTHOR>
 */
public class BaseFilter implements Filter {

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {

    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        HttpServletRequest req = (HttpServletRequest) request;
        BaseServletRequestWrapper xssRequestWrapper = new BaseServletRequestWrapper(req);
        chain.doFilter(xssRequestWrapper, response);
        // 请求结束，清理用户信息缓存
        SecurityHelper.cleanUserHolder();
    }

    @Override
    public void destroy() {

    }


}
