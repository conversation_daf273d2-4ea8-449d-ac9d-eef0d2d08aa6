package com.hxdi.system.client.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.hxdi.common.core.constants.CommonConstants;
import com.hxdi.common.core.mybatis.base.entity.AbstractEntity;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.common.core.utils.support.ICache;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * <p>
 * 组织机构
 * </p>
 */
@Getter
@Setter
@TableName("system_organ")
public class SystemOrgan extends AbstractEntity {

    @TableId(type = IdType.INPUT)
    private String id;

    /**
     * 组织名称
     */
    @NotEmpty(message = "组织名称不能为空")
    private String name;

    /**
     * 组织类型：1-管理单位，2-军供站
     */
    private Integer type;

    /**
     * 组织编码
     */
    private String code;

    /**
     * 父节点
     */
    private String parentId;

    /**
     * 联系人
     */
    private String contact;

    /**
     * 联系点好
     */
    private String tel;

    private Integer level;

    /**
     * 0-禁用，1-启用
     */
    private Integer state;

    /**
     * 描述
     */
    private String remark;

    /**
     * 排序
     */
    private Integer seq;

    /**
     * 组织路径：xxx-xxx-xxx
     */
    private String organPath;

    @TableField(fill = FieldFill.INSERT)
    public String createId;
    @TableField(fill = FieldFill.INSERT_UPDATE)
    public String updateId;

    private String tenantId;

    @Override
    public Serializable pkVal() {
        return this.id;
    }


    /**
     * ---------------
     */

    @TableField(exist = false)
    private String parentName;

    public SystemOrgan(){}

    public SystemOrgan(String id, Integer seq) {
        this.id = id;
        this.seq = seq;
    }
}
