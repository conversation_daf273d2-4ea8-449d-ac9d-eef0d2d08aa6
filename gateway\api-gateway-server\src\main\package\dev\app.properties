#\u5E94\u7528\u4FE1\u606F
app.port=8888
profile.name=dev
config.server-addr=10.13.4.20:8848
discovery.server-addr=10.13.4.20:8848
config.namespace=afdfe253-628c-493a-96b0-fa67e9698a7c
config.group=DEFAULT_GROUP
auth.username=
auth.password=

access.control=false
api.debug=true
endpoints.enable=true


#ignores.url= \n\
#    \u0020\u0020\u0020\u0020\u0020\u0020- /*/login/** \n\
#    \u0020\u0020\u0020\u0020\u0020\u0020- /*/logout/** \n\
#    \u0020\u0020\u0020\u0020\u0020\u0020- /*/oauth/** \n\
#    \u0020\u0020\u0020\u0020\u0020\u0020- /*/file/** \n\
#    \u0020\u0020\u0020\u0020\u0020\u0020- /*/thirdparty/registry \n\
#    \u0020\u0020\u0020\u0020\u0020\u0020- /*/stockSummaryReport/** \n\
#    \u0020\u0020\u0020\u0020\u0020\u0020- /*/cycleReport/** \n\
#    \u0020\u0020\u0020\u0020\u0020\u0020- /*/emmPurchaseMonthReport/** \n\
#    \u0020\u0020\u0020\u0020\u0020\u0020- /*/emmPurchaseStateReport/** \n\
#    \u0020\u0020\u0020\u0020\u0020\u0020- /*/emmTransitionReport/** \n\
#    \u0020\u0020\u0020\u0020\u0020\u0020- /*/providerProductReport/** \n\
#    \u0020\u0020\u0020\u0020\u0020\u0020- /*/storeMonthReport/** \n\
#    \u0020\u0020\u0020\u0020\u0020\u0020- /*/wmsStockSummaryReport/** \n\
#    \u0020\u0020\u0020\u0020\u0020\u0020- /*/ureport/designer \n\
#    \u0020\u0020\u0020\u0020\u0020\u0020- /*/tag/getDetail \n\
#    \u0020\u0020\u0020\u0020\u0020\u0020- /*/userConfig/** \n\
#    \u0020\u0020\u0020\u0020\u0020\u0020- /*/issueConfirmedListSurplus/IssuseByRfId \n\
#    \u0020\u0020\u0020\u0020\u0020\u0020- /*/data/** \n\
#    \u0020\u0020\u0020\u0020\u0020\u0020- /*/bi/ureport/** \n\
#    \u0020\u0020\u0020\u0020\u0020\u0020- /*/majorEventReport/getMajorEventList/** \n\
