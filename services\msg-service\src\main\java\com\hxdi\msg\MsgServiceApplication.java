package com.hxdi.msg;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableScheduling;


/**
 * <AUTHOR>
 */
@EnableFeignClients
@EnableDiscoveryClient
@SpringBootApplication
@EnableScheduling
@MapperScan(basePackages = "com.hxdi.msg.mapper")
public class MsgServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(MsgServiceApplication.class, args);
    }

//    @Autowired
//    private EmailConfigService emailConfigService;
//
//    @Override
//    public void run(String... strings) throws Exception {
//        emailConfigService.loadCacheConfig();
//    }
}
