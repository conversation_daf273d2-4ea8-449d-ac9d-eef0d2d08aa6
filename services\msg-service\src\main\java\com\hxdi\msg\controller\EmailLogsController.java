package com.hxdi.msg.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.model.PageParams;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.msg.client.model.entity.EmailLogs;
import com.hxdi.msg.service.EmailLogsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@Api(tags = "邮件发送日志")
@RestController
@RequestMapping("/emailLogs")
public class EmailLogsController {

    @Autowired
    private EmailLogsService targetService;

    @ApiOperation("获取分页数据")
    @GetMapping("/list")
    public ResultBody<Page<EmailLogs>> list(@RequestParam(required = false) Map map) {
        QueryWrapper<EmailLogs> queryWrapper = new QueryWrapper();
        return ResultBody.ok().data(targetService.page(new PageParams(map), queryWrapper));
    }

    @ApiOperation("根据ID查找数据")
    @RequestMapping("/get")
    public ResultBody<EmailLogs> get(@RequestParam("id") String id) {
        return ResultBody.ok().data(targetService.getById(id));
    }

}
