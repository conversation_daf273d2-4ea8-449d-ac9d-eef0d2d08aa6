package com.hxdi.system.client.service;

import com.hxdi.common.core.model.ResultBody;
import com.hxdi.system.client.model.entity.SystemAction;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 */
public interface ISystemActionServiceClient {


    /**
     * 获取功能按钮详情
     *
     * @param actionId
     * @return
     */
    @GetMapping("/action/info")
    ResultBody<SystemAction> get(@RequestParam("actionId") String actionId);

}
