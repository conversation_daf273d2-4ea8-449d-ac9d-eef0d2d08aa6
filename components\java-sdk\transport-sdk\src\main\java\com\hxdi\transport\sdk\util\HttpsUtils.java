package com.hxdi.transport.sdk.util;

import com.hxdi.transport.sdk.constant.Constants;
import lombok.extern.slf4j.Slf4j;

import javax.net.ssl.*;
import java.io.DataOutputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2023/3/7 09:14
 * @description
 * @version 1.0
 */
@Slf4j
public class HttpsUtils {

    public static Optional<String> doPost(String url, String param, int connectTimeout, int readTimeout) {
        InputStreamReader isReader = null;
        HttpURLConnection conn = null;
        DataOutputStream dos = null;
        StringBuffer result = new StringBuffer();
        try {
            trustAllHttpsCertificates();
            HostnameVerifier hv = (urlHostName, session) -> {
                log.info("Warning: URL Host: " + urlHostName + " vs. " + session.getPeerHost());
                return true;
            };
            HttpsURLConnection.setDefaultHostnameVerifier(hv);
            conn = (HttpURLConnection) (new URL(url)).openConnection();
            conn.setDoInput(true);
            conn.setDoOutput(true);
            conn.setUseCaches(false);
            conn.setRequestProperty("Content-Length", String.valueOf(param.getBytes(Constants.CHARSET_UTF8).length));
            conn.setRequestProperty("charset", Constants.CHARSET_UTF8);
            conn.setRequestMethod(Constants.HTTP_POST);
            conn.setConnectTimeout(connectTimeout);
            conn.setReadTimeout(readTimeout);
            conn.connect();
            dos = new DataOutputStream(conn.getOutputStream());
            int length = 0;
            int totalLength = param.length();
            while (length < totalLength) {
                int endLength = length + 1024;
                if (endLength > totalLength){
                    endLength = totalLength;
                }

                dos.write(param.substring(length, endLength).getBytes(Constants.CHARSET_UTF8));
                length = endLength;
                dos.flush();
            }
            dos.close();
            isReader = new InputStreamReader(conn.getInputStream(), Constants.CHARSET_UTF8);
            char[] buffer = new char[2048];
            int pos = 0;
            while ((pos = isReader.read(buffer)) != -1) {
                String temp = new String(buffer, 0, pos);
                result.append(temp);
            }
            return Optional.of(result.toString());
        } catch (Exception e) {
            log.error("调用物流服务接口,网络出现异常!");
            log.error(e.getMessage(), e);
        } finally {
            try {
                if (isReader != null) {
                    isReader.close();
                }
            } catch (IOException ex) {
                log.error("关闭数据流出错了", ex);
            }
        }

        return Optional.empty();
    }

    private static void trustAllHttpsCertificates() throws Exception {
        TrustManager[] trustAllCerts = new TrustManager[1];
        TrustManager tm = new CustomTrustManager();
        trustAllCerts[0] = tm;
        SSLContext sc = SSLContext.getInstance("SSL");
        sc.init(null, trustAllCerts, null);
        HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());
    }

    static class CustomTrustManager implements X509TrustManager {
        @Override
        public X509Certificate[] getAcceptedIssuers() {
            return null;
        }

        @Override
        public void checkServerTrusted(X509Certificate[] certs, String authType) throws CertificateException {
        }

        @Override
        public void checkClientTrusted(X509Certificate[] certs, String authType) throws CertificateException {
        }
    }
}
