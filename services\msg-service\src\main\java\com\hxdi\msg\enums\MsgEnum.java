package com.hxdi.msg.enums;

import lombok.Getter;

/**
 * @program: hxdicloud_2.0
 * @description: 消息
 * @author: 王贝强
 * @create: 2025-06-27 16:17
 */
@Getter
public enum MsgEnum{
    MSG_TYPE_SITE("SITE","站内信"),
    MSG_TYPE_EMAIL("EMAIL","邮件"),
    MSG_TYPE_SMS("SMS","短信"),
    MSG_USER_ALL("all","所有用户");

    private final String code;
    private final String desc;

    MsgEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
