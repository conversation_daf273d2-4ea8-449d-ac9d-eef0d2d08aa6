package com.hxdi.common.core.converter;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import org.springframework.core.convert.converter.Converter;

import java.util.Date;

/**
 * 字符串转日期转换器
 * <AUTHOR>
 */
public enum StringToDateConverter implements Converter<String, Date> {

    INSTANCE;

    @Override
    public Date convert(String source) {
        if (StrUtil.isEmpty(source)) {
            return null;
        }

        return DateUtil.parse(source.trim());
    }

}

