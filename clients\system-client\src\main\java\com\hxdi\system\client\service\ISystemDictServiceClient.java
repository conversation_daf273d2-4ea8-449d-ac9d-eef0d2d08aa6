package com.hxdi.system.client.service;

import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.model.cache.CodeTable;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

public interface ISystemDictServiceClient {

    /**
     * 根据字典项编码获取字典值列表
     */
    @GetMapping("/dict/list/table")
    ResultBody<List<CodeTable>> gitDict(@RequestParam("code") String code);
}
