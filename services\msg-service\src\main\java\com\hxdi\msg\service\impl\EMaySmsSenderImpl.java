package com.hxdi.msg.service.impl;

import cn.emay.eucp.inter.http.v1.dto.request.SmsSingleRequest;
import cn.emay.util.AES;
import cn.emay.util.JsonHelper;
import cn.emay.util.http.*;
import cn.hutool.core.util.StrUtil;
import com.hxdi.common.core.utils.JsonConverter;
import com.hxdi.msg.client.model.MsgSmsLog;
import com.hxdi.msg.client.model.SmsMessage;
import com.hxdi.msg.service.IMsgSmsLogService;
import com.hxdi.msg.service.SmsSender;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * 亿美短信平台
 */
@Slf4j
public class EMaySmsSenderImpl implements SmsSender {

    private static final String APP_ID = "EUCP-EMY-SMS1-6BNBX";
    private static final String SECRET_KEY = "EEEA7F26B5256304";
    private static final String HOST = "bjmtn.b2m.cn";
    private static final String ALGORITHM = "AES/ECB/PKCS5Padding";
    private static final String ENCODE = "UTF-8";
    private static final Map<String, String> TEMPLATES = new HashMap<String, String>(){
        {
            put("SMS-1000", "【浙江省应急物资平台】你的验证码是：{code}，5分钟内有效");
            put("SMS-1001", "【浙江省应急物资平台】{content}");
        }
    };

    /**
     * 发送短信接口
     */
    private static final String SINGLE_SMS_URL = "http://" + HOST + "/inter/sendSingleSMS";

    @Autowired
    private IMsgSmsLogService msgSmsLogService;

    @Override
    public Boolean send(SmsMessage parameter) {
        SmsSingleRequest smsRequest = new SmsSingleRequest();
        smsRequest.setRequestTime(System.currentTimeMillis());
        smsRequest.setRequestValidPeriod(3600);
        smsRequest.setMobile(parameter.getPhoneNum());
        String tplContent = parameter.getTemplate();
        HashMap<String, String> tplParamsMap = JsonConverter.parse(parameter.getTplParams(), HashMap.class);
        String content = StrUtil.format(tplContent, tplParamsMap);
        smsRequest.setContent(content);
        EmayHttpRequestBytes request;
        try {
            Map<String, String> headers = new HashMap<>();
            headers.put("appId", APP_ID);
            headers.put("encode", ENCODE);
            String requestJson = JsonHelper.toJsonString(smsRequest);
            byte[] bytes = requestJson.getBytes(ENCODE);
            byte[] encBytes = AES.encrypt(bytes, SECRET_KEY.getBytes(), ALGORITHM);
            request = new EmayHttpRequestBytes(SINGLE_SMS_URL, ENCODE, "POST", headers, null, encBytes);
        } catch (Exception e) {
            log.error("building EmayHttpRequestBytes Instance error", e);
            return false;
        }

        EmayHttpClient client = new EmayHttpClient();
        EmayHttpResponseBytes response = client.service(request, new EmayHttpResponseBytesPraser());
        if (response.getResultCode().equals(EmayHttpResultCode.SUCCESS)) {
            String code = response.getHeaders().get("result");
            if ("SUCCESS".equals(code)){
                log.info("短信发送成功: {}#{}", parameter.getPhoneNum(), content);
                MsgSmsLog msgSmsLog=new MsgSmsLog();
                msgSmsLog.setCreataTime(new Date());
                msgSmsLog.setContent(content);
                msgSmsLog.setBuzId(parameter.getBuzId());
                msgSmsLog.setPhoneNumber(parameter.getPhoneNum());
                msgSmsLog.setReceveName(parameter.getReceveName());
                msgSmsLog.setTplId(parameter.getTplCode());
                msgSmsLogService.save(msgSmsLog);

                return true;
            }

            log.error("发送短信内部错误，状态：{}", code);
            if ("ERROR_BALANCE".equals(code)){
                log.error("当前余额不足");
            }
        } else {
            log.error("发送短信请求失败：{}", response.getResultCode().getName());
        }
        return false;
    }
}

