package com.hxdi.system.client.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hxdi.system.client.model.entity.SystemOrgan;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

@Getter
@Setter
public class WarehouseOrganInfo extends SystemOrgan {
//    库点分类、库点类型、经度、纬度、存储面积、建设时间、启用时间
    private String warehouseLevel;

    private String warehouseType;

    private String warehouseNature;

    private String lng;

    private String lat;

    private BigDecimal storageArea;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date enableDate;

}
