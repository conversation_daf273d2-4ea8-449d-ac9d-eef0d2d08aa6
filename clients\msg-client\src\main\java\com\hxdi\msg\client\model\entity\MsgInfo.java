package com.hxdi.msg.client.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "MSG_INFO")
public class MsgInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 提醒方式:1-待办，2-预警，3-提醒
     */
    @TableField(value = "ALERM_TYPE")
    @ApiModelProperty(value = "提醒方式:1-待办，2-预警，3-提醒")
    private Integer alermType;

    /**
     * 跳转地址
     */
    @TableField(value = "URLS")
    private String urls;

    /**
     * 标题
     */
    @TableField(value = "TITLE")
    @ApiModelProperty(value = "标题")
    private String title;

    /**
     * 消息内容
     */
    @TableField(value = "CONTENT")
    @ApiModelProperty(value = "消息内容")
    private String content;

    /**
     * 自动已读
     */
    @TableField(value = "AUTO_READ")
    @ApiModelProperty(value = "自动已读")
    private Integer autoRead;

    /**
     * 发布人ID
     */
    @TableField(value = "PUBLISHER")
    @ApiModelProperty(value = "发布人（UserId）")
    private String publisher;

    /**
     * 发布人名称
     */
    @TableField(value = "PUBLISHER_NAME")
    @ApiModelProperty(value = "发布人（UserName）")
    private String publisherName;

    /**
     * 接收对象类型: 1-全部，2-个人
     */
    @TableField(value = "RCV_TYPE")
    @ApiModelProperty(value = "接收对象类型: 1-全部，2-个人")
    private Integer rcvType;

    /**
     * 接收人
     */
    @TableField(value = "RECEIVER", exist = false)
    @ApiModelProperty(value = "接收人(UserId)：','分割，仅在查询发布的消息时使用")
    private String receiver;

    @TableField(value = "READ_TIME", exist = false)
    @ApiModelProperty(value = "已读时间（如果字段为空则未读，仅在查询所有消息时使用）")
    private Date readTime;

    /**
     * 计划发送时间
     */
    @TableField(value = "SCHEDULED_TIME")
    @ApiModelProperty(value = "计划发送时间")
    private Date scheduledTime;

    /**
     * 实际发送时间
     */
    @TableField(value = "SEND_TIME")
    @ApiModelProperty(value = "实际发送时间")
    private Date sendTime;

    /**
     * 发送状态：0-待发送，1-成功，2-失败
     */
    @TableField(value = "STATE")
    @ApiModelProperty(value = "发送状态：0-待发送，1-成功，2-失败")
    private Integer state;

    /**
     * 状态（0无效，1有效）
     */
    @TableField(value = "ENABLED")
    @ApiModelProperty(value = "状态（0无效，1有效）")
    private Integer enabled;

    /**
     * 创建时间
     */
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 创建id
     */
    @TableField(value = "CREATE_ID", fill = FieldFill.INSERT)
    private String createId;

    /**
     * 更新id
     */
    @TableField(value = "UPDATE_ID", fill = FieldFill.INSERT_UPDATE)
    private String updateId;

    /**
     * 租户id
     */
    @TableField(value = "TENANT_ID", fill = FieldFill.INSERT)
    private String tenantId;

    /**
     * 组织
     */
    @TableField(value = "DATA_HIERARCHY_ID", fill = FieldFill.INSERT)
    private String dataHierarchyId;

    @Override
    public String toString() {
        return "MsgInfo{" +
                "id='" + id + '\'' +
                ", alermType=" + alermType +
                ", title='" + title + '\'' +
                ", content='" + content + '\'' +
                ", autoRead=" + autoRead +
                ", publisher='" + publisher + '\'' +
                ", rcvType=" + rcvType +
                ", scheduledTime=" + scheduledTime +
                ", sendTime=" + sendTime +
                ", state=" + state +
                ", enabled=" + enabled +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime + '}';
    }
}