<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>components</artifactId>
        <groupId>com.hxdi</groupId>
        <version>1.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>grm-watermark-starter</artifactId>
    <version>1.0.0</version>
    <packaging>jar</packaging>

   <dependencies>
       <dependency>
           <groupId>com.hxdi</groupId>
           <artifactId>grm-core</artifactId>
       </dependency>
       <dependency>
           <groupId>org.springframework.boot</groupId>
           <artifactId>spring-boot-configuration-processor</artifactId>
           <optional>true</optional>
       </dependency>
       <dependency>
           <groupId>com.itextpdf</groupId>
           <artifactId>itextpdf</artifactId>
           <version>5.5.13.3</version>
       </dependency>
   </dependencies>

</project>
