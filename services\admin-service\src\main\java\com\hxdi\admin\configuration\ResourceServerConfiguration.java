package com.hxdi.admin.configuration;

import com.hxdi.common.core.constants.CommonConstants;
import com.hxdi.common.core.exception.BaseAccessDeniedHandler;
import com.hxdi.common.core.exception.BaseAuthenticationEntryPoint;
import com.hxdi.common.core.security.BaseUserDetails;
import com.hxdi.common.core.utils.WebUtil;
import com.hxdi.system.client.constants.SystemConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.actuate.autoconfigure.security.servlet.EndpointRequest;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpHeaders;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.oauth2.config.annotation.web.configuration.EnableResourceServer;
import org.springframework.security.oauth2.config.annotation.web.configuration.ResourceServerConfigurerAdapter;
import org.springframework.security.oauth2.config.annotation.web.configurers.ResourceServerSecurityConfigurer;
import org.springframework.security.oauth2.provider.authentication.BearerTokenExtractor;
import org.springframework.security.oauth2.provider.token.TokenStore;
import org.springframework.security.web.authentication.AuthenticationFailureHandler;
import org.springframework.security.web.authentication.SavedRequestAwareAuthenticationSuccessHandler;
import org.springframework.security.web.authentication.logout.CookieClearingLogoutHandler;
import org.springframework.security.web.authentication.logout.SimpleUrlLogoutSuccessHandler;
import org.springframework.security.web.savedrequest.HttpSessionRequestCache;
import org.springframework.security.web.savedrequest.RequestCache;
import org.springframework.security.web.savedrequest.SavedRequest;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * oauth2资源服务器配置
 *
 * @author: liuyadu
 * @date: 2018/10/23 10:31
 * @description:
 */
@Slf4j
@Configuration
@EnableResourceServer
public class ResourceServerConfiguration extends ResourceServerConfigurerAdapter {

    @Autowired
    private TokenStore tokenStore;

    @Autowired
    private AmqpTemplate amqpTemplate;

    private BearerTokenExtractor tokenExtractor = new BearerTokenExtractor();

    @Value("${spring.application.name}")
    private String serviceId;

    @Override
    public void configure(ResourceServerSecurityConfigurer resources) {
        resources.resourceId(serviceId);
    }

    @Override
    public void configure(HttpSecurity http) throws Exception {
        http.sessionManagement().sessionCreationPolicy(SessionCreationPolicy.IF_REQUIRED)
                .and()
                .authorizeRequests()
                .antMatchers("/login/**", "/oauth/**", "/swagger-ui/index.html", "/captcha/**", "/app/wxcode2token", "/user/third-party/binding", "/check_token/token").permitAll()
                // 监控端点内部放行
                .requestMatchers(EndpointRequest.toAnyEndpoint()).permitAll()
                .anyRequest().authenticated()
                .and()
                .formLogin().loginPage("/login").loginProcessingUrl("/login")
                .successHandler(new LoginSuccessHandler())
                //.failureHandler(new LoginFailureHandler())
                .and()
                .logout().permitAll()
                // /logout退出清除cookie
                .addLogoutHandler(new CookieClearingLogoutHandler("token", "remember-me"))
                .logoutSuccessHandler(new LogoutSuccessHandler())
                .and()
                .exceptionHandling()
                // 认证鉴权错误处理,为了统一异常处理。每个资源服务器都应该加上。
                .accessDeniedHandler(new BaseAccessDeniedHandler())
                .authenticationEntryPoint(new BaseAuthenticationEntryPoint())
                .and()
                .csrf().disable()
                // 禁用httpBasic
                .httpBasic().disable();
    }

    public class LoginFailureHandler implements AuthenticationFailureHandler{

        @Override
        public void onAuthenticationFailure(HttpServletRequest request, HttpServletResponse response, AuthenticationException e) throws IOException, ServletException {
            response.getWriter().write("{\"msg\":\"登录失败，请检查用户名或者密码！\"}");
        }
    }

    public class LoginSuccessHandler extends SavedRequestAwareAuthenticationSuccessHandler {

        @Override
        public void onAuthenticationSuccess(HttpServletRequest request, HttpServletResponse response, Authentication authentication) throws IOException, ServletException {
            Map map = new HashMap();
            BaseUserDetails userDetails = (BaseUserDetails)authentication.getPrincipal();
            map.put("domain", SystemConstants.ACCOUNT_DOMAIN_SITE);
            map.put("userId", userDetails.getUserId());
            map.put("account", userDetails.getUsername());
            map.put("accountId", userDetails.getAccountId());
            map.put("accountType", userDetails.getAccountType());
            map.put("loginIp", WebUtil.getClientIP(request));
            map.put("loginAgent", request.getHeader(HttpHeaders.USER_AGENT));
            amqpTemplate.convertAndSend(CommonConstants.ACCOUNT_LOGS, map);

            RequestCache requestCache = new HttpSessionRequestCache();
            SavedRequest savedRequest = requestCache.getRequest(request,response);
            String url = "";
            if(savedRequest != null){
                url = savedRequest.getRedirectUrl();
                getRedirectStrategy().sendRedirect(request, response, url);
            }else{
                getRedirectStrategy().sendRedirect(request,response,"/login?error");
            }
        }
    }

    public class LogoutSuccessHandler extends SimpleUrlLogoutSuccessHandler {

        public LogoutSuccessHandler() {
            // 重定向到原地址
            super.setUseReferer(true);
        }

        @Override
        public void onLogoutSuccess(HttpServletRequest request, HttpServletResponse response, Authentication authentication) throws IOException, ServletException {
            try {
                // 解密请求头
                authentication = tokenExtractor.extract(request);
                if (authentication != null && authentication.getPrincipal() != null) {
                    String tokenValue = authentication.getPrincipal().toString();
                    log.debug("revokeToken tokenValue:{}", tokenValue);
                    // 移除token
                    tokenStore.removeAccessToken(tokenStore.readAccessToken(tokenValue));
                }
            } catch (Exception e) {
                log.error("revokeToken error:{}", e);
            }
            super.onLogoutSuccess(request, response, authentication);
        }
    }
}

