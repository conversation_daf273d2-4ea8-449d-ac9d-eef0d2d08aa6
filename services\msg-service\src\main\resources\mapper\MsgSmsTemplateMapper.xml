<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdicloud.msg.mapper.MsgSmsTemplateMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hxdi.msg.client.model.MsgSmsTemplate">
        <result column="tpl_id" property="tplId" />
        <result column="name" property="name" />
        <result column="code" property="code" />
        <result column="template" property="template" />
        <result column="params" property="params" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="status" property="status" />
        <result column="subject" property="subject" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        tpl_id, name, code, template, params, receive_type, receivers, receivers_name, create_time, update_time, status, subject, configurable
    </sql>


    <select id="pageList" resultType="com.hxdi.msg.client.model.MsgSmsTemplate">
      SELECT * FROM  msg_sms_template
      <where>
          status in ('1','2')
          <if test="@plugins.OGNL@isNotEmpty(condition.name)">
            and  name like CONCAT ('%',#{condition.name},'%')
          </if>
          <if test="@plugins.OGNL@isNotEmpty(condition.code)">
            and  code = #{condition.code}
          </if>
          <if test="@plugins.OGNL@isNotEmpty(condition.status)">
            and  status = #{condition.status}
          </if>
          <if test="@plugins.OGNL@isNotEmpty(condition.configurable)">
            and  configurable = #{condition.configurable}
          </if>
      </where>
    </select>

</mapper>
