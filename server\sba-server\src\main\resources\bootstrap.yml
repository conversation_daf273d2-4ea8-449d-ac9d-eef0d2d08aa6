server:
    port: 8849
spring:
    application:
        name: ${artifactId}
    cloud:
        nacos:
            config:
                namespace: ${config.namespace}
                server-addr: ${config.server-addr}
                group: ${config.group}
                shared-configs:
                    - dataId: common.properties
                      refresh: true
            discovery:
                namespace: ${config.namespace}
                server-addr: ${discovery.server-addr}
                # false:不注册该服务，仅限本地调试使用
                register-enabled: ${non.debug}
    profiles:
        active: ${profile.name}
    security:
        user:
            name: sba-admin
            password: sba-admin

management:
    endpoints:
        web:
            exposure:
                include: health,info
