package com.hxdi.moco.server;


import com.github.dreamhead.moco.bootstrap.Bootstrap;

/**
 * <AUTHOR>
 * @date 2023/3/17 15:44
 * @description
 * @version 1.0
 */
public class MocoApplication {

    public static void main(String[] args) {
        (new Bootstrap()).run(args);
//        final HttpServer server = MocoJsonRunner.jsonHttpServer(8060, Moco.pathResource("foo.json"));
//        final ServerRunner run = (ServerRunner) Runner.runner(server);
//        run.start();

//        System.out.println("moco程序启动成功!");
//        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
//            if (run != null) {
//                run.stop();
//                System.out.println("moco程序已退出!");
//            }
//        }));
    }
}
