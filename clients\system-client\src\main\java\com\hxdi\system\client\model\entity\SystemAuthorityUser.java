package com.hxdi.system.client.model.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hxdi.common.core.mybatis.base.entity.AbstractEntity;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
 * 系统权限-用户关联
 * <AUTHOR>
 */
@Getter
@Setter
@TableName("system_authority_user")
public class SystemAuthorityUser implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 权限ID
     */
    private String authorityId;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 过期时间
     */
    private Date expireTime;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    public Date createTime;

}
