package com.hxdi.admin.service.impl;

import com.google.code.kaptcha.Producer;
import com.hxdi.common.core.exception.BizExp;
import com.hxdi.common.core.utils.CommonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.awt.image.BufferedImage;
import java.util.concurrent.TimeUnit;

/**
 * 图片验证码服务
 * <AUTHOR>
 * @version 1.0
 * @since 2025/7/31 13:48
 */
@Service
public class KaptchaService {

    private static final String CAPTCHA_KEY_PREFIX = "CAPTCHA:";

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @Autowired
    private Producer kaptchaProducer;

    /**
     * 验证码有效期（秒）
     */
    private static final long CAPTCHA_EXPIRATION = 120;


    /**
     * 存储验证码
     * @param key
     * @param captchaCode
     */
    public void storeCaptcha(String key, String captchaCode) {
        redisTemplate.opsForValue().set(
                CAPTCHA_KEY_PREFIX + key,
                captchaCode,
                CAPTCHA_EXPIRATION,
                TimeUnit.SECONDS);
    }

    /**
     * 验证验证码
     * @param key
     * @param inputCaptchaCode
     * @return
     */
    public void validateCaptcha(String key, String inputCaptchaCode, Integer securityCheck) {
        if (CommonUtils.isEmpty(inputCaptchaCode) && CommonUtils.isEmpty(key) && securityCheck != 110) {
            // 不需要验证码服务
            return;
        }

        if (CommonUtils.isEmpty(inputCaptchaCode)) {
            BizExp.pop(5000, "验证码不能为空");
        }

        if (CommonUtils.isEmpty(key)) {
            BizExp.pop(5000, "无效验证码");
        }

        String redisKey = CAPTCHA_KEY_PREFIX + key;
        String storedCaptcha = redisTemplate.opsForValue().get(redisKey);
        if (storedCaptcha == null) {
            BizExp.pop(5000, "验证码已过期");
        }

        if (!storedCaptcha.equalsIgnoreCase(inputCaptchaCode)) {
            BizExp.pop(5000, "验证码错误");
        }

        // 验证成功后立即删除
        redisTemplate.delete(redisKey);
    }

    /**
     * 创建验证码文本
     * @return
     */
    public String createText() {
        return kaptchaProducer.createText();
    }

    /**
     * 创建验证码图片
     * @param captchaKey  验证码token
     * @return
     */
    public BufferedImage createImage(String captchaKey) {
        // 创建验证码文本
        String capText = createText();

        // 存储验证码
        storeCaptcha(captchaKey, capText);
        return kaptchaProducer.createImage(capText);
    }
}
