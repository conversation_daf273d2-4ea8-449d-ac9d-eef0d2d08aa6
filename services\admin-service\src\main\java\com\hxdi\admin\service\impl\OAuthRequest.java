package com.hxdi.admin.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.hxdi.common.autoconfigure.ServerConfiguration;
import com.hxdi.common.core.constants.SecurityClientSecret;
import com.hxdi.common.core.utils.RestUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/7/26 14:27
 * @description oauth 认证请求处理
 * @version 1.0
 */
@Service
@Slf4j
public class OAuthRequest {

    private static final String ACCESS_TOKEN_URL = "http://127.0.0.1:%s/oauth/token";

    @Autowired
    private RestUtil restUtil;

    @Autowired
    private ServerConfiguration environment;

    /**
     * 使用oauth2授权码模式登录.
     * @param clientId
     * @param redirectUri
     * @param code
     * @param headers
     * @return
     */
    public JSONObject authorizationCode2Token(String clientId, String redirectUri, String code, HttpHeaders headers) {
        Map<String, Object> postParameters = new LinkedHashMap<>();
        postParameters.put("client_id", clientId);
        postParameters.put("client_secret", SecurityClientSecret.clientSecret);
        postParameters.put("grant_type", "authorization_code");
        postParameters.put("code", code);
        // 添加参数区分,第三方登录
        postParameters.put("redirect_uri", redirectUri);
        // 使用客户端的请求头,发起请求
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        // 强制移除 原来的请求头,防止token失效
        headers.remove(HttpHeaders.AUTHORIZATION);
        JSONObject result = restUtil.post(getAccessTokenUrl(), headers.toSingleValueMap(), postParameters, JSONObject.class);
        return result;
    }

    /**
     * 使用oauth2密码模式登录.
     * @param username
     * @param password
     * @param apiKey
     * @param headers
     * @return
     */
    public JSONObject usernamePassword2Token(String username, String password, String apiKey, HttpHeaders headers) {
        Map<String, Object> requestMaps = new LinkedHashMap<>();
        requestMaps.put("username", username);
        requestMaps.put("password", password);
        requestMaps.put("client_id", apiKey);
        requestMaps.put("client_secret", SecurityClientSecret.clientSecret);
        requestMaps.put("grant_type", "password");
        // 使用客户端的请求头,发起请求
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        // 强制移除 原来的请求头,防止token失效
        headers.remove(HttpHeaders.AUTHORIZATION);
        JSONObject result = restUtil.post(getAccessTokenUrl(), headers.toSingleValueMap(), requestMaps, JSONObject.class);
        return result;
    }

    /**
     * 获取token URL
     * @return
     */
    private String getAccessTokenUrl() {
        return String.format(ACCESS_TOKEN_URL, environment.getPort());
    }
}
