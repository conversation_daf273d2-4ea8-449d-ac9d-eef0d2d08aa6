package com.hxdi.msg.client.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "MSG_TEMPLATE")
public class MsgTemplate implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 消息类型：SMS，EMAIL，SITE
     */
    @TableField(value = "MSG_TYPE")
    private String msgType;

    /**
     * 模板标识
     */
    @TableField(value = "MSG_CODE")
    private String msgCode;

    /**
     * 模板名称
     */
    @TableField(value = "TPL_NAME")
    private String tplName;

    /**
     * 标题
     */
    @TableField(value = "TITLE")
    private String title;

    /**
     * 模板内容
     */
    @TableField(value = "CONTENT")
    private String content;

    /**
     * 触发方式：IMMEDIATE（立即）、SCHEDULED（定时）、EVENT（事件触发）、CRON（周期任务）
     */
    @TableField(value = "TRI_TYPE")
    private String triType;

    /**
     * 触发参数
     */
    @TableField(value = "TRI_PARAMS")
    private String triParams;

    /**
     * 自动已读：1-是，0-否
     */
    @TableField(value = "AUTO_READ")
    private Integer autoRead;

    /**
     * 提醒方式:1-待办，2-预警，3-提醒
     */
    @TableField(value = "ALERM_TYPE")
    private Integer alermType;

    /**
     * 跳转地址
     */
    @TableField(value = "URLS")
    private String urls;

    /**
     * 打开方式
     */
    @TableField(value = "MODE")
    private Integer mode;

    /**
     * 状态（0禁用，1有效，7删除）
     */
    @TableField(value = "ENABLED")
    private Integer enabled;

    /**
     * 创建时间
     */
    @TableField(value = "CREATE_TIME", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "UPDATE_TIME", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 创建id
     */
    @TableField(value = "CREATE_ID", fill = FieldFill.INSERT)
    private String createId;

    /**
     * 更新id
     */
    @TableField(value = "UPDATE_ID", fill = FieldFill.INSERT_UPDATE)
    private String updateId;

    /**
     * 租户id
     */
    @TableField(value = "TENANT_ID", fill = FieldFill.INSERT)
    private String tenantId;

    /**
     * 组织id
     */
    @TableField(value = "DATA_HIERARCHY_ID", fill = FieldFill.INSERT)
    private String dataHierarchyId;

    /**
     * 扩展字段（邮件、短信）
     */
    @TableField(value = "EXTEND")
    private String extend;
}