package com.hxdicloud.genarator.server;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;


/**
 * 平台基础服务
 * 提供系统用户、权限分配、资源、客户端管理
 *
 * <AUTHOR>
 */
@EnableDiscoveryClient
@SpringBootApplication
public class GeneratorApplication {

    public static void main(String[] args) {
        SpringApplication.run(GeneratorApplication.class, args);
    }
}
