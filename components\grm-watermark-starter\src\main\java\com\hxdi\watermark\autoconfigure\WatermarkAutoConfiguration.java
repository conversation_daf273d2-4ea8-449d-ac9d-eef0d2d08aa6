package com.hxdi.watermark.autoconfigure;

import com.hxdi.watermark.handle.WatermarkHandle;
import com.hxdi.watermark.properties.WatermarkProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2023/7/5
 * @description
 * @version 1.0
 */
@Configuration
@EnableConfigurationProperties({WatermarkProperties.class})
public class WatermarkAutoConfiguration {

    private WatermarkProperties watermarkProperties;

    public WatermarkAutoConfiguration(WatermarkProperties watermarkProperties) {
        this.watermarkProperties = watermarkProperties;
    }

    @Bean
    public WatermarkHandle watermarkHandle() {
        return new WatermarkHandle(watermarkProperties);
    }


}
