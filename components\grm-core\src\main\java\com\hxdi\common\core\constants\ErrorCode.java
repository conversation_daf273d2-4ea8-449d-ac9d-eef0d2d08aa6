package com.hxdi.common.core.constants;

/**
 * 自定义返回码
 *
 * <AUTHOR>
 */

public enum ErrorCode {
    /**
     * 成功
     */
    OK(0, "success"),
    FAIL(10000, "fail"),
    SUCCESS(200,"success"),

    FILE_TYPE_UNKNOWN(256, "不支持的文件类型"),
    FILE_UPLOAD_ERROR(257, "文件上传失败"),
    // 258-264为库存模块预留的错误码


    ORGAN_ALREADY_EXISTS(266, "保存失败: 组织信息已存在"),

    // 1000xx 为基础服务预留的错误码
    SIGNATURE_DENIED(10001, "signature_denied"),
    BAD_CREDENTIALS(10002, "bad_credentials"),
    ACCOUNT_DISABLED(10003, "account_disabled"),
    ACCOUNT_EXPIRED(10004, "account_expired"),
    CREDENTIALS_EXPIRED(10005, "credentials_expired"),
    ACCOUNT_LOCKED(10006, "account_locked"),
    ACCOUNT_REPEAT(10007, "account_repeat"),
    ACCOUNT_NOT_EXIST(10009, "账号不存在"),
    SIMPLE_PWD(10008, "密码安全等级不满足系统要求"),
    USERNAME_REPEATABLE(10010, "用户名重复"),
    ORGAN_NAME_REPEATABLE(10011, "组织名重复"),

    // 10100-10199为业务服务预留的错误码
    //...

    // 10200-10299为开放接口服务预留的错误码
    DEVICE_NOT_EXIST(10200, "设备不存在"),
    DEVICE_BIZ_ERROR(10201, "业务发生错误"),
    DEVICE_NOT_ENABLE(10202, "设备未启用"),

    // 4xxxx为系统预留的错误码
    BAD_REQUEST(40000, "bad_request"),
    NOT_FOUND(40004, "not_found"),
    METHOD_NOT_ALLOWED(40005, "method_not_allowed"),
    MEDIA_TYPE_NOT_ACCEPTABLE(40006, "media_type_not_acceptable"),
    TOO_MANY_REQUESTS(40029, "too_many_requests"),
    ARGUMENT_ERROR(40030, "参数错误"),

    UNAUTHORIZED(40001, "当前会话信息已失效,请重新登录!"),
    ACCESS_DENIED(40003, "请求被拒绝，无权限访问!"),

    ACCESS_DENIED_BLACK_LIMITED(40031, "access_denied_black_limited"),
    ACCESS_DENIED_WHITE_LIMITED(40032, "access_denied_white_limited"),
    ACCESS_DENIED_AUTHORITY_EXPIRED(40033, "access_denied_authority_expired"),
    ACCESS_DENIED_UPDATING(40034, "access_denied_updating"),
    ACCESS_DENIED_DISABLED(40035, "access_denied_disabled"),

    // 5xxxx为系统预留的错误码
    ERROR(50000, "error"),
    GATEWAY_TIMEOUT(50004, "gateway_timeout"),
    SERVICE_UNAVAILABLE(50003, "service_unavailable"),
    // 不要再扩展错误码！！！
    // 所有业务错误码以上面三位编码递增, 如果某个业务类型可能存在多个错误的时候，请预留若干个错误码供以后使用；
    ;





    private int code;
    private String message;

    ErrorCode() {}

    ErrorCode(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public static ErrorCode getResultEnum(int code) {
        for (ErrorCode type : ErrorCode.values()) {
            if (type.getCode() == code) {
                return type;
            }
        }
        return ERROR;
    }

    public static ErrorCode getResultEnum(String message) {
        for (ErrorCode type : ErrorCode.values()) {
            if (type.getMessage().equals(message)) {
                return type;
            }
        }
        return ERROR;
    }

    public int getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}


