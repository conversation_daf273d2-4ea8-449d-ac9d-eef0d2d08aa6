<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdicloud.msg.mapper.EmailConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.hxdi.msg.client.model.entity.EmailConfig">
        <id column="config_id" property="configId" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="name" property="name" />
        <result column="smtp_host" property="smtpHost" />
        <result column="smtp_username" property="smtpUsername" />
        <result column="smtp_password" property="smtpPassword" />
        <result column="is_persist" property="isPersist" />
        <result column="is_default" property="isDefault" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        config_id, name, smtp_host, smtp_username, smtp_password, is_persist, is_default, create_time, update_time
    </sql>
</mapper>
