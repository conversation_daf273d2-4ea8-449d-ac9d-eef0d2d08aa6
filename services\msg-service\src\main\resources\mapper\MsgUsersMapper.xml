<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.msg.mapper.MsgUsersMapper">
  <resultMap id="BaseResultMap" type="com.hxdi.msg.client.model.entity.MsgUsers">
    <!--@mbg.generated-->
    <!--@Table MSG_USERS-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="MSG_ID" jdbcType="VARCHAR" property="msgId" />
    <result column="USER_ID" jdbcType="VARCHAR" property="userId" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, MSG_ID, USER_ID, CREATE_TIME
  </sql>
</mapper>