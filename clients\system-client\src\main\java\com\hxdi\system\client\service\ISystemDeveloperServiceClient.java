package com.hxdi.system.client.service;

import com.hxdi.common.core.model.ResultBody;
import com.hxdi.system.client.model.UserInfo;
import com.hxdi.system.client.model.entity.SystemDeveloper;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ISystemDeveloperServiceClient {

    /**
     * 获取账号登录信息
     *
     * @param username
     * @return
     */
    @PostMapping("/developer/login")
    ResultBody<UserInfo> login(@RequestParam(value = "username") String username, @RequestParam(value = "clientId") String clientId, @RequestParam(value = "thirdParty",required = false)  String thirdParty);

    /**
     * 获取开发者列表
     *
     * @return
     */
    @GetMapping("/developer/list")
    ResultBody<List<SystemDeveloper>> getList();

    @PostMapping("/developer/add/thirdParty")
    ResultBody addThirdParty(
            @RequestParam(value = "account") String account,
            @RequestParam(value = "password") String password,
            @RequestParam(value = "accountType") String accountType,
            @RequestParam(value = "nickName") String nickName,
            @RequestParam(value = "avatar", required = false) String avatar
    );

}
