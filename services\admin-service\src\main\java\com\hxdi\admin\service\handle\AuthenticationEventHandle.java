package com.hxdi.admin.service.handle;

import cn.hutool.core.util.StrUtil;
import com.hxdi.admin.service.feign.SystemUserServiceClient;
import com.hxdi.common.core.model.ResultBody;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.event.AbstractAuthenticationFailureEvent;
import org.springframework.security.authentication.event.AuthenticationSuccessEvent;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/11/12 21:10
 * @description 认证事件监听
 * @version 1.0
 */
@Component
public class AuthenticationEventHandle {

    @Autowired
    private SystemUserServiceClient systemUserServiceClient;

    @EventListener
    public void onSuccess(AuthenticationSuccessEvent event) {
        // no thing to do
    }

    @EventListener
    public void onFailure(AbstractAuthenticationFailureEvent event) {
        AuthenticationException exp = event.getException();
        if (exp instanceof BadCredentialsException) {
            // 认证失败记录，超过设定的尝试次数，将会锁定账号
            Authentication authentication = (Authentication) event.getSource();
            ResultBody<Integer> resp = systemUserServiceClient.authFail((String) authentication.getPrincipal());
            Integer attempts = resp.getData();
            if (attempts.intValue() == 0) {
                throw new BadCredentialsException("#Bad credentials#密码输错次数过多账号已被锁定，请于10分钟后再试");
            }

            if (attempts.intValue() <= 3 && attempts.intValue() > 0) {
                throw new BadCredentialsException(StrUtil.format("#Bad credentials#密码错误，当前还剩余{}次登录尝试", attempts));
            }
        } else if (exp instanceof UsernameNotFoundException) {
            // 安全考虑，重新抛出用户密码错误提示的异常
            throw new BadCredentialsException("Bad credentials");
        }
    }
}
