package com.hxdi.system.client.service;

import com.hxdi.common.core.model.ResultBody;
import com.hxdi.system.client.model.entity.GatewayRateLimit;
import com.hxdi.system.client.model.entity.GatewayRateLimitApi;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface IGatewayRateLimitClient {

    /**
     * 获取流量策略
     *
     * @param policyId
     * @return
     */
    @GetMapping("/gateway/limit/rate/info")
    ResultBody<GatewayRateLimit> get(@RequestParam("policyId") String policyId);

    /**
     * 查询获取流量策略已绑定API列表
     *
     * @param policyId
     * @return
     */
    @GetMapping("/gateway/limit/rate/api/list")
    ResultBody<List<GatewayRateLimitApi>> getApis(@RequestParam("policyId") String policyId);
}
