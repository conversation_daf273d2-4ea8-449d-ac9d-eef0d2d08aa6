package com.hxdi.system.client.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hxdi.common.core.mybatis.base.entity.AbstractEntity;
import lombok.*;

/**
 * 系统权限-菜单权限、操作权限、API权限
 *
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@TableName("system_authority")
public class SystemAuthority extends AbstractEntity {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    private String authorityId;

    /**
     * 权限标识
     */
    private String authority;

    /**
     * 菜单资源ID
     */
    private String menuId;

    /**
     * API资源ID
     */
    private String apiId;

    /**
     * 操作资源ID
     */
    private String actionId;

    /**
     * 状态
     */
    private Integer status;


}
