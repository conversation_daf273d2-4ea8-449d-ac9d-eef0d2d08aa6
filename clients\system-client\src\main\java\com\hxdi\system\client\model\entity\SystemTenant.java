package com.hxdi.system.client.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hxdi.common.core.mybatis.base.entity.AbstractEntity;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import java.time.LocalDateTime;

/**
 * <p>
 * 系统租户
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-03
 */
@Getter
@Setter
@TableName("system_tenant")
public class SystemTenant extends AbstractEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 租户ID
     */
    @TableId(type = IdType.ASSIGN_UUID)
    @TableField("tenant_id")
    private String tenantId;

    /**
     * 租户名称
     */
    @NotEmpty(message = "企业名称不能为空")
    @TableField("tenant_name")
    private String tenantName;

    @NotEmpty(message = "企业简称不能为空")
    @TableField("abbr_name")
    private String abbrName;

    /**
     * 有效期
     */
    @TableField("validity_time")
    private LocalDateTime validityTime;

    /**
     * 联系人
     */
    @TableField("contacts")
    private String contacts;

    /**
     * 电话
     */
    @TableField("phone")
    private String phone;

    /**
     * 注册类型：1-创建，2-注册
     */
    @TableField("registry_type")
    private Integer registryType;

    /**
     * 状态：0：停用，1-启用
     */
    @TableField("state")
    private Integer state;

    /**
     * 信用代码
     */
    @NotEmpty(message = "信用代码不能为空")
    @TableField("credit_code")
    private String creditCode;

    /**
     * 法人
     */
    @TableField("enterprise_corp")
    private String enterpriseCorp;

    /**
     * 法人身份证号码
     */
    @TableField("corp_id_no")
    private String corpIdNo;

    /**
     * 地址
     */
    @NotEmpty(message = "地址不能为空")
    @TableField("address")
    private String address;

    /**
     * 详细地址
     */
    @TableField("detail_addr")
    private String detailAddr;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 经度
     */
    @TableField("lon")
    private String lon;

    /**
     * 纬度
     */
    @TableField("lat")
    private String lat;

    /**
     * 创建人
     */
    @TableField("created_by")
    private String createdBy;

    /**
     * 租户编码，用于组织编码前缀
     */
    @TableField("tenant_code")
    private String tenantCode;

    /**
     * 集团编号
     */
    private String qyCode;
}
