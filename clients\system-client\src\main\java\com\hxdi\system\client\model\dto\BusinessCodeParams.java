package com.hxdi.system.client.model.dto;

import com.hxdi.common.core.model.DataType;

import java.util.Map;

/**
 * 业务编码请求参数封装对象
 * <AUTHOR>
 */
public class BusinessCodeParams extends BusinessCode {

    private static final long serialVersionUID = 6422373232974992131L;

    /**
     * 规则定义编码
     */
    private String code;

    /**
     * 规则定义扩展信息
     */
    private Map<String, String> extendMap;

    /**
     * 生成业务编码数据类型：STRING, INTEGER, LONG
     */
    private DataType dt;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Map<String, String> getExtendMap() {
        return extendMap;
    }

    public void setExtendMap(Map<String, String> extendMap) {
        this.extendMap = extendMap;
    }

    public DataType getDt() {
        return dt;
    }

    public void setDt(DataType dt) {
        this.dt = dt;
    }
}
