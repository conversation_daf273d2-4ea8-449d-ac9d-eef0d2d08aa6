package com.hxdi.gateway.service.feign;

import com.hxdi.common.core.model.ResultBody;
import com.hxdi.system.client.constants.SystemConstants;
import com.hxdi.system.client.model.RuntimeSettingProperties;
import com.hxdi.system.client.service.IGatewayServiceClient;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;

/**
 * @author: liuyadu
 * @date: 2018/10/24 16:49
 * @description:
 */
@Component
@FeignClient(SystemConstants.SYSTEM_SERVER)
public interface GatewayServiceClient extends IGatewayServiceClient {

    @GetMapping("/gateway/runtime/settings")
    ResultBody<RuntimeSettingProperties> getRuntimeSettings();
}
