server:
    port: ${app.port}
    undertow:
        buffer-size: 1024
        direct-buffers: true
    servlet:
        session:
            timeout: PT30S
spring:
    application:
        name: ${artifactId}
    cloud:
        #手动配置Bus id,
        bus:
            id: ${artifactId}:${app.port}
        nacos:
            config:
                namespace: ${config.namespace}
                server-addr: ${config.server-addr}
                group: ${config.group}
                shared-configs:
                    - dataId: common.properties
                      refresh: true
                    - dataId: base_db.properties
                      refresh: false
                    - dataId: redis.properties
                      refresh: false
                    - dataId: rabbitmq.properties
                      refresh: false
            discovery:
                namespace: ${config.namespace}
                server-addr: ${discovery.server-addr}
                # false:不注册该服务，仅限本地调试使用
                register-enabled: ${non.debug}
            username: ${auth.username}
            password: ${auth.password}
    main:
        allow-bean-definition-overriding: true
    # 解决restful 404错误 spring.mvc.throw-exception-if-no-handler-found=true spring.resources.add-mappings=false
    mvc:
        throw-exception-if-no-handler-found: true
    resources:
        add-mappings: false
    profiles:
        active: ${profile.name}
    thymeleaf:
        cache: false
        encoding: UTF-8
        mode: HTML
        prefix: classpath:/templates/
        suffix: .html


management:
    endpoints:
        web:
            exposure:
                include: '*'

knife4j:
    enable: ${api.debug}
    setting:
        enableFooter: false
        enableFooterCustom: true
        footerCustomContent: "Apache License 2.0 | Copyright  2023-[物储云联]"

cloud:
    swagger2:
        enabled: ${api.debug}
        description: 认证服务
        title: 认证服务
    social:
         client:
             admin:
                  client-id: app-client
                  client-secret: hx123456
             wechat-mini:
#                  client-id: wx639bc287e23bc0a9
#                  client-secret: f476c061ad74c70f82cf4d244c67cf3b
                  client-id: 1
                  client-secret: 1

