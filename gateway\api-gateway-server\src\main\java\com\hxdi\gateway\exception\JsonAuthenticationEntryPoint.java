package com.hxdi.gateway.exception;

import com.alibaba.fastjson.JSONObject;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.gateway.service.AccessLogService;
import com.hxdi.common.core.exception.GlobalExceptionHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.core.io.buffer.DataBufferFactory;
import org.springframework.core.io.buffer.DataBufferUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.server.ServerAuthenticationEntryPoint;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.nio.charset.Charset;

/**
 * 网关认证异常处理,记录日志
 *
 * <AUTHOR>
 */
@Slf4j
public class JsonAuthenticationEntryPoint implements ServerAuthenticationEntryPoint {
    private AccessLogService accessLogService;

    public JsonAuthenticationEntryPoint(AccessLogService accessLogService) {
        this.accessLogService = accessLogService;
    }

    @Override
    public Mono<Void> commence(ServerWebExchange exchange, AuthenticationException e) {
        ResultBody resultBody = GlobalExceptionHandler.resolveException(e,exchange.getRequest().getURI().getPath());
        return Mono.defer(() -> Mono.just(exchange.getResponse()))
                   .flatMap((response) -> {
                        response.setStatusCode(HttpStatus.valueOf(resultBody.getHttpStatus()));
                        response.getHeaders().setContentType(MediaType.APPLICATION_JSON_UTF8);
                        DataBufferFactory dataBufferFactory = response.bufferFactory();
                        DataBuffer buffer = dataBufferFactory.wrap(JSONObject.toJSONString(resultBody).getBytes(Charset.defaultCharset()));
                        // 保存日志
                        accessLogService.sendLog(exchange,e);

                        return response.writeWith(Mono.just(buffer))
                                       .doOnError((error) -> DataBufferUtils.release(buffer));
                    });
    }
}
