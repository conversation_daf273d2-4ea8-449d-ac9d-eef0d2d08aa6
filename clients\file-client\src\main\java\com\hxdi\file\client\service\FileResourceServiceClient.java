package com.hxdi.file.client.service;

import com.hxdi.common.core.model.ResultBody;
import com.hxdi.file.client.model.entity.FileResource;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @date 2023/3/9 18:18
 * @description 文件操作客户端
 * @version 1.0
 */
public interface FileResourceServiceClient {

    /**
     * 立即删除
     * @param resIds
     * @return
     */
    @DeleteMapping("/file/remove")
    ResultBody remove(@RequestParam("resIds") String resIds);

    /**
     * 通过URL读取并上传文件
     * @param fileUrl
     * @return
     */
    @PostMapping("/file/upload/internal/byUrl")
    ResultBody<FileResource> uploadFileByUrl(@RequestParam("fileUrl") String fileUrl);
}
