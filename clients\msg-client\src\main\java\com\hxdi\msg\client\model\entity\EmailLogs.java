package com.hxdi.msg.client.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.hxdi.common.core.mybatis.base.entity.AbstractEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 邮件发送日志
 */
@Getter
@Setter
@TableName("msg_email_logs")
public class EmailLogs extends AbstractEntity {

    private static final long serialVersionUID=1L;

    @TableId(type = IdType.ASSIGN_ID)
    private String logId;

    private String subject;

    private String sendTo;

    private String sendCc;

    private String content;

    @ApiModelProperty(value = "附件路径")
    private String attachments;

    @ApiModelProperty(value = "发送次数")
    private Integer sendNums;

    @ApiModelProperty(value = "错误信息")
    private String error;

    @ApiModelProperty(value = "0-失败 1-成功")
    private Integer result;

    @ApiModelProperty(value = "发送配置")
    private String config;

    @ApiModelProperty(value = "模板编号")
    private String tplCode;


}
