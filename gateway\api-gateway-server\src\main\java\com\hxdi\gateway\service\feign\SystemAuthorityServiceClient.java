package com.hxdi.gateway.service.feign;

import com.hxdi.system.client.constants.SystemConstants;
import com.hxdi.system.client.service.ISystemAuthorityServiceClient;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;

/**
 * @author: liu<PERSON><PERSON>
 * @date: 2018/10/24 16:49
 * @description:
 */
@Component
@FeignClient(value = SystemConstants.SYSTEM_SERVER)
public interface SystemAuthorityServiceClient extends ISystemAuthorityServiceClient {


}
