package com.hxdi.job.client.model;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;

/**
 * 任务详情
 * <AUTHOR>
 */
@Getter
@Setter
@TableName("qrtz_job_details")
public class JobInfo implements Serializable {

    private static final long serialVersionUID = -7693857859775581311L;
    /**
     * 增加或修改标识
     */
    @TableField(exist = false)
    private int id;

    /**
     * 任务名称
     */
    @TableField("JOB_NAME")
    private String jobName;

    /**
     * 任务描述
     */
    @TableField("DESCRIPTION")
    private String jobDescription;

    /**
     * 任务类名
     */
    @TableField("JOB_CLASS_NAME")
    private String jobClassName;

    /**
     * 任务分组
     */
    @TableField("JOB_GROUP")
    private String jobGroupName;

    /**
     * 任务状态
     */
    @TableField(exist = false)
    private String jobStatus;

    /**
     * 任务类型 SimpleTrigger-简单任务,CronTrigger-表达式
     */
    @TableField(exist = false)
    private String jobTrigger;

    /**
     * 任务表达式
     */
    @TableField(exist = false)
    private String cronExpression;

    /**
     * 创建时间
     */
    @TableField(exist = false)
    private Date createTime;

    /**
     * 间隔时间（毫秒）
     */
    @TableField(exist = false)
    private Long repeatInterval;

    /**
     * 重复次数
     */
    @TableField(exist = false)
    private Integer repeatCount;

    /**
     * 起始时间
     */
    @TableField(exist = false)
    private Date startDate;

    /**
     * 终止时间
     */
    @TableField(exist = false)
    private Date endDate;

    /**
     * 执行数据
     */
    @TableField(exist = false)
    private Map data;
}
