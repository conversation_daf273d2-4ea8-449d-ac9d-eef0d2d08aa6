package com.hxdi.system.client.service;

import com.hxdi.common.core.model.ResultBody;
import com.hxdi.system.client.model.entity.SystemArea;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

public interface ISystemAreaServiceClient {

    @GetMapping("/area/list")
    ResultBody<List<SystemArea>> getAreaList(@RequestParam("parentId") Long parentId);
    /**
     * "根据名称、层级获取确切地点"
     * @param level
     * @param name
     * @return
     */
    @GetMapping("/area/getArea")
    ResultBody<SystemArea> getAreaByName(@RequestParam(value="level") Integer level,@RequestParam(value="name") String name);

    @GetMapping("/area/getByCode")
    ResultBody<SystemArea> getByCode(@RequestParam(value="code") Long code);

//    @GetMapping("/area/getById")
//    ResultBody<SystemArea> getById(@RequestParam(value="id") Long id);

    @GetMapping("/area/getByMergeName")
    ResultBody<SystemArea> getByMergeName(@RequestParam(value="mergeName")String mergeName);

}
