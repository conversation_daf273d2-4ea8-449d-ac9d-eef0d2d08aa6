package com.hxdi.common.listener;

import com.hxdi.common.core.utils.RedisUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.listener.KeyExpirationEventMessageListener;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

/**
 * redis的key过期监听
 * <AUTHOR> on 2019/8/1.
 */
public class KeyExpiredListener extends KeyExpirationEventMessageListener {
    private final static Logger logger = LoggerFactory.getLogger(KeyExpiredListener.class);

    private RedisUtil redisUtil;

    public KeyExpiredListener(RedisMessageListenerContainer listenerContainer, RedisUtil redisUtil) {
        super(listenerContainer);
        this.redisUtil = redisUtil;
    }

    @Override
    public void onMessage(Message message, byte[] pattern) {


        //key=iot:device:command:868474043308814_b39d7fb91ef74ff6a205761b62cff0fb_aaaaaa
        String channel = new String(message.getChannel(), StandardCharsets.UTF_8);
        //过期的key
        String key = new String(message.getBody(), StandardCharsets.UTF_8);
        //删除过期key
        redisUtil.del(key);
        logger.info("redis key 过期：pattern={},channel={},key={}", new String(pattern), channel, key);
    }
}
