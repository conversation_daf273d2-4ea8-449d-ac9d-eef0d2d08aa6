package com.hxdi.file.util;

import com.hxdi.common.core.constants.ErrorCode;
import com.hxdi.common.core.exception.BizExp;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.common.core.utils.Strcat;
import com.hxdi.file.util.dto.FileParams;
import com.hxdi.file.util.dto.FileWriteResult;
import com.obs.services.model.ObjectListing;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;

import java.io.InputStream;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2023/2/27 18:44
 * @description 抽象文件管理服务
 * @version 1.0
 */
public abstract class AbstractFileOperator {

    private static final String DEFAULT_PREFIX = "default";


    /**
     * 存储文件对象
     * @param is   输入流
     * @param params
     * @return
     */
    public abstract Optional<FileWriteResult> storeObject(InputStream is, FileParams params);

    /**
     * 读取文件对象
     * @param params
     * @return
     */
    public abstract Optional<InputStream> loadObject(FileParams params);

    /**
     * 获取文件对象访问地址
     * @param params
     * @return
     */
    public abstract Optional<String> getObjectUrl(FileParams params);

    /**
     * 删除文件对象
     * @param params
     */
    public abstract void removeObject(FileParams params);

    /**
     * 批量删除文件对象
     * @param params
     */
    public abstract void removeObjects(FileParams params);

    /**
     * 对原始对象重新命名，保证对象的唯一性，避免错误替换的情况。
     * 防止同一目录文件存放太多
     * @param originalName
     * @return
     */
    protected String rename(String originalName) {
        LocalDate date = LocalDate.now();
        String path_0 = String.valueOf(date.getYear());
        String path_1 = String.valueOf(date.getMonthValue());
        String uniqueName;
        String[] splitNames = originalName.split("\\.");
        if (splitNames.length > 1) {
            uniqueName = Strcat.join(splitNames[0], "_", String.valueOf(System.currentTimeMillis()), ".", splitNames[splitNames.length - 1]).toString();
        } else {
            uniqueName = Strcat.join(originalName, "_", String.valueOf(System.currentTimeMillis())).toString();
        }
        return Strcat.joinWithDelimiter("/", path_0, path_1, uniqueName).toString();
    }

    /**
     * 对原始对象重新命名，保证对象的唯一性，避免错误替换的情况。
     * 防止同一目录文件存放太多
     * @param originalName
     * @param prefix 业务前缀, 如：image/2025/04/xxx.jpg
     * @return
     */
    protected String rename(String originalName, String prefix) {
        if (CommonUtils.isEmpty(prefix)) {
            prefix = DEFAULT_PREFIX;
        }

        String path_0 = prefix;

        LocalDate date = LocalDate.now();
        String path_1 = String.valueOf(date.getYear());
        String path_2 = String.valueOf(date.getMonthValue());
        String uniqueName;
        String[] splitNames = originalName.split("\\.");
        if (splitNames.length > 1) {
            uniqueName = Strcat.join(splitNames[0], "_", String.valueOf(System.currentTimeMillis()), ".", splitNames[splitNames.length - 1]).toString();
        } else {
            uniqueName = Strcat.join(originalName, "_", String.valueOf(System.currentTimeMillis())).toString();
        }
        return Strcat.joinWithDelimiter("/", path_0, path_1, path_2, uniqueName).toString();
    }

    /**
     * 检查上传的文件类型是否支持
     * @param originalName
     */
    protected void isSupportUploadingFile(String originalName) {
        final String extName = StringUtils.substringAfterLast(originalName, ".");
        boolean support = FileType.anySupport(extName);
        if (!support) {
            BizExp.pop(ErrorCode.FILE_TYPE_UNKNOWN);
        }
    }

    /**
     * 解析content-type
     * @param originalName
     * @return
     */
    protected String parseContentType(String originalName) {
//        final String extName = StringUtils.substringAfterLast(originalName, ".");
//        String contentType = MediaType.APPLICATION_OCTET_STREAM_VALUE;
//        if (FileType.IMAGE.support(extName)) {
//            contentType = "image/" + extName;
//        }
//
//        return contentType;

        return MimeTypes.getInstance().getMimetype(originalName);
    }

    /**
     * 进行预处理--上传对象
     *
     * @param params
     * @return
     */
    protected FileWriteResult preHandleResult(FileParams params) {
        isSupportUploadingFile(params.getObjectName());

        FileWriteResult result = new FileWriteResult();
        final String originalName = params.getObjectName();
        final String objectName = rename(originalName, params.getPrefix());
        final String contentType = parseContentType(originalName);
        result.setContentType(contentType);
        result.setObjectName(objectName);
        return result;
    }
}
