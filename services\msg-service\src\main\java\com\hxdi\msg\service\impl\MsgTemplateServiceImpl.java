package com.hxdi.msg.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hxdi.common.core.exception.BaseException;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.common.core.utils.JsonConverter;
import com.hxdi.msg.client.model.entity.MsgInfo;
import com.hxdi.msg.client.model.entity.MsgTemplate;
import com.hxdi.msg.client.model.vo.MsgTemplateCondition;
import com.hxdi.msg.enums.MsgEnum;
import com.hxdi.msg.mapper.MsgTemplateMapper;
import com.hxdi.msg.service.MsgInfoService;
import com.hxdi.msg.service.MsgTaskService;
import com.hxdi.msg.service.MsgTemplateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service
@Transactional(rollbackFor = Exception.class)
public class MsgTemplateServiceImpl extends ServiceImpl<MsgTemplateMapper, MsgTemplate> implements MsgTemplateService {

    @Resource
    private MsgTaskService msgTaskService;
    @Autowired
    private MsgInfoService msgInfoService;

    protected static String convertParams(MsgTemplate template, String JSONParams) {
        //获取待转换的模版内容  e.g. 测试用户${test1}: 你好，我是${test2}
        String content = template.getContent();

        // 解析JSON参数为Map对象
        @SuppressWarnings("unchecked")
        Map<String, String> parameter = (Map<String, String>) JsonConverter.parse(JSONParams, Map.class);

        // 如果参数为空，直接返回原内容
        if (parameter == null || parameter.isEmpty()) {
            return content;
        }

        // 使用正则表达式查找并替换占位符 ${变量名}
        String result = content;
        Pattern pattern = Pattern.compile("\\$\\{([^}]+)}");
        Matcher matcher = pattern.matcher(content);

        // 遍历所有匹配的占位符进行替换
        while (matcher.find()) {
            String placeholder = matcher.group(0); // 完整的占位符，如 ${test}
            String variableName = matcher.group(1); // 变量名，如 test

            // 从参数映射中获取对应的值
            String value = parameter.get(variableName);

            // 如果找到对应的参数值，则进行替换；否则保持原占位符不变
            if (value != null) {
                result = result.replace(placeholder, value);
            }
        }

        return result;

    }

    public static void main(String[] args) {
        Map<String, String> testParameter = new HashMap<>();
        testParameter.put("test_1", "张三");
        testParameter.put("test_2", "李四");

        String json = JsonConverter.toJson(testParameter);
        System.out.println("JSON: " + json);
        MsgTemplate t = new MsgTemplate();
        t.setContent("测试用户${test_1}: 你好，我是${test_2}");
        String string = convertParams(t, json);
        System.out.println("转换结果：" + string);
    }

    @Override
    public List<MsgTemplate> getListByCondition(MsgTemplateCondition condition) {
        LambdaQueryWrapper<MsgTemplate> wrapper = new LambdaQueryWrapper<MsgTemplate>()
                .eq(CommonUtils.isNotEmpty(condition.getMsgType()), MsgTemplate::getMsgType, condition.getMsgType())
                .eq(CommonUtils.isNotEmpty(condition.getMsgCode()), MsgTemplate::getMsgCode, condition.getMsgCode())
                .eq(CommonUtils.isNotEmpty(condition.getAlermType()), MsgTemplate::getAlermType, condition.getAlermType())
                .like(CommonUtils.isNotEmpty(condition.getTplName()), MsgTemplate::getTplName, "%" + condition.getTplName() + "%");

        return this.list(wrapper);
    }

    @Override
    public IPage<MsgTemplate> getPageByCondition(MsgTemplateCondition condition) {
        IPage<MsgTemplate> page = new Page<>(condition.getPage(), condition.getLimit());
        List<MsgTemplate> templateList = getListByCondition(condition);
        page.setRecords(templateList);
        page.setTotal(templateList.size());
        return page;
    }

    @Override
    public void addV1(MsgTemplate msgTemplate) {
        LambdaQueryWrapper<MsgTemplate> wrapper = new LambdaQueryWrapper<MsgTemplate>()
                .eq(MsgTemplate::getMsgCode, msgTemplate.getMsgCode());
        if (this.count(wrapper) > 0) {
            throw new BaseException("该模板标识已存在！");
        }
        this.save(msgTemplate);
    }

    @Override
    public void updateV1(MsgTemplate msgTemplate) {
        LambdaQueryWrapper<MsgTemplate> wrapper = new LambdaQueryWrapper<MsgTemplate>()
                .eq(MsgTemplate::getMsgCode, msgTemplate.getMsgCode())
                .ne(MsgTemplate::getId, msgTemplate.getId());
        if (this.count(wrapper) > 0) {
            throw new BaseException("该模板标识已存在！");
        }
        this.updateById(msgTemplate);
    }

    @Override
    public void changeStatus(String id, Integer status) {
        MsgTemplate msgTemplate = new MsgTemplate();
        msgTemplate.setId(id);
        msgTemplate.setEnabled(status);
        this.updateById(msgTemplate);
    }

    @Override
    public void deleteV1(String id) {
        this.removeById(id);
    }

    @Override
    public void sendMsgByTemplate(List<String> msgCodes, List<String> jsonParamsList, List<String> receivers) {
        if (msgCodes.isEmpty() || jsonParamsList.isEmpty() || receivers.isEmpty()) {
            return;
        }

        if (msgCodes.size() != jsonParamsList.size() || msgCodes.size() != receivers.size()) {
            throw new BaseException("消息代码、参数和接收者列表长度必须一致");
        }

        List<MsgTemplate> msgTemplateList = this.list(new LambdaQueryWrapper<MsgTemplate>().in(MsgTemplate::getMsgCode, msgCodes));
        Map<String, MsgTemplate> msgTemplateMap = msgTemplateList.stream().collect(Collectors.toMap(MsgTemplate::getMsgCode, t -> t));

        for (int i = 0; i < msgCodes.size(); i++) {
            String msgCode = msgCodes.get(i);
            MsgTemplate template = msgTemplateMap.get(msgCode);

            if (template == null) {
                continue; // 跳过不存在的模板
            }

            MsgInfo msgInfo = assembleMessage(template, jsonParamsList.get(i), receivers.get(i));
            //先保存MsgInfo，以生成ID
            msgInfo.setCreateTime(new Date());
            msgInfo.setUpdateTime(msgInfo.getCreateTime());
            msgInfo.setState(0);
            // 保存消息以获取ID
            msgInfoService.save(msgInfo);

            // 然后再添加任务
            msgTaskService.addTask(msgInfo);
        }
    }

    /**
     * 将消息模版转换为消息数据
     *
     * @param template
     * @param JSONParams
     * @param receiver
     * @return
     */
    protected MsgInfo assembleMessage(MsgTemplate template, String JSONParams, String receiver) {
        // todo 待增加更多的消息发送方式
        if (!template.getMsgType().equals("SITE")) {
            throw new BaseException("暂不支持其他类型的模版！");
        }

        MsgInfo msgInfo = new MsgInfo();
        msgInfo.setReceiver(receiver);
        if(receiver.equals(MsgEnum.MSG_USER_ALL.getCode())){
            msgInfo.setRcvType(1);
        }else {
            msgInfo.setRcvType(2);
        }
        //消息默认发送人超级管理员
        msgInfo.setPublisher("0");
        msgInfo.setAlermType(template.getAlermType());
        msgInfo.setUrls(template.getUrls());
        msgInfo.setTitle(template.getTitle());
        msgInfo.setContent(convertParams(template, JSONParams));
        msgInfo.setAutoRead(template.getAutoRead());
        if (template.getTriType().equals("IMMEDIATE")) {
            //立即发送
            msgInfo.setScheduledTime(new Date());
        } else {
            //todo 待实现其他类型发送模式
            throw new BaseException("暂不支持其他类型！");
        }

        return msgInfo;
    }
}
