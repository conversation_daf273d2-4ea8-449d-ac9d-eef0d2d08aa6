package com.hxdi.common.core.model;

import java.util.Arrays;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2023/7/4 20:21
 * @description 省域代码
 * @version 1.0
 */
public enum ProvArea {

    BEIJING(11, "北京"),
    TIANJIN(12, "天津"),
    HEBEI(13, "河北省"),
    SHANXI(14, "山西省"),
    INNER_MONGOLIA(15, "内蒙古自治区"),
    LIAONING(21, "辽宁省"),
    JILIN(22, "吉林省"),
    HEILONGJIANG(23, "黑龙江省"),
    SHANGHAI(31, "上海"),
    JIANGSU(32, "江苏省"),
    ZHEJIANG(33, "浙江省"),
    ANHUI(34, "安徽省"),
    FUJIAN(35, "福建省"),
    JIANGXI(36, "江西省"),
    SHANDONG(37, "山东省"),
    HENAN(41, "河南省"),
    HUBEI(42, "湖北省"),
    HUNAN(43, "湖南省"),
    GUANGDONG(44, "广东省"),
    GUANGXI(45, "广西壮族自治区"),
    HAINAN(46, "海南省"),
    CHONGQING(50, "重庆"),
    SICHUAN(51, "四川省"),
    GUIZHOU(52, "贵州省"),
    YUNNAN(53, "云南省"),
    TIBET(54, "西藏自治区"),
    SHAANXI(61, "陕西省"),
    GANSU(62, "甘肃省"),
    QINGHAI(63, "青海省"),
    NINGXIA(64, "宁夏回族自治区"),
    XINJIANG(65, "新疆维吾尔自治区"),
    TAIWAN(71, "台湾"),
    HONG_KONG(81, "香港特别行政区"),
    MACAU(82, "澳门特别行政区"),
    OTHERS(99, "其他")
    ;

    private Integer code;
    private String name;

    ProvArea(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static ProvArea parse(String areaText) {
        Optional<ProvArea> optional = Arrays.stream(values()).filter(e -> areaText.startsWith(e.getName())).findFirst();
        return optional.orElse(ProvArea.OTHERS);
    }

}
