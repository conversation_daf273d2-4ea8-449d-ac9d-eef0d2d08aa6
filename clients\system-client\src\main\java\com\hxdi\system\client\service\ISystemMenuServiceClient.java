package com.hxdi.system.client.service;

import com.hxdi.system.client.model.entity.SystemMenu;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.system.client.model.entity.SystemAction;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 */
 public interface ISystemMenuServiceClient {

    /**
     * 获取菜单列表
     * @return
     */
    @GetMapping("/menu/list")
    ResultBody<List<SystemMenu>> getList(@RequestParam(value = "serviceId",required = false) String serviceId);

    /**
     * 获取菜单下所有操作
     * @param menuId
     * @return
     */
    @GetMapping("/menu/action")
    ResultBody<List<SystemAction>> getMenuAction(@RequestParam("menuId") String menuId);

    /**
     * 获取菜单信息详情
     * @param menuId
     * @return
     */
    @GetMapping("/menu/info")
    ResultBody<SystemMenu> get(@RequestParam("menuId") String menuId);
}
