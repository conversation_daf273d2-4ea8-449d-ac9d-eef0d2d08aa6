package com.hxdi.file.component;

import com.hxdi.file.util.AbstractFileOperator;
import com.hxdi.file.util.dto.FileParams;
import com.hxdi.file.util.dto.FileWriteResult;

import java.io.InputStream;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2023/2/27 18:38
 * @description 文件对象存储服务操作工具
 * @version 1.0
 */
public class FssTemplate {

    private AbstractFileOperator fileOperator;

    public FssTemplate(AbstractFileOperator fileOperator) {
        this.fileOperator = fileOperator;
    }

    /**
     * 存储对象
     * @param is
     * @param params
     * @return
     */
    public Optional<FileWriteResult> storeObject(InputStream is, FileParams params) {
        return fileOperator.storeObject(is, params);
    }

    /**
     * 读取对象
     * @param params
     * @return
     */
    public Optional<InputStream> loadObject(FileParams params) {
        return fileOperator.loadObject(params);
    }

    /**
     * 获取对象访问URL
     * @param params
     * @return
     */
    public Optional<String> getObjectUrl(FileParams params) {
        return fileOperator.getObjectUrl(params);
    }

    /**
     * 删除对象
     * @param params
     */
    public void removeObject(FileParams params) {
        fileOperator.removeObject(params);
    }

    /**
     * 批量删除
     * @param params
     */
    public void removeObjects(FileParams params) {
        fileOperator.removeObjects(params);
    }
}
