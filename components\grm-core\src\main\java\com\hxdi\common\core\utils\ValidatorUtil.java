package com.hxdi.common.core.utils;

import lombok.Getter;
import lombok.Setter;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;

import javax.validation.ConstraintViolation;
import javax.validation.Valid;
import javax.validation.Validation;
import javax.validation.Validator;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/7/15
 */
public class ValidatorUtil {

    private static Validator validator =  Validation.buildDefaultValidatorFactory().getValidator();

    public static <T> void validate(@Valid T pojo) {
        Map<String, String> errorFields = new HashMap<>();
        Set<ConstraintViolation<Object>> constraintViolations = validator.validate(pojo);
        for (ConstraintViolation<Object> error : constraintViolations) {
            errorFields.put(error.getPropertyPath().toString(), error.getInvalidValue()+"#"+error.getMessage());
        }

        Assert.isTrue(ObjectUtils.isEmpty(constraintViolations), errorFields.toString());
    }

    public static void validateList(List<?> dataList) {
        Map<String, List<ErrorInfo>> result = new LinkedHashMap<>();
        for (int i = 0; i < dataList.size(); i++) {
            List<ErrorInfo> errorList = new ArrayList<>();
            Object data = dataList.get(i);
            Set<ConstraintViolation<Object>> errorFields = validator.validate(data);
            for (ConstraintViolation<Object> error : errorFields) {
                ErrorInfo errorInfo = new ErrorInfo();
                errorInfo.setFn(error.getPropertyPath().toString());
                errorInfo.setFv(error.getInvalidValue());
                errorInfo.setErr(error.getMessage());
                errorList.add(errorInfo);
            }

            if (errorList.size() > 0) {
                result.put("Index#" + i, errorList);
            }
        }

        // 如果错误信息过大，将会截取数据，提升消息传输效率
        if (result.size() > 50) {
            result = result.entrySet().stream()
                    .limit(50)
                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (v1, v2) -> v1, LinkedHashMap::new));
        }

        Assert.isTrue(result.isEmpty(), JsonConverter.toJson(result));
    }


    @Getter
    @Setter
    private static class ErrorInfo {
        private String fn;

        private Object fv;

        private String err;
    }
}
