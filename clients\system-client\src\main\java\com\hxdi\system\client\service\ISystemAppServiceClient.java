package com.hxdi.system.client.service;

import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.security.BaseClientInfo;
import com.hxdi.system.client.model.entity.SystemApp;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface ISystemAppServiceClient {

    /**
     * 获取应用列表-不分页（下拉框）
     *
     * @return
     */
    @GetMapping("/appList")
    ResultBody<List<SystemApp>> getAppList(@RequestParam(value = "type", required = false) String appType);

    /**
     * 获取应用详情
     *
     * @param appId
     * @return
     */
    @GetMapping("/app/info")
    ResultBody<SystemApp> get(@RequestParam("appId") String appId);

    /**
     * 根据ApiKey获取应用详情
     * @param apiKey
     * @return
     */
    @GetMapping("/app/info/apiKey")
    ResultBody<SystemApp> getByApiKey(@RequestParam("apiKey") String apiKey);

    /**
     * 获取应用开发配置信息
     *
     * @param clientId
     * @return
     */
    @GetMapping("/app/client/info")
    ResultBody<BaseClientInfo> getByClientId(@RequestParam("clientId") String clientId);
}
