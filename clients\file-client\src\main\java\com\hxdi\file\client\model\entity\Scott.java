package com.hxdi.file.client.model.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.hxdi.common.core.mybatis.base.entity.Entity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/2/22 15:43
 * @description 文件资源对象
 * @version 1.0
 */
@Getter
@Setter
@ToString
@TableName("scott")
public class Scott extends Entity {

    private static final long serialVersionUID = -1672072687716355558L;

    /**
     * 资源ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    private String name;

    private Integer age;

    @TableField(value = "create_id", fill = FieldFill.INSERT)
    private String createId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    public Date createTime;
    /**
     * 修改时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    public Date updateTime;

    public String tenantId;


}
