package com.hxdi.transport.sdk.sign;

import java.io.UnsupportedEncodingException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;

public class SecurityUtil {
    public static final String HMAC_SHA1 = "HmacSHA1";

    public static byte[] hmacSha1(byte[] data, byte[] key, int offset, int len) {
        SecretKeySpec signingKey = new SecretKeySpec(key, "HmacSHA1");
        Mac mac = null;
        try {
            mac = Mac.getInstance("HmacSHA1");
            mac.init(signingKey);
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e.getMessage(), e);
        } catch (InvalidKeyException e) {
            throw new RuntimeException(e.getMessage(), e);
        }
        mac.update(data, offset, len);
        return mac.doFinal();
    }

    public static byte[] hmacSha1(byte[][] datas, byte[] key) {
        SecretKeySpec signingKey = new SecretKeySpec(key, "HmacSHA1");
        Mac mac = null;
        try {
            mac = Mac.getInstance("HmacSHA1");
            mac.init(signingKey);
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e.getMessage(), e);
        } catch (InvalidKeyException e) {
            throw new RuntimeException(e.getMessage(), e);
        }
        byte b;
        int i;
        byte[][] arrayOfByte;
        for (i = (arrayOfByte = datas).length, b = 0; b < i; ) {
            byte[] data = arrayOfByte[b];
            mac.update(data);
            b++;
        }

        return mac.doFinal();
    }

    public static byte[] hmacSha1(String[] datas, byte[] key) {
        SecretKeySpec signingKey = new SecretKeySpec(key, "HmacSHA1");
        Mac mac = null;
        try {
            mac = Mac.getInstance("HmacSHA1");
            mac.init(signingKey);
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e.getMessage(), e);
        } catch (InvalidKeyException e) {
            throw new RuntimeException(e.getMessage(), e);
        }
        try {
            byte b;
            int i;
            String[] arrayOfString;
            for (i = (arrayOfString = datas).length, b = 0; b < i; ) {
                String data = arrayOfString[b];
                mac.update(data.getBytes("UTF-8"));
                b++;
            }

        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e.getMessage(), e);
        }
        return mac.doFinal();
    }

    public static String hmacSha1ToHexStr(byte[] data, byte[] key, int offset, int len) {
        byte[] rawHmac = hmacSha1(data, key, offset, len);
        return SignStringUtil.encodeHexStr(rawHmac);
    }

    public static String hmacSha1ToHexStr(byte[] data, String key, int offset, int len) {
        try {
            return hmacSha1ToHexStr(data, key.getBytes("UTF-8"), offset, len);
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e.getMessage(), e);
        }
    }

    public static String hmacSha1ToHexStr(String str, String key) {
        try {
            byte[] data = str.getBytes("UTF-8");
            return hmacSha1ToHexStr(data, key.getBytes("UTF-8"), 0, data.length);
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e.getMessage(), e);
        }
    }
}
