server:
  port: ${app.port}
  undertow:
    buffer-size: 1024
    direct-buffers: true
  servlet:
    session:
      timeout: PT30S
  compression:
    enabled: true
    min-response-size: 2048


spring:
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
  application:
    name: ${artifactId}
  cloud:
    #手动配置Bus id,
    bus:
      id: ${artifactId}:${app.port}
    gateway:
      discovery:
        locator:
          #表明gateway开启服务注册和发现的功能，并且spring cloud gateway自动根据服务发现为每一个服务创建了一个router，# 这个router将以服务名开头的请求路径转发到对应的服务
          enabled: false
          #将请求路径上的服务名配置为小写（因为服务注册的时候，向注册中心注册时将服务名转成大写的了,比如以/service-hi/*的请求路径被路由转发到服务名为service-hi的服务上
          lowerCaseServiceId: true
    nacos:
      username: ${auth.username}
      password: ${auth.password}
      config:
        namespace: ${config.namespace}
        server-addr: ${config.server-addr}
        group: ${config.group}
        shared-configs:
          - dataId: common.properties
            refresh: true
          - dataId: base_db.properties
            refresh: false
          - dataId: redis.properties
            refresh: false
          - dataId: rabbitmq.properties
            refresh: false
      discovery:
        namespace: ${config.namespace}
        server-addr: ${discovery.server-addr}
        # false:不注册该服务，仅限本地调试使用
        register-enabled: ${non.debug}

  main:
    allow-bean-definition-overriding: true
  profiles:
    active: ${profile.name}
  thymeleaf:
    cache: false
    encoding: UTF-8
    mode: LEGACYHTML5
    prefix: classpath:/templates/
    suffix: .html
  codec:
    max-in-memory-size: 10MB

filters:
  # 熔断降级配置
  - name: Hystrix
    args:
      name: default
      fallbackUri: 'forward:/fallback'

# hystrix 信号量隔离，3秒后自动超时
hystrix:
  command:
    default:
      execution:
        isolation:
          strategy: SEMAPHORE
          thread:
            timeoutInMilliseconds: 60000
  shareSecurityContext: true

management:
  endpoints:
    enabled-by-default: ${endpoints.enable}
    web:
      exposure:
        # 开放部分端点和自定义端点 health,info,fccd60738f4
        include: health,info,fccd60738f4
  endpoint:
    fccd60738f4:
      enabled: true
    health:
      enabled: true
    info:
      enabled: true

cloud:
  # 开放api
  api:
    env: ${profile.name}
    # 参数签名验证
    check-sign: true
    # 访问权限控制
    access-control: ${access.control}
    check-access-alive: true
    # swagger调试,生产环境设为false
    api-debug: ${api.debug}
    # 始终放行
    permit-all:
      - /*/login/**
      - /*/logout/**
      - /*/oauth/**
#      - /actuator/health
#      - /actuator/info
      - /*/file/preview/**
      - /*/ureport/designer
      - /*/bi/ureport/**
      - /*/sensitive/**
      - /*/captcha/**
      - /*/user/third-party/binding
      - /*/app/wxcode2token
      - /*/check_token/token
      - /odx/api/**
      - /grm/deviceInfo/getLiveVideoByPlateNo
      - /grm/apiDahuaDevice/spliceLiveVideo
      - /grm/apiDahuaDevice/getRecordsVideo
      - /odx/apiDahuaDevice/spliceLiveVideo
      - /odx/apiDahuaDevice/getRecordsVideo
      - /odx/internal/api/video/alarm/url
      - /odx/external/api/**
    # 忽略权限鉴定
    authority-ignores:
      - /*/authority/granted/me
      - /*/authority/granted/me/menu
      #- /*/current/user/**
    # 签名URLs
    sign-urls:
      - /odx/api/**
