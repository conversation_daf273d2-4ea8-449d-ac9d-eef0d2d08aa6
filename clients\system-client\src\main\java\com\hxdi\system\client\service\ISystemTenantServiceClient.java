package com.hxdi.system.client.service;

import com.hxdi.common.core.model.ResultBody;
import com.hxdi.system.client.model.entity.SystemTenant;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ISystemTenantServiceClient {

    /**
     * 查询租户信息
     * @param tenantId
     * @return
     */
    @GetMapping("/tenant/get")
    ResultBody<SystemTenant> get(@RequestParam("tenantId") String tenantId);

}
