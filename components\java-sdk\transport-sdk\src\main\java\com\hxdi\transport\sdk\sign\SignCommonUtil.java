package com.hxdi.transport.sdk.sign;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/3/7 15:42
 * @description
 * @version 1.0
 */
public class SignCommonUtil {

    public static String signWithParamsAndUrlPath(String urlPath, Map<String, String> params, String secretKey) {
        List<String> paramValueList = new ArrayList<String>();
        if (params != null) {
            for (Map.Entry<String, String> entry : params.entrySet()) {
                paramValueList.add(entry.getKey() + entry.getValue());
            }
        }
        String[] datas = new String[1 + paramValueList.size()];
        datas[0] = urlPath;
        Collections.sort(paramValueList);
        for (int i = 0; i < paramValueList.size(); i++) {
            datas[i + 1] = paramValueList.get(i);
        }
        byte[] signature = SecurityUtil.hmacSha1(datas, SignStringUtil.toBytes(secretKey));
        return SignStringUtil.encodeHexStr(signature);
    }

    public static String signWithParamsOnly(Map<String, Object> params, String secretKey) {
        List<String> paramValueList = new ArrayList<>();
        if (params != null) {
            for (Map.Entry<String, Object> entry : params.entrySet()) {
                paramValueList.add(entry.getKey() + entry.getValue().toString());
            }
        }
        Collections.sort(paramValueList);
        String[] data = new String[paramValueList.size()];
        paramValueList.toArray(data);
        byte[] signature = SecurityUtil.hmacSha1(data, SignStringUtil.toBytes(secretKey));
        return SignStringUtil.encodeHexStr(signature);
    }
}

