package com.hxdi.common.core.utils;

import com.github.rholder.retry.*;
import com.google.common.base.Predicates;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

/**
 * guava重试工具封装
 * <AUTHOR>
 */
@Slf4j
public class RetryUtils {

    /**
     * 根据结果进行判定是否进行有限的尝试
     * @param callable 任务结果
     * @param unexpectedResult 意外结果
     * @param attemptNumber 重试次数
     * @param delayTime 延迟重试时间
     * @param desc  业务说明
     * @param <T>
     */
    public  static <T> T retryWithLimitedAttempt(Callable<T> callable, T unexpectedResult, int attemptNumber, long delayTime, String desc)throws RetryException{
        Retryer<T> retryer = RetryerBuilder.<T>newBuilder()
                .retryIfResult(Predicates.isNull())
                .retryIfResult(Predicates.equalTo(unexpectedResult))
                .withStopStrategy(StopStrategies.stopAfterAttempt(attemptNumber))
                .withWaitStrategy(WaitStrategies.fixedWait(delayTime, TimeUnit.MILLISECONDS))
                .withRetryListener(new RetryListener() {
                    @Override
                    public <V> void onRetry(Attempt<V> attempt) {
                         if(attempt.hasResult()){
                             log.info("【{}】重试第{}次，重试结果: {}", desc, attempt.getAttemptNumber(), attempt.getResult());
                         }
                         if(attempt.hasException()){
                             log.info("【{}】重试第{}次，异常消息: {}", desc, attempt.getAttemptNumber(), attempt.getExceptionCause().getMessage());
                         }
                    }
                })
                .build();

        try {
            return retryer.call(callable);
        } catch (ExecutionException e) {
            //将可能的业务异常进行转发
            throw new RuntimeException(e.getCause());
        }
    }

    /**
     *
     * @param callable   结果
     * @param unexpectedResult  不是预期结果
     * @param delayTime   延迟重试的时间
     * @param desc  业务描述
     * @param <T>
     * @return
     * @throws RetryException
     */
    public  static <T> T retryWithNeverStop(Callable<T> callable, T unexpectedResult, long delayTime, String desc)throws RetryException{
        Retryer<T> retryer = RetryerBuilder.<T>newBuilder()
                //默认行为
                .retryIfResult(Predicates.isNull())
                .retryIfResult(Predicates.equalTo(unexpectedResult))
                //终止策略
                .withStopStrategy(StopStrategies.neverStop())
                //延迟策略
                .withWaitStrategy(WaitStrategies.fixedWait(delayTime, TimeUnit.MILLISECONDS))
                .withRetryListener(new RetryListener() {
                    @Override
                    public <V> void onRetry(Attempt<V> attempt) {
                        if(attempt.hasResult()){
                            log.info("【{}】重试第{}次，重试结果: {}", desc, attempt.getAttemptNumber(), attempt.getResult());
                        }
                        if(attempt.hasException()){
                            log.info("【{}】重试第{}次，异常消息: {}", desc, attempt.getAttemptNumber(), attempt.getExceptionCause().getMessage());
                        }
                    }
                })
                .build();

        try {
            return retryer.call(callable);
        } catch (ExecutionException e) {
            //将可能的业务异常进行转发
            throw new RuntimeException(e.getCause());
        }
    }

    /**
     * 根据指定异常判定是否进行尝试
     * @param callable
     * @param clazz
     * @param attemptNumber
     * @param delayTime
     * @param <T>
     * @return
     * @throws RetryException
     */
    public  static <T> T retryByException(Callable<T> callable, Class<Throwable> clazz, int attemptNumber, long delayTime, String desc)throws RetryException{
        Retryer<T> retryer = RetryerBuilder.<T>newBuilder()
                .retryIfResult(Predicates.isNull())
                .retryIfExceptionOfType(clazz)
                .withStopStrategy(StopStrategies.stopAfterAttempt(attemptNumber))
                .withWaitStrategy(WaitStrategies.fixedWait(delayTime, TimeUnit.MILLISECONDS))
                .withRetryListener(new RetryListener() {
                    @Override
                    public <V> void onRetry(Attempt<V> attempt) {
                        if(attempt.hasResult()){
                            log.info("【{}】重试第{}次，重试结果: {}", desc, attempt.getAttemptNumber(), attempt.getResult());
                        }
                        if(attempt.hasException()){
                            log.info("【{}】重试第{}次，异常消息: {}", desc, attempt.getAttemptNumber(), attempt.getExceptionCause().getMessage());
                        }
                    }
                })
                .build();

        try {
            return retryer.call(callable);
        } catch (ExecutionException e) {
            //将可能的业务异常进行转发
            throw new RuntimeException(e.getCause());
        }
    }

}
