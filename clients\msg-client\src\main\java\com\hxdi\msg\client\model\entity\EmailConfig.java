package com.hxdi.msg.client.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hxdi.common.core.mybatis.base.entity.AbstractEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 邮件发送配置
 */
@Getter
@Setter
@TableName("msg_email_config")
public class EmailConfig extends AbstractEntity {

    private static final long serialVersionUID=1L;

    @TableId(type = IdType.ASSIGN_ID)
    private String configId;

    @ApiModelProperty(value = "配置名称")
    private String name;

    @ApiModelProperty(value = "发件服务器域名")
    private String smtpHost;

    @ApiModelProperty(value = "发件服务器账户")
    private String smtpUsername;

    @ApiModelProperty(value = "发件服务器密码")
    private String smtpPassword;

    @ApiModelProperty(value = "默认数据0-否 1-是 禁止删除")
    private Integer isPersist;

    @ApiModelProperty(value = "是否为默认 0-否 1-是 ")
    private Integer isDefault;


}
