package com.hxdi.obs.component;

import com.hxdi.obs.util.AbstractFileOperator;
import com.hxdi.obs.util.dto.FileParams;
import com.hxdi.obs.util.dto.FileWriteResult;

import java.io.File;
import java.io.InputStream;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2023/2/27 18:38
 * @description 文件对象存储服务操作工具
 * @version 1.0
 */
public class FssTemplate {

    private AbstractFileOperator fileOperator;

    public FssTemplate(AbstractFileOperator fileOperator) {
        this.fileOperator = fileOperator;
    }

    /**
     * 对象流上传
     * @param is
     * @param params
     * @return
     */
    public Optional<FileWriteResult> storeObject(InputStream is, FileParams params) {
        return fileOperator.storeObject(is, params);
    }

    /**
     * 文件对象上传
     * @param file
     * @param params
     * @return
     */
    public Optional<FileWriteResult> storeObject(File file, FileParams params) {
        return fileOperator.storeObject(file, params);
    }

    /**
     * 分段上传
     * @param file
     * @param params
     * @return
     */
    public Optional<FileWriteResult> multipartStoreObject(File file, FileParams params) {
        return fileOperator.multipartStoreObject(file, params);
    }

    /**
     * 读取对象
     * @param params
     * @return
     */
    public Optional<InputStream> loadObject(FileParams params) {
        return fileOperator.loadObject(params);
    }

    /**
     * 获取对象访问URL
     * @param params
     * @return
     */
    public Optional<String> getObjectSignedUrl(FileParams params) {
        return fileOperator.getObjectSignedUrl(params);
    }

    /**
     * 删除对象
     * @param params
     */
    public void removeObject(FileParams params) {
        fileOperator.removeObject(params);
    }

    /**
     * 批量删除
     * @param params
     */
    public void removeObjects(FileParams params) {
        fileOperator.removeObjects(params);
    }
}
