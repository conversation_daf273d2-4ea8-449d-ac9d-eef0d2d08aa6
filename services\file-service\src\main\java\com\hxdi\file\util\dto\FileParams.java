package com.hxdi.file.util.dto;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/28 09:26
 * @description 文件对象操作入参封装
 * @version 1.0
 */
@Getter
@Setter
public class FileParams {

    /**
     * 对象名称/文件名称
     */
    private String objectName;

    /**
     * 业务前缀编码
     */
    private String prefix;


    /**
     * 文件对象集合
     */
    private List<String> objects;

    public FileParams() {}

    public FileParams(String objectName) {
        this.objectName = objectName;
    }

    public FileParams(String objectName, String prefix) {
        this.objectName = objectName;
        this.prefix = prefix;
    }

    public FileParams(List<String> objects) {
        this.objects = objects;
    }
}
