package com.hxdi.system.client.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hxdi.common.core.mybatis.base.entity.AbstractEntity;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@TableName("system_authority_app")
public class SystemAuthorityApp extends AbstractEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 权限ID
     */
    private String authorityId;

    /**
     * 应用ID
     */
    private String appId;

    /**
     * 过期时间:null表示长期
     */
    private Date expireTime;

}
