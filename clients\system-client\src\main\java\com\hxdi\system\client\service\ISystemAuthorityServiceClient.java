package com.hxdi.system.client.service;

import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.security.BaseAuthority;
import com.hxdi.system.client.model.AuthorityApi;
import com.hxdi.system.client.model.AuthorityMenu;
import com.hxdi.system.client.model.AuthorityResource;
import com.hxdi.system.client.model.entity.SystemAuthorityAction;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 权限控制API接口
 *
 * <AUTHOR>
 */
public interface ISystemAuthorityServiceClient {
    /**
     * 获取所有访问权限列表
     */
    @GetMapping("/authority/access")
    ResultBody<List<AuthorityResource>> findAuthorityResource();


    /**
     * 获取所有访问权限列表
     */
    @GetMapping("/authority/service")
    ResultBody<List<AuthorityResource>> findAuthorityResourceByServiceId(@RequestParam(value = "serviceId", required = true) String serviceId);

    /**
     * 获取接口权限列表
     *
     * @param serviceId
     * @return
     */
    @GetMapping("/authority/api")
    ResultBody<List<AuthorityApi>> findAuthorityApi(@RequestParam(value = "serviceId", required = false) String serviceId);

    /**
     * 获取菜单权限列表
     *
     * @return
     */
    @GetMapping("/authority/menu")
    ResultBody<List<AuthorityMenu>> findAuthorityMenu(@RequestParam(name="serviceId",required=false)String serviceId);

    /**
     * 获取功能权限列表
     *
     * @param actionId
     * @return
     */
    @GetMapping("/authority/action")
    ResultBody<List<SystemAuthorityAction>> findAuthorityAction(@RequestParam(value = "actionId") String actionId);

    /**
     * 获取角色已分配权限
     *
     * @param roleId
     * @return
     */
    @GetMapping("/authority/role")
    ResultBody<List<BaseAuthority>> findAuthorityRole(@RequestParam(value = "roleId") String roleId,@RequestParam(value="serviceId",required=false)String serviceId);


    /**
     * 获取用户菜单列表
     * @param userId
     * @param root
     * @return
     */
    @GetMapping("/authority/user/menu")
    ResultBody<List<AuthorityMenu>> findAuthorityMenuByUser(@RequestParam(value = "userId") String userId, @RequestParam(value = "root")  Boolean root,@RequestParam(value="ApiKey",required=false)String ApiKey);

    /**
     * 获取用户已分配权限
     *
     * @param userId
     * @return
     */
    @GetMapping("/authority/user")
    ResultBody<List<BaseAuthority>> findAuthorityUser(@RequestParam(value = "userId") String userId);

    /**
     * 获取应用已分配接口权限
     *
     * @param appId
     * @return
     */
    @GetMapping("/authority/app")
    ResultBody<List<BaseAuthority>> findAuthorityApp(@RequestParam(value = "appId") String appId);

}
