package com.hxdi.tenant.configuration;

import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.TenantLineInnerInterceptor;
import com.hxdi.database.autoconfigure.BaseMybatisAppAutoConfiguration;
import com.hxdi.database.autoconfigure.MybatisAppConfigurer;
import com.hxdi.database.properties.DatabaseProperties;
import com.hxdi.tenant.handle.BaseTenantLineHandler;
import com.hxdi.tenant.handle.CustomTenantLineInnerInterceptor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;

/**
 * <AUTHOR>
 * @date 2023/3/13 15:16
 * @description 多租户插件配置
 * @version 1.0
 */
@ConditionalOnBean(BaseMybatisAppAutoConfiguration.class)
@AutoConfigureAfter(BaseMybatisAppAutoConfiguration.class)
@Slf4j
public class TenantAutoConfiguration {

    private DatabaseProperties databaseProperties;

    public TenantAutoConfiguration(DatabaseProperties databaseProperties) {
        this.databaseProperties = databaseProperties;
    }

    @Autowired
    public void registerTenantInterceptor(MybatisPlusInterceptor mybatisPlusInterceptor, MybatisAppConfigurer mybatisPlusConfigurer) {
        CustomTenantLineInnerInterceptor tenantLineInnerInterceptor = new CustomTenantLineInnerInterceptor();
        BaseTenantLineHandler baseTenantLineHandler = new BaseTenantLineHandler(databaseProperties);
        tenantLineInnerInterceptor.setTenantLineHandler(baseTenantLineHandler);

        mybatisPlusConfigurer.setTenantLineInnerInterceptor(tenantLineInnerInterceptor);
        mybatisPlusInterceptor.setInterceptors(mybatisPlusConfigurer.assemblyInnerInterceptors());
        log.info("启用基于Column模式的多租户插件");
    }

}

