package com.hxdi.msg.service;

import com.hxdi.msg.client.model.entity.MsgUsers;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

public interface MsgUsersService extends IService<MsgUsers>{
    /**
     * 批量保存消息用户关系(即实际意义上的发送消息)
     *
     * @param msgId   消息ID
     * @param userIds 用户ID列表
     */
    void saveMsgUsers(String msgId, List<String> userIds);

    /**
     * 删除消息用户关系
     * @param msgId 消息ID
     * @return 是否删除成功
     */
    boolean removeMsgUsers(List<String> msgId);

    /**
     * 根据消息ID查询接收用户ID列表
     * @param msgId
     * @return
     */
    List<MsgUsers> selectUserIdsByMsgId(List<String> msgId);
}
