package com.hxdi.common.core.utils;



import com.hxdi.common.core.utils.codec.MD5Util;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class RegexUtils {

    private static Map<String, Pattern> cachedPatterns = new HashMap<>();

    public static boolean find(String regex, String input, boolean isCached){
        Pattern pattern;
        if(isCached){
            String cachedKey = MD5Util.toMD5(regex);
            pattern = cachedPatterns.get(cachedKey);
            if(pattern == null){
                pattern = Pattern.compile(regex);
                cachedPatterns.put(cachedKey, pattern);
            }
        } else {
            pattern = Pattern.compile(regex);
        }

        Matcher matcher = pattern.matcher(input);
        return matcher.find();
    }

    public static boolean find(String regex, String input){
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(input);
        return matcher.find();
    }


    /**
     * 根据规则和数据获取替换之后的值
     * @param regex
     * @param rule
     * @param replacementData
     * @return
     */
    public static String appendReplacement(String regex, String rule, Map<String, String> replacementData){
        String cachedKey = MD5Util.toMD5(regex);
        Pattern pattern = cachedPatterns.get(cachedKey);
        if(pattern == null){
            pattern = Pattern.compile(regex);
            cachedPatterns.put(cachedKey, pattern);
        }

        Matcher matcher = pattern.matcher(rule);
        StringBuffer sb = new StringBuffer();
        while (matcher.find()) {
            String keyText = matcher.group(1);
            matcher.appendReplacement(sb, replacementData.get(keyText));
        }

        matcher.appendTail(sb);
        return sb.toString();
    }

    /**
     * 检查手机号格式是否正确
     * @param phoneNo
     * @return
     */
    public static boolean checkPhoneNo(String phoneNo){
        String regex = "1[3456789]\\d{9}";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(phoneNo);
        return matcher.matches();
    }

    /**
     * 检查手机号是否有效，如果有效返回有效的手机号，如果无效返回null
     * @param phoneNo
     * @return
     */
    public static String checkPhoneIsValid(String phoneNo){
        if(CommonUtils.isEmpty(phoneNo)){
            return null;
        }
        phoneNo = phoneNo.trim();
        String regex = "(0+1|1)[3456789]\\d{9}";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(phoneNo);
        if(matcher.matches()){
            if(phoneNo.startsWith("0")){
                phoneNo = phoneNo.replaceFirst("0+", "");
            }
            return phoneNo;
        }else{
            return null;
        }
    }

    public static void main(String[] args) {
        Map<String, String> map = new HashMap<>();
        map.put("prefix", "aa");
        map.put("suffix", "bb");
        String result = appendReplacement("\\#\\{(.+?)\\}", "#{prefix}#{suffix}", map);
        System.out.println(result);
    }
}
