package com.hxdi.admin.service.feign;

import com.hxdi.common.core.model.ResultBody;
import com.hxdi.system.client.constants.SystemConstants;
import com.hxdi.system.client.service.ISystemUserServiceClient;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

@Component
@FeignClient(value = SystemConstants.SYSTEM_SERVER)
public interface SystemUserServiceClient extends ISystemUserServiceClient {

    @PostMapping("/user/auth/fail")
    ResultBody<Integer> authFail(@RequestParam(value = "account") String account);

}
