package com.hxdi.admin.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.hxdi.common.core.constants.StrPool;
import com.hxdi.common.core.exception.BizExp;
import com.hxdi.common.core.security.oauth2.SocialClientDetails;
import com.hxdi.common.core.security.oauth2.SocialProperties;
import com.hxdi.common.core.utils.RestUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2023/7/26 11:00
 * @description 微信小程序后台服务
 * @version 1.0
 */
@Service("wechatMiniService")
@Slf4j
public class WechatMiniServiceImpl {

    @Autowired
    private RestUtil restUtil;

    @Autowired
    private SocialProperties SocialProperties;

    /**
     * 获取手机号URL
     */
    private final static String PHONE_NUMBER_URL = "https://api.weixin.qq.com/wxa/business/getuserphonenumber?access_token=%s";

    /**
     * 获取token的URL
     */
    private final static String ACCESS_TOKEN_URL = "https://api.weixin.qq.com/cgi-bin/stable_token";

    /**
     * 获取openId的URL
     */
    private final static String OPEN_ID_URL = "https://api.weixin.qq.com/sns/jscode2session?appid=%s&secret=%s&js_code=%s&grant_type=authorization_code";


    public String getAccessToken() {
        Map<String, Object> params = new LinkedHashMap<>();
        params.put("grant_type", "client_credential");
        params.put("appid", getClientDetails().getClientId());
        params.put("secret", getClientDetails().getClientSecret());

        Map<String, String> headers = new LinkedHashMap<>();
        headers.put(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);
        JSONObject result = JSONObject.parseObject(restUtil.post(ACCESS_TOKEN_URL, headers, params, String.class));

        Optional<Integer> state = Optional.ofNullable(result.getInteger("errcode"));
        if (state.isPresent()) {
            BizExp.pop(state.get(), "微信登录异常");
        }

        if (result.containsKey(StrPool.ACCESS_TOKEN)) {
            log.debug("Wechat getAccessToken:", result);
            return result.getString(StrPool.ACCESS_TOKEN);
        }
        return "";
    }

    public String getOpenId(String code) {
        String url = String.format(OPEN_ID_URL, getClientDetails().getClientId(), getClientDetails().getClientSecret(), code);
        JSONObject result = JSONObject.parseObject(restUtil.get(url, null, null, String.class));
        Optional<Integer> state = Optional.ofNullable(result.getInteger("errcode"));
        if (state.isPresent()) {
            if (state.get().intValue() == 40029) {
                BizExp.pop(state.get(), "JSCODE无效");
            } else if (state.get().intValue() == 40226) {
                BizExp.pop(state.get(), "高风险等级用户，禁止登录");
            } else {
                BizExp.pop(state.get(), "系统繁忙，请稍候再试");
            }
        }

        if (result.containsKey("openid")) {
            log.debug("Wechat getOpenId:", result);
            return result.getString("openid");
        } else {
            log.error(result.toString());
            BizExp.pop("登录失败，openid为空");
        }

        return "";
    }

    /**
     * 临时方法
     * @deprecated
     * @param code
     * @return
     */
    public String getOpenId2(String code) {
        String url = String.format(OPEN_ID_URL, "wx83c60f48b80d9df6", "7ff176e68de30c8f55860b96dc153489", code);
        JSONObject result = JSONObject.parseObject(restUtil.get(url, null, null, String.class));
        Optional<Integer> state = Optional.ofNullable(result.getInteger("errcode"));
        if (state.isPresent()) {
            if (state.get().intValue() == 40029) {
                BizExp.pop(state.get(), "JSCODE无效");
            } else if (state.get().intValue() == 40226) {
                BizExp.pop(state.get(), "高风险等级用户，禁止登录");
            } else {
                BizExp.pop(state.get(), "系统繁忙，请稍候再试");
            }
        }

        if (result.containsKey("openid")) {
            log.debug("Wechat getOpenId:", result);
            return result.getString("openid");
        }

        return "";
    }

    public String getUserPhoneNumber(String code) {
        String accessToken = getAccessToken();
        String url = String.format(PHONE_NUMBER_URL, accessToken);
        Map<String, Object> params = new LinkedHashMap<>();
        params.put("code", code);
        Map<String, String> headers = new LinkedHashMap<>();
        headers.put(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);
        JSONObject result = JSONObject.parseObject(restUtil.post(url,headers, params, String.class));
        Optional<Integer> state = Optional.ofNullable(result.getInteger("errcode"));
        if (state.isPresent() && state.get().intValue() != 0) {
            if (state.get().intValue() == 40029) {
                BizExp.pop(state.get(), "JSCODE无效");
            } else {
                BizExp.pop(state.get(), "系统繁忙，请稍候再试");
            }
        }

        if (result.containsKey("phone_info")) {
            log.debug("Wechat getPhoneNumber:", result);
            return result.getJSONObject("phone_info").getString("purePhoneNumber");
        }

        return "";
    }

    /**
     * 获取客户端配置信息
     *
     * @return
     */
    public SocialClientDetails getClientDetails() {
        return SocialProperties.getClient().get("wechat-mini");
    }

}
