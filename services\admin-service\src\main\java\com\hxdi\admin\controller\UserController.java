package com.hxdi.admin.controller;

import com.alibaba.fastjson.JSONObject;
import com.hxdi.admin.service.feign.SystemAuthorityServiceClient;
import com.hxdi.admin.service.feign.SystemUserServiceClient;
import com.hxdi.admin.service.impl.OAuthRequest;
import com.hxdi.admin.service.impl.WechatMiniServiceImpl;
import com.hxdi.common.core.constants.CommonConstants;
import com.hxdi.common.core.constants.ErrorCode;
import com.hxdi.common.core.constants.StrPool;
import com.hxdi.common.core.exception.BizExp;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.common.core.model.UserType;
import com.hxdi.common.core.properties.CommonProperties;
import com.hxdi.common.core.security.BaseUserDetails;
import com.hxdi.common.core.security.SecurityHelper;
import com.hxdi.system.client.constants.SystemConstants;
import com.hxdi.system.client.model.AuthorityMenu;
import com.hxdi.system.client.model.entity.SystemUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.security.oauth2.provider.token.store.redis.RedisTokenStore;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Api(tags = "用户安全管理")
@RestController
@Slf4j
public class UserController {

    @Autowired
    private SystemUserServiceClient systemUserServiceClient;
    @Autowired
    private SystemAuthorityServiceClient systemAuthorityServiceClient;
    @Resource
    private RedisTokenStore redisTokenStore;

    @Autowired
    private WechatMiniServiceImpl wechatMiniService;

    @Autowired
    private OAuthRequest oAuthRequest;

    @Autowired
    private CommonProperties commonProperties;

    @ApiOperation("修改当前登录用户密码")
    @GetMapping("/current/user/rest/password")
    public ResultBody restPassword(@RequestParam(value = "password") String password) {
        systemUserServiceClient.updatePassword(SecurityHelper.getUser().getUserId(), password);
        return ResultBody.ok();
    }

    @ApiOperation("修改当前登录用户基本信息")
    @PostMapping("/current/user/update")
    public ResultBody updateUserInfo(@RequestParam(value = "nickName") String nickName,
                                     @RequestParam(value = "userDesc", required = false) String comment,
                                     @RequestParam(value = "avatar", required = false) String avatar) {

        BaseUserDetails baseUserDetails = SecurityHelper.getUser();
        SystemUser user = new SystemUser();
        user.setUserId(baseUserDetails.getUserId());
        user.setNickName(nickName);
        user.setRemark(comment);
        user.setAvatar(avatar);
        systemUserServiceClient.save(user);
        baseUserDetails.setNickName(nickName);
        baseUserDetails.setAvatar(avatar);
        SecurityHelper.updateUser(redisTokenStore, baseUserDetails);
        return ResultBody.ok();
    }

    @ApiOperation("获取当前登录用户已分配菜单权限")
    @GetMapping("/current/user/menu")
    public ResultBody<List<AuthorityMenu>> findAuthorityMenu(@RequestParam(value="ApiKey",required=false)String ApiKey) {
        BaseUserDetails session = SecurityHelper.getUser();
        return systemAuthorityServiceClient.findAuthorityMenuByUser(session.getUserId(), UserType.SUPER.match(session.getUserType()), ApiKey);
    }

//    @ApiOperation("修改第三方用户手机号")
//    @PostMapping("/current/user/third-party/changeMobile")
//    public ResultBody changeMobile(@RequestParam(value = "mobile") String mobile) {
//        ResultBody<Map<String, Object>> data = bizDriverServiceClient.loadDriverByMobile(mobile);
//        if (ErrorCode.OK.getCode() != data.getCode()) {
//            BizExp.pop("请通过管理后台登记司机信息");
//        }
//
//        systemUserServiceClient.changeMobile(mobile);
//        return ResultBody.ok();
//    }

//    @ApiOperation("绑定第三方账号")
//    @PostMapping("/user/third-party/binding")
//    public Object bindThirdPartyAccount(@RequestParam("openId") String openId,
//                                @RequestParam("code") String code,
//                                @RequestParam("ApiKey") String ApiKey,
//                                @RequestParam(value = "userType", required = false, defaultValue = "normal") String userType,
//                                @RequestHeader HttpHeaders httpHeaders) {
//
//        if (userType.equals("normal")) {
//            // 一般用户绑定操作
//            if (commonProperties.isWxEnable()) {
//                // 小程序发布审核
//                openId = "ouNp06_rjSXKzt9BktY46XjPyEx0";
//            } else {
//                String phone = wechatMiniService.getUserPhoneNumber(code);
//                ResultBody<Map<String, Object>> data = systemUserServiceClient.thirdPartyBinding(phone, openId, SystemConstants.ACCOUNT_TYPE_WECHAT);
//                if (ErrorCode.ACCOUNT_NOT_EXIST.getCode() == data.getCode()) {
//                    BizExp.pop("当前账号异常，请联系管理员");
//                }
//
//                if (ErrorCode.OK.getCode() != data.getCode()) {
//                    return data;
//                }
//            }
//
//            JSONObject result = oAuthRequest.usernamePassword2Token(openId, CommonConstants.WECHAT_PW_PREFIX + openId, ApiKey, httpHeaders);
//            if (result.containsKey(StrPool.ACCESS_TOKEN)) {
//                return ResultBody.ok().data(result);
//            }
//
//            return result;
//
//        } else {
//            // 司机账户绑定操作
//            return bindingSjUser(openId, code, ApiKey, httpHeaders);
//        }
//    }

//    @ApiOperation("绑定司机微信小程序账号")
//    @PostMapping("/user/third-party/sj/binding")
//    public Object bindingSjUser(@RequestParam(value = "openId") String openId,
//                                    @RequestParam(value = "code") String code,
//                                    @RequestParam(value = "ApiKey") String ApiKey,
//                                    @RequestHeader HttpHeaders httpHeaders) {
//
//        if (commonProperties.isWxEnable()) {
//            // 小程序发布审核
//            openId = "ouNp06_rjSXKzt9BktY46XjPyEx0";
//        } else {
//            String phone = wechatMiniService.getUserPhoneNumber(code);
//            ResultBody<Map<String, Object>> data = bizDriverServiceClient.loadDriverByMobile(phone);
//
//            String nickName = "";
//            String mobile = phone;
//            if (ErrorCode.OK.getCode() == data.getCode()) {
//                Map<String, Object> map = data.getData();
//                nickName = (String) map.get("driverName");
//            }
//
//            ResultBody saveUserData = systemUserServiceClient.saveThirdPartyUser(openId, nickName, UserType.THIRD_PARTY.getName(), mobile, UserType.THIRD_PARTY.getDesc(), "100099", SystemConstants.ACCOUNT_TYPE_WECHAT);
//            if (ErrorCode.OK.getCode() != saveUserData.getCode()) {
//                return saveUserData;
//            }
//        }
//
//        JSONObject result = oAuthRequest.usernamePassword2Token(openId, CommonConstants.WECHAT_PW_PREFIX + openId, ApiKey, httpHeaders);
//        if (result.containsKey(StrPool.ACCESS_TOKEN)) {
//            return ResultBody.ok().data(result);
//        }
//
//        return result;
//    }
}
