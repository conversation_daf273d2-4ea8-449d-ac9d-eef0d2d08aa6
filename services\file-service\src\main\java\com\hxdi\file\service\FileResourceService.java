package com.hxdi.file.service;

import com.hxdi.common.core.mybatis.base.service.IBaseService;
import com.hxdi.file.client.model.entity.FileResource;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/22 16:34
 * @description 文件管理服务接口
 * @version 1.0
 */
public interface FileResourceService extends IBaseService<FileResource> {

    /**
     * 预览
     * @param resId
     * @param request
     * @param response
     */
    void preview(String resId, HttpServletRequest request, HttpServletResponse response);

    /**
     * 文件资源信息查询
     * @param resIds
     * @return
     */
    List<FileResource> queryResInfo(String[] resIds);


    /**
     * 保存并存储
     * @param file
     * @param category  资源分类
     * @return
     */
    FileResource saveAndStore(MultipartFile file, String category);

    /**
     * 读取文件并存储
     * @param fileUrl
     */
    FileResource readAndStore(String fileUrl);

    /**
     * 删除
     * @param resIds
     */
    void clean(String[] resIds);

    /**
     * 下载
     * @param resId
     * @param request
     * @param response
     */
    void download(String resId, HttpServletRequest request, HttpServletResponse response);

    /**
     * 读取IO流
     * @param resId
     * @param request
     * @param response
     */
    void getStream(String resId, HttpServletRequest request, HttpServletResponse response);

    /**
     * 下载&压缩
     * @param resIds
     * @param request
     * @param response
     */
    void downloadZip(String[] resIds, HttpServletRequest request, HttpServletResponse response);


}
