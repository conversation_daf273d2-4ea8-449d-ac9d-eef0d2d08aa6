package com.hxdi.common.core.utils.codec;

import cn.hutool.core.codec.Base64;
import cn.hutool.crypto.CryptoException;
import cn.hutool.crypto.SecureUtil;
import com.hxdi.common.core.utils.CommonUtils;
import lombok.extern.slf4j.Slf4j;

import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @date 2022/8/10 14:16
 * @description RES 对称加密工具类   key:68f81dd297dc48fd92c68c4c548cd6dd
 * @version 1.0
 */
@Slf4j
public class AESProxy {

    private static final byte[] KEYS = "68f81dd297dc48fd92c68c4c548cd6dd".getBytes(StandardCharsets.UTF_8);

    public static String encrypt(String data) {
        return (CommonUtils.isEmpty(data)) ? null : encrypt(data.getBytes(StandardCharsets.UTF_8));
    }

    public static String encrypt(byte[] data) {
        byte[] encryptBytes = SecureUtil.aes(KEYS).encrypt(data);
        return Base64.encode(encryptBytes, StandardCharsets.UTF_8);
    }

    public static String decrypt(String data) {
        try {
            return (CommonUtils.isEmpty(data)) ? null : decrypt(Base64.decode(data, StandardCharsets.UTF_8));
        } catch (Exception e) {
            log.error("AES decrypt({}) failure!", data);
            return data;
        }
    }

    public static String decrypt(byte[] data) {
        byte[] decryptBytes = SecureUtil.aes(KEYS).decrypt(data);
        return new String(decryptBytes, StandardCharsets.UTF_8);
    }

    public static void main(String[] args) {
//        String s = "13567248594";
//        String ss = encrypt(s);
//        System.out.println(ss);

        try {
            String sss = decrypt("idgoH9zfTpkbixz/xWa0mQ==");
            System.out.println(sss);
        } catch (CryptoException e) {
            e.printStackTrace();
            System.out.println("解密异常");
        }

    }
}
