package com.hxdi.gateway.filter;

import com.hxdi.gateway.service.AuthTokenService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.server.ServerWebExchange;
import org.springframework.web.server.WebFilter;
import org.springframework.web.server.WebFilterChain;
import reactor.core.publisher.Mono;

/**
 * 认证授权过滤器，提供延时服务
 * <AUTHOR>
 */
@Slf4j
public class AuthPostCheckFilter implements WebFilter {

    private AuthTokenService authTokenService;

    public AuthPostCheckFilter(AuthTokenService authTokenService) {
        this.authTokenService = authTokenService;
    }

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, WebFilterChain chain) {
        return Mono.fromRunnable(() -> authTokenService.check(exchange))
                .then(chain.filter(exchange));
    }
}

