package com.hxdi.common.core.mybatis;

import com.hxdi.common.core.exception.BaseException;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.sql.*;
import java.util.Map;

/**
 * Blob数据类型转换处理器
 * <AUTHOR>
 * @param <T>
 */
public class BlobTypeHandler<T> extends BaseTypeHandler<T> {

    private static Logger logger = LoggerFactory.getLogger(BlobTypeHandler.class);

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, T parameter, JdbcType jdbcType) throws SQLException {
        logger.info("【BlobTypeHandler】insert或者update时处理blob字段");

        ObjectOutputStream oos = null;
        try {
            ByteArrayInputStream bis = null;
            if (parameter instanceof byte[]){
                bis = new ByteArrayInputStream((byte[]) parameter);
            } else if (parameter instanceof Map<?, ?>){
                ByteArrayOutputStream bos = new ByteArrayOutputStream();
                oos = new ObjectOutputStream(bos);
                oos.writeObject(parameter);
                bis = new ByteArrayInputStream(bos.toByteArray());
            }

            ps.setBinaryStream(i, bis, bis.available());
        } catch (IOException e) {
            logger.error("【BlobTypeHandler】insert或者update字段转换", e);
            throw new BaseException("Blob Encoding Error!");
        } finally {
            if (oos != null) {
                try {
                    oos.close();
                } catch (IOException e) {}
            }
        }
    }

    @Override
    public T getNullableResult(ResultSet rs, String columnName) throws SQLException {
        logger.info("【BlobTypeHandler】query查询时处理blob字段");
        return convert(rs.getBlob(columnName));
    }

    @Override
    public T getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        logger.info("【BlobTypeHandler】query查询时处理blob字段");
        return convert(cs.getBlob(columnIndex));
    }

    @Override
    public T getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        logger.info("【BlobTypeHandler】query查询时处理blob字段");
        return convert(rs.getBlob(columnIndex));

    }

    private T convert(Blob blobLocator) throws SQLException {
        if (blobLocator != null && blobLocator.length() != 0L) {
            InputStream binaryStream = blobLocator.getBinaryStream();
            ObjectInputStream ois = null;
            try {
                if (binaryStream != null && (!(binaryStream instanceof ByteArrayInputStream) || ((ByteArrayInputStream)binaryStream).available() != 0)) {
                    ois = new ObjectInputStream(binaryStream);
                    return (T)ois.readObject();
                }
            } catch (ClassNotFoundException | IOException e){
                logger.error("【BlobTypeHandler】字段转换异常");
            } finally {
                if (ois != null){
                    try {
                        ois.close();
                    } catch (IOException e) {}
                }
            }
        }

        return null;
    }

}
