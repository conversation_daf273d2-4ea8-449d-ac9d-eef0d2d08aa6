package com.hxdi.common.autoconfigure;

import com.hxdi.common.core.annotation.RequestMappingScan;
import com.hxdi.common.core.exception.BaseRestResponseErrorHandler;
import com.hxdi.common.core.exception.GlobalExceptionHandler;
import com.hxdi.common.core.filter.BaseFilter;
import com.hxdi.common.core.interceptor.DataPermissionAspect;
import com.hxdi.common.core.properties.CommonProperties;
import com.hxdi.common.core.properties.FssProperties;
import com.hxdi.common.core.properties.ObsProperties;
import com.hxdi.common.core.security.oauth2.SocialProperties;
import com.hxdi.common.core.utils.RedisUtil;
import com.hxdi.common.core.utils.RestHttpClient;
import com.hxdi.common.core.utils.RestUtil;
import com.hxdi.common.core.utils.SpringContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.cloud.bus.BusProperties;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.client.RestTemplate;

/**
 * 默认配置类
 *
 * <AUTHOR>
 */
@Slf4j
@Configuration
@EnableConfigurationProperties({CommonProperties.class,  SocialProperties.class, FssProperties.class, ObsProperties.class})
public class AutoConfiguration {

    @Bean
    @ConditionalOnMissingBean(ServerConfiguration.class)
    public ServerConfiguration serverConfiguration(){
        return new ServerConfiguration();
    }

    /**
     * xss过滤
     * body缓存
     *
     * @return
     */
    @Bean
    public FilterRegistrationBean baseFilter() {
        FilterRegistrationBean filterRegistrationBean = new FilterRegistrationBean(new BaseFilter());
        log.info("BaseFilter [{}]", filterRegistrationBean);
        return filterRegistrationBean;
    }


    /**
     * 默认加密配置
     *
     * @return
     */
    @Bean
    @Primary
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }


    /**
     * Spring上下文工具配置
     *
     * @return
     */
    @Bean
    @ConditionalOnMissingBean(SpringContextHolder.class)
    public SpringContextHolder springContextHolder() {
        SpringContextHolder holder = new SpringContextHolder();
        log.info("Spring上下文静态工具类 [{}]", holder);
        return holder;
    }

    /**
     * 统一异常处理配置
     *
     * @return
     */
    @Bean
    @Primary
    @ConditionalOnMissingBean(GlobalExceptionHandler.class)
    public GlobalExceptionHandler globalExceptionHandler() {
        GlobalExceptionHandler exceptionHandler = new GlobalExceptionHandler();
        log.info("全局异常处理器 [{}]", exceptionHandler);
        return exceptionHandler;
    }

    /**
     * 资源扫描类
     * @return
     */
    @Bean
    @ConditionalOnMissingBean(RequestMappingScan.class)
    public RequestMappingScan resourceAnnotationScan(RedisTemplate redisTemplate) {
        RequestMappingScan scan = new RequestMappingScan(redisTemplate);
        log.info("资源扫描类.[{}]", scan);
        return scan;
    }

    /**
     * 自定义Oauth2请求类
     *
     * @param commonProperties
     * @return
     */
    @Bean
    @ConditionalOnMissingBean(RestUtil.class)
    public RestUtil baseRestTemplate(RestTemplate restTemplate, CommonProperties commonProperties, BusProperties busProperties, ApplicationEventPublisher publisher) {
        RestUtil restUtil = new RestUtil(restTemplate, commonProperties, busProperties, publisher);
        log.info("RestUtil [{}]", restUtil);
        return restUtil;
    }

    @Bean
    public RestTemplate restTemplate() {
        RestTemplate restTemplate = new RestTemplate();
        //设置自定义ErrorHandler
        restTemplate.setErrorHandler(new BaseRestResponseErrorHandler());
        log.info("RestTemplate [{}]", restTemplate);
        return restTemplate;
    }

    /**
     * http 请求客户端封装工具类
     * @return
     */
    @Bean
    public RestHttpClient restHttpClient() {
        return new RestHttpClient();
    }

}
