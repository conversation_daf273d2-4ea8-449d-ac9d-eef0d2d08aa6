<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hxdi.msg.mapper.MsgReadMarkMapper">
  <resultMap id="BaseResultMap" type="com.hxdi.msg.client.model.entity.MsgReadMark">
    <!--@mbg.generated-->
    <!--@Table MSG_READ_MARK-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="MSG_ID" jdbcType="VARCHAR" property="msgId" />
    <result column="USER_ID" jdbcType="VARCHAR" property="userId" />
    <result column="READ_TIME" jdbcType="TIMESTAMP" property="readTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, MSG_ID, USER_ID, READ_TIME
  </sql>

  <select id="markBatchAsRead_1" resultType="int">
    INSERT INTO MSG_READ_MARK (ID, MSG_ID, USER_ID, READ_TIME)
    SELECT REPLACE(UUID(),'-',''), mu.MSG_ID, #{userId}, NOW()
    FROM MSG_USERS mu
    WHERE mu.MSG_ID IN
    <foreach item="item" index="index" collection="msgIds" open="(" separator="," close=")">
      #{item}
    </foreach>
    AND mu.USER_ID = #{userId}
    ON DUPLICATE KEY UPDATE READ_TIME = NOW()
    </select>

  <select id="markBatchAsRead_2" resultType="int">
    INSERT INTO MSG_READ_MARK (ID, MSG_ID, USER_ID, READ_TIME)
    SELECT REPLACE(UUID(),'-',''), USER_ID, #{msgId}, NOW()
    FROM MSG_USERS
    WHERE USER_ID IN
    <foreach item="item" index="index" collection="userIds" open="(" separator="," close=")">
        #{item}
    </foreach>
    AND MSG_ID = #{msgId}
    ON DUPLICATE KEY UPDATE READ_TIME = NOW()
  </select>

  <select id="selectClassifyMessageNumber" resultType="com.hxdi.msg.client.model.vo.ClassifyMessageNumber">
    SELECT
    SUM(CASE WHEN mi.ALERM_TYPE = 1 AND mrm.MSG_ID IS NULL THEN 1 ELSE 0 END) AS todoNumber,
    SUM(CASE WHEN mi.ALERM_TYPE = 2 AND mrm.MSG_ID IS NULL THEN 1 ELSE 0 END) AS warningNumber,
    SUM(CASE WHEN mi.ALERM_TYPE = 3 AND mrm.MSG_ID IS NULL THEN 1 ELSE 0 END) AS tipNumber
    FROM MSG_INFO mi
    JOIN MSG_USERS mu ON mi.ID = mu.MSG_ID
    LEFT JOIN (
    SELECT MSG_ID
    FROM MSG_READ_MARK
    WHERE USER_ID = #{userId} OR USER_ID = #{userIdAll}
    ) mrm ON mi.ID = mrm.MSG_ID
    WHERE (mu.USER_ID = #{userId} OR mu.USER_ID = #{userIdAll})
    AND mi.ENABLED = 1
    AND mi.STATE = 1
  </select>
</mapper>