package com.hxdi.obs.util;

import cn.hutool.core.util.StrUtil;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2023/3/31 10:42
 * @description 支持的文件类型
 * @version 1.0
 */
public enum FileType {

    IMAGE(new HashSet<>(Arrays.asList("jpg", "jpeg", "png", "gif"))),
    VIDEO(new HashSet<>(Arrays.asList("mp4", "mpeg", "mpg", "avi", "mov", "flv", "dav"))),
    FILE(new HashSet<>(Arrays.asList("doc", "docx", "xls", "xlsx", "ppt", "pptx", "pdf", "txt", "zip", "rar")))
    ;

    Set<String> set;

    FileType(Set<String> set) {
        this.set = set;
    }

    public boolean support(String type) {
        if (StrUtil.isEmpty(type)) {
            return false;
        }
        return this.set.contains(type.toLowerCase());
    }

    public static boolean anySupport(String type) {
        boolean support = false;
        for (FileType fileType : values()) {
            if (fileType.support(type)) {
                support = true;
                break;
            }
        }

        return support;
    }
}
