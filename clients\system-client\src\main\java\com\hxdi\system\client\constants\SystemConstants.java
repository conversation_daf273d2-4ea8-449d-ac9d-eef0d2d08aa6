package com.hxdi.system.client.constants;

/**
 * 通用权限常量
 *
 * <AUTHOR>
 */
public interface SystemConstants {

    /**
     * 服务名称
     */
    String SYSTEM_SERVER = "system-service";

    /**
     * 状态:0-无效 1-有效
     */
    int ENABLED = 1;
    int DISABLED = 0;



    /**
     * 系统用户类型:超级管理员=super-admin 租户管理员=tenant-admin 普通管理员=admin 普通用户=normal  第三方用户=thirdparty
     */
    String USER_TYPE_THIRD_PARTY = "third-party";

    /**
     * 账号状态
     * 0:禁用、1:正常、2:锁定
     */
    int ACCOUNT_STATUS_DISABLE = 0;
    int ACCOUNT_STATUS_NORMAL = 1;
    int ACCOUNT_STATUS_LOCKED = 2;

    /**
     * 账号类型:
     * username:系统用户名、email：邮箱、mobile：手机号、qq：QQ号、wechat：微信号
     */
    String ACCOUNT_TYPE_USERNAME = "username";
    String ACCOUNT_TYPE_EMAIL = "email";
    String ACCOUNT_TYPE_MOBILE = "mobile";
    String ACCOUNT_TYPE_WECHAT = "wechat";

    /**
     * 账号域
     */
    String ACCOUNT_DOMAIN_ADMIN = "@admin.com";
    String ACCOUNT_DOMAIN_SITE= "@site.com";

    /**
     * 组织类型
     */
    Integer ORGAN_TYPE_PARTY = 0;
    Integer ORGAN_TYPE_CORP = 1;
    Integer ORGAN_TYPE_DEPT = 2;

    /**
     * 系统消息接收方类型
     */
    String SYSTEM_MSG_TYPE_ORGAN = "ORGAN";
    String SYSTEM_MSG_TYPE_ROLE = "ROLE";
    String SYSTEM_MSG_TYPE_USER = "USER";
    String SYSTEM_MSG_TYPE_ALL = "ALL";
}
