package com.hxdi.common.core.utils;

import com.hxdi.common.core.utils.support.SFunction;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cglib.beans.BeanMap;
import org.springframework.objenesis.instantiator.util.ClassUtils;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.lang.invoke.SerializedLambda;
import java.lang.reflect.Method;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/2/26 11:04 AM
 * @description
 * @version 1.0
 */
@Slf4j
public class BeanUtil {

    private static Map<String, SerializedLambda> CLASS_LAMBDA_CACHE = new ConcurrentHashMap<>();

    private BeanUtil(){}

    public static Map<String, Object> beanToMap(Object bean) {
        return null == bean ? null : new HashMap<>(BeanMap.create(bean));
    }

    public static <T> T mapToBean(Map<String, Object> map, Class<T> clazz) {
        T bean = ClassUtils.newInstance(clazz);
        BeanMap.create(bean).putAll(map);
        return bean;
    }

    public static <T, K> T lambdaMapToBean(Map<SFunction<K, ?>, ?> map, Class<T> clazz) {
        T bean = ClassUtils.newInstance(clazz);
        Map<String, ?> newMap = map.entrySet().stream().collect(Collectors.toMap(e -> resolveFiledName(e.getKey()), Map.Entry::getValue));
        BeanMap.create(bean).putAll(newMap);
        return bean;
    }

    public static <T> List<Map<String, Object>> beansToMaps(List<T> beans) {
        return CollectionUtils.isEmpty(beans) ? Collections.emptyList() : beans.stream().map(BeanUtil::beanToMap).collect(Collectors.toList());
    }

    public static <T> List<T> mapsToBeans(List<Map<String, Object>> maps, Class<T> clazz) {
        return CollectionUtils.isEmpty(maps) ? Collections.emptyList() : maps.stream().map((e) -> mapToBean(e, clazz)).collect(Collectors.toList());
    }

    public static <T> String resolveFiledName(SFunction<T, ?> fn) {
        SerializedLambda lambda = resolveLambda(fn);
        String getMethodName = lambda.getImplMethodName();
        if (getMethodName.startsWith("get")){
            getMethodName = getMethodName.substring(3);
        } else if (getMethodName.startsWith("is")){
            getMethodName = getMethodName.substring(2);
        }

        return firstToLowerCase(getMethodName);
    }

    static SerializedLambda resolveLambda(Serializable fn){
        SerializedLambda lambda = CLASS_LAMBDA_CACHE.get(fn.getClass());
        if (lambda == null){
            try {
                Method method = fn.getClass().getDeclaredMethod("writeReplace");
                method.setAccessible(Boolean.TRUE);
                lambda = (SerializedLambda) method.invoke(fn);
                String key = lambda.getImplClass() + "." + lambda.getImplMethodName();
                CLASS_LAMBDA_CACHE.put(key, lambda);
            } catch (Exception e){
                log.error("SerializedLambda Error", e);
            }
        }

        return lambda;
    }

    static String firstToLowerCase(String s){
        if (Character.isLowerCase(s.charAt(0))){
            return s;
        } else {
            return Character.toLowerCase(s.charAt(0)) + s.substring(1);
        }
    }
}
