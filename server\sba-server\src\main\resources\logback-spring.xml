<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="false" scan="false">
    <property name="application.name" value="sba-server"/>
    <property name="log.path" value="logs/${application.name}"/>
    <!-- Console log output -->
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <!--格式化输出：%d表示日期，%thread表示线程名，%-5level：级别从左显示5个字符宽度%msg：日志消息，%n是换行符-->
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%15thread] %highlight(%5level) %cyan(%-60.60logger{60}) : %highlight(%msg) %n</pattern>
        </encoder>
    </appender>

    <appender name="file" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/startup.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/%d{yyyy-MM}/history.out.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <maxFileSize>50MB</maxFileSize>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%date [%15thread] %5level [%-65.65logger{65}] %file:%line : %msg %n</pattern>
        </encoder>
    </appender>

    <!--<appender name="error" class="ch.qos.logback.core.rolling.RollingFileAppender">-->
        <!--<file>${log.path}/error.log</file>-->
        <!--<rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">-->
            <!--<fileNamePattern>${log.path}/%d{yyyy-MM}/error.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>-->
            <!--<maxFileSize>50MB</maxFileSize>-->
            <!--<maxHistory>30</maxHistory>-->
        <!--</rollingPolicy>-->
        <!--<encoder>-->
            <!--<pattern>%date [%15thread] %5level [%-65.65logger{65}] %file:%line : %msg %n</pattern>-->
        <!--</encoder>-->
        <!--<filter class="ch.qos.logback.classic.filter.ThresholdFilter">-->
            <!--<level>ERROR</level>-->
        <!--</filter>-->
    <!--</appender>-->

    <!--开发环境:打印控制台-->
    <springProfile name="dev,local">
        <logger name="com.hxdi" level="debug"/>
        <root level="info">
            <appender-ref ref="console"/>
        </root>
    </springProfile>

    <!--正式环境-->
    <springProfile name="test,uat,release">
        <logger name="com.hxdi" level="debug"/>
        <root level="info">
            <appender-ref ref="file"/>
        </root>
    </springProfile>
</configuration>
