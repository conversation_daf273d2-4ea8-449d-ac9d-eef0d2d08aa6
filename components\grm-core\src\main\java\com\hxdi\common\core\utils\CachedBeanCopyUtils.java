package com.hxdi.common.core.utils;

import com.hxdi.common.core.utils.codec.MD5Util;
import org.springframework.cglib.beans.BeanCopier;
import org.springframework.cglib.core.Converter;

import java.util.Collection;
import java.util.HashMap;
import java.util.Map;

/**
 * 用于实现 model <--> bean 之间的相互转换
 * cglib动态代理实现bean拷贝
 * 通过缓存的方式进一步提升copy性能
 * <AUTHOR>
 */
public class CachedBeanCopyUtils {

    private static final Map<String, BeanCopier> BEAN_COPIERS = new HashMap<>();

    public static <S,T> T copy(S s, T t){
        CommonUtils.assertNotNull(s, "");
        CommonUtils.assertNotNull(t, "");

        String key = MD5Util.toMD5(s.getClass().getName() + t.getClass().getName());
        BeanCopier beanCopier;
        if(!BEAN_COPIERS.containsKey(key)){
            beanCopier = BeanCopier.create(s.getClass(), t.getClass(), false);
            BEAN_COPIERS.put(key, beanCopier);
        }else{
            beanCopier = BEAN_COPIERS.get(key);
        }

        beanCopier.copy(s, t, null);
        return t;
    }

//    /**
//     * 用于bean -> model时，替换手动赋值Model固定属性
//     * @param s 源对象
//     * @param t 目标对象
//     *
//     * @param <S>
//     * @param <T>
//     * @return
//     */
//    public static <S,T> T copyEnhancer(S s, T t, OptType optType){
//        copy(s, t);
//
//        if(t instanceof BaseModel){
//
//            BaseModel typedThis = (BaseModel) t;
//            switch (optType){
//                case ADD:
//                    if (CommonUtils.isEmpty(typedThis.getCreateTime())){
//                        typedThis.setCreateTime(new Date());
//                    }
//                    if (CommonUtils.isEmpty(typedThis.getUpdateTime())){
//                        typedThis.setUpdateTime(new Date());
//                    }
//                    if (CommonUtils.isEmpty(typedThis.getCreateId())){
//                        typedThis.setCreateId(SecurityHelper.getUserId());
//                    }
//                    if (CommonUtils.isEmpty(typedThis.getUpdateId())){
//                        typedThis.setUpdateId(SecurityHelper.getUserId());
//                    }
//                    // 新增时，添加tenantId
//                    typedThis.setTenantId(SecurityHelper.getTenantId());
//                    break;
//                case UPDATE:
//                    typedThis.setUpdateTime(new Date());
//                    typedThis.setUpdateId(SecurityHelper.getUserId());
//
//                    break;
//            }
//
//        }
//
//        return t;
//    }

    public static <S extends Collection<SE>, T extends Collection<TE>, SE, TE> T copyCollection(S s, T t, Class<TE> cls){
        if (s == null){
            return null;
        }
        s.forEach(se -> {
            try {
                TE te = cls.newInstance();
                copy(se, te);
                t.add(te);
            } catch (Exception e) {
                throw new RuntimeException("BeanCopy fail!");
            }
        });
        return t;
    }

    public static <S extends Map<String, SV>, T extends Map<String, TV>, SV, TV> T copyMap(S s, T t, Class<TV> cls){
        s.forEach((sk, sv) -> {
            try {
                if (sv == null){
                    return;
                }

                TV tv = cls.newInstance();
                copy(sv, tv);

                t.put(sk, tv);
            } catch (Exception e) {
                throw new RuntimeException("BeanCopy fail!");
            }
        });

        return t;
    }

    /**
     * 基于目标对象的属性出发，如果与源对象有相同名称的属性，则调一次convert方法
     */
    static class CopyConverter implements Converter{

        @Override
        public Object convert(Object source, Class returnType, Object setterName) {
            return source;
        }
    }




}
