package com.hxdi.msg.controller;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.msg.client.model.MsgSmsTemplate;
import com.hxdi.msg.client.model.MsgSmsUser;
import com.hxdi.msg.service.IMsgSmsUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import com.hxdi.common.core.mybatis.base.controller.BaseController;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-03
 */
@Api(tags = "模板关联用户")
@RestController
@RequestMapping("/msgSmsUser")
public class MsgSmsUserController  {

    @Autowired
    private IMsgSmsUserService smsUserService;

    @ApiOperation("添加数据")
    @PostMapping("/add")
    public ResultBody add(@RequestBody List<MsgSmsUser>  msgSmsUsers) {
        if (!CollectionUtils.isEmpty(msgSmsUsers)){
            String tplId = msgSmsUsers.get(0).getTplId();
            smsUserService.remove(new QueryWrapper<MsgSmsUser>().eq("tpl_id", tplId));
            smsUserService.saveBatch(msgSmsUsers);
        }
        return ResultBody.ok();
    }

}
