package com.hxdi.system.client.model;

import com.google.common.collect.Lists;
import com.hxdi.common.core.security.BaseAuthority;
import com.hxdi.system.client.model.entity.SystemAccount;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * @author: liuyadu
 * @date: 2018/11/12 11:35
 * @description:
 */
public class UserInfo extends SystemAccount implements Serializable {
    private static final long serialVersionUID = 6717800085953996702L;

    /**
     * 用户数据权限
     */
    private String userScopeRole;
    private Integer userScopeRoleType;

    private Collection<Map<String, Object>> roles = Lists.newArrayList();
    /**
     * 用户权限
     */
    private Collection<BaseAuthority> authorities = Lists.newArrayList();

    /**
     * 昵称
     */
    private String nickName;

    private String userType;

    private String mobile;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 所属组织
     */
    private String organId;
    private String organName;
    private String organPath;
    private String organCode;
    private Integer organType;

    /**
     * 父级组织
     */
    private String pid;
    private String pname;

    /**
     * 租户ID/编码
     */
    private String tenantId;
    private String tenantCode;
    private String tenantName;

    private List<String> scopes;


    @Override
    public String getTenantId() {
        return tenantId;
    }

    @Override
    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public Collection<BaseAuthority> getAuthorities() {
        return authorities;
    }

    public void setAuthorities(Collection<BaseAuthority> authorities) {
        this.authorities = authorities;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public Collection<Map<String, Object>> getRoles() {
        return roles;
    }

    public void setRoles(Collection<Map<String, Object>> roles) {
        this.roles = roles;
    }

    public String getTenantCode() {
        return tenantCode;
    }

    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode;
    }

    public String getTenantName() {
        return tenantName;
    }

    public void setTenantName(String tenantName) {
        this.tenantName = tenantName;
    }

    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getOrganId() {
        return organId;
    }

    public void setOrganId(String organId) {
        this.organId = organId;
    }

    public String getOrganName() {
        return organName;
    }

    public void setOrganName(String organName) {
        this.organName = organName;
    }

    public String getOrganPath() {
        return organPath;
    }

    public void setOrganPath(String organPath) {
        this.organPath = organPath;
    }

    public String getOrganCode() {
        return organCode;
    }

    public void setOrganCode(String organCode) {
        this.organCode = organCode;
    }

    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }

    public String getPname() {
        return pname;
    }

    public void setPname(String pname) {
        this.pname = pname;
    }

    public String getUserScopeRole() {
        return userScopeRole;
    }

    public void setUserScopeRole(String userScopeRole) {
        this.userScopeRole = userScopeRole;
    }

    public Integer getUserScopeRoleType() {
        return userScopeRoleType;
    }

    public void setUserScopeRoleType(Integer userScopeRoleType) {
        this.userScopeRoleType = userScopeRoleType;
    }

    public List<String> getScopes() {
        return scopes;
    }

    public void setScopes(List<String> scopes) {
        this.scopes = scopes;
    }

    public Integer getOrganType() {
        return organType;
    }

    public void setOrganType(Integer organType) {
        this.organType = organType;
    }
}
