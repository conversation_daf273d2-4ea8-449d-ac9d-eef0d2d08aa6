package com.hxdi.job.job;

import com.alibaba.fastjson.JSONObject;
import com.hxdi.common.autoconfigure.ServerConfiguration;
import com.hxdi.common.core.security.oauth2.SocialClientDetails;
import com.hxdi.common.core.security.oauth2.SocialProperties;
import com.hxdi.common.core.utils.RestUtil;
import com.hxdi.common.core.utils.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.quartz.Job;
import org.quartz.JobDataMap;
import org.quartz.JobExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.client.ServiceInstance;
import org.springframework.cloud.client.loadbalancer.LoadBalancerClient;
import org.springframework.http.*;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 微服务远程调用任务
 *
 * <AUTHOR>
 */
@Slf4j
public class HttpJob implements Job {
    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private RestUtil restUtil;

    /**
     * 负载均衡
     */
    @Autowired
    private LoadBalancerClient loadBalancerClient;


    @Autowired
    private SocialProperties socialProperties;
    @Autowired
    ServerConfiguration serverConfiguration;


    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws RuntimeException {

        JobDataMap dataMap = jobExecutionContext.getJobDetail().getJobDataMap();
        String serviceId = dataMap.getString("serviceId");
        String method = dataMap.getString("method");
        method = StringUtil.isBlank(method) ? "POST" : method;
        String path = dataMap.getString("path");
        String contentType = dataMap.getString("contentType");
        contentType = StringUtil.isBlank(contentType) ? MediaType.APPLICATION_FORM_URLENCODED_VALUE : contentType;
        String body = dataMap.getString("body");
        ServiceInstance serviceInstance = loadBalancerClient.choose(serviceId);
        ServiceInstance instance = loadBalancerClient.choose("admin-service");
        // 获取服务实例
        if (serviceInstance == null) {
            throw new RuntimeException(String.format("%s服务暂不可用", serviceId));
        }


        String url_token = instance.getUri() + "/oauth/token";
        log.info("[url_token]"+url_token);
        JSONObject token = getToken(url_token);
        String accessToken = token.get("access_token").toString();
        log.info("[accessToken]"+accessToken);
        String url = String.format("%s%s", serviceInstance.getUri(), path);
        HttpHeaders headers = new HttpHeaders();
        headers.set("Authorization","bearer "+accessToken);
        HttpMethod httpMethod = HttpMethod.resolve(method.toUpperCase());
        HttpEntity requestEntity = null;
        headers.setContentType(MediaType.parseMediaType(contentType));
        if (contentType.contains(MediaType.APPLICATION_JSON_VALUE)) {
            // json格式
            requestEntity = new HttpEntity(body, headers);
        } else {
            // 表单形式
            // 封装参数，千万不要替换为Map与HashMap，否则参数无法传递
            MultiValueMap<String, String> params = new LinkedMultiValueMap();
            if (StringUtil.isNotBlank(body)) {
                Map data = JSONObject.parseObject(body, Map.class);
                params.putAll(data);
                requestEntity = new HttpEntity(params, headers);
            }else {
                requestEntity = new HttpEntity(params, headers);
            }
        }
        ResponseEntity<String> result = restTemplate.exchange(url, httpMethod, requestEntity, String.class);
        dataMap.put("result",result);
        log.debug("==> url[{}] method[{}] data=[{}] result=[{}]", url, httpMethod, requestEntity, result);
    }


    public JSONObject getToken(String url) {
        SocialClientDetails clientDetails = socialProperties.getClient().get("admin");

        // 使用oauth2密码模式登录.
        Map<String, Object> postParameters = new LinkedHashMap<>();
        postParameters.put("username", "admin");
        postParameters.put("password", "123456");
        postParameters.put("client_id", clientDetails.getClientId());
        postParameters.put("client_secret", clientDetails.getClientSecret());
        postParameters.put("grant_type", "password");
        // 添加参数区分,第三方登录
        postParameters.put("login_type", "password");
        // 使用客户端的请求头,发起请求
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        JSONObject result = restUtil.post(url, headers.toSingleValueMap(), postParameters, JSONObject.class);
        return result;
    }
}
