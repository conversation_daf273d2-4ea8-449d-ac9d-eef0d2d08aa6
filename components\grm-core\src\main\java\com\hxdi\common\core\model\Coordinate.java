package com.hxdi.common.core.model;

import lombok.Getter;
import lombok.Setter;

import java.util.Objects;

/**
 * 经纬度坐标
 */
@Getter
@Setter
public class Coordinate {

    public final static Coordinate NON = new Coordinate();

    /**
     * 经度
     */
    private String lon;

    /**
     * 纬度
     */
    private String lat;

    public Coordinate(){
        this.lon = "0.00";
        this.lat = "0.00";
    }

    public Coordinate(String lon, String lat){
        this.lon = lon;
        this.lat = lat;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }

        if (obj instanceof Coordinate) {
            Coordinate another = (Coordinate) obj;
            return Objects.equals(this.lon, another.lon) && Objects.equals(this.lat, another.lat);
        }

        return false;
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.lon, this.lat);
    }
}
