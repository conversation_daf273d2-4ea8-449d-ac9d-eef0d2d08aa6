package com.hxdi.msg.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hxdi.msg.client.model.entity.MsgReadMark;
import com.hxdi.msg.client.model.vo.ClassifyMessageNumber;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

@Mapper
public interface MsgReadMarkMapper extends BaseMapper<MsgReadMark> {
    int markBatchAsRead_1(@Param("msgIds") List<String> msgIds,@Param("userId") String userId);

    int markBatchAsRead_2(@Param("msgId") String msgIds,@Param("userIds") List<String> userId);

    ClassifyMessageNumber selectClassifyMessageNumber(@Param("userId") String userId,@Param("userIdAll") String userIdAll);
}