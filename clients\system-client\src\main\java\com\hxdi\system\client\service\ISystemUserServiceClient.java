package com.hxdi.system.client.service;

import com.hxdi.common.core.model.ResultBody;
import com.hxdi.system.client.model.UserInfo;
import com.hxdi.system.client.model.entity.SystemRole;
import com.hxdi.system.client.model.entity.SystemUser;
import org.springframework.web.bind.annotation.*;

import java.util.List;

public interface ISystemUserServiceClient {


    /**
     * 获取账号登录信息
     *
     * @param account
     * @return
     */
    @PostMapping("/user/query")
    ResultBody<UserInfo> loadUser(@RequestParam(value = "account") String account);

    /**
     * 检查用户是否存在
     * @param account
     * @return
     */
    @PostMapping("/user/check")
    ResultBody<Integer> checkUser(@RequestParam(value = "account") String account);

    /**
     * 获取用户详情
     * @param userId
     * @return
     */
    @GetMapping("/user/info")
    ResultBody<SystemUser> get(@RequestParam(value = "userId") String userId);

    /**
     * 添加/修改系统用户
     *
     * @param systemUser
     * @return
     */
    @PostMapping("/user/save")
    ResultBody save(@RequestBody SystemUser systemUser);

    /**
     * 获取用户已分配角色
     *
     * @param userId
     * @return
     */
    @GetMapping("/user/roles")
    ResultBody<List<SystemRole>> getUserRoles(@RequestParam(value = "userId") String userId);

    /**
     * 注销账号
     * @return
     */
    @DeleteMapping("/user/unregister")
    ResultBody unregister();
    /**
     * 删除
     * @param userId
     * @return
     */
    @DeleteMapping("/user/remove")
    ResultBody remove(@RequestParam("userId") String userId);

    /**
     * 修改用户密码
     */
    @PostMapping("/user/update/password")
    ResultBody updatePassword(@RequestParam(value = "userId") String userId, @RequestParam(value = "password") String password);


    /**
     * 注册第三方用户
     * @param username
     * @param nickname
     * @param userType
     * @param mobile
     * @param comment
     * @param accountType
     * @return
     */
    @PostMapping("/user/third-party/registry")
    ResultBody saveThirdPartyUser(@RequestParam("username") String username,
                                         @RequestParam("nickname") String nickname,
                                         @RequestParam("userType") String userType,
                                         @RequestParam("mobile") String mobile,
                                         @RequestParam("comment") String comment,
                                         @RequestParam("role") String role,
                                         @RequestParam("accountType") String accountType);


    /**
     * 第三方账号绑定
     * @param mobile
     * @param openId
     * @param type
     * @return
     */
    @PostMapping("/user/third-party/bind")
    ResultBody thirdPartyBinding(@RequestParam("mobile") String mobile,
                                        @RequestParam("openId") String openId,
                                        @RequestParam("type") String type);

    /**
     * 第三方账号更换手机号
     * @param mobile
     * @return
     */
    @PostMapping("/user/third-party/changeMobile")
    ResultBody changeMobile(@RequestParam("mobile") String mobile);

    /**
     * 根据手机号删除用户信息
     * @param mobile
     * @return
     */
    @PostMapping("/user/deleteSilentUserInfos")
    ResultBody deleteSilentUserInfos(@RequestParam("mobile") String mobile);
}
