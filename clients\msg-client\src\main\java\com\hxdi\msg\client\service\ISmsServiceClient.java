package com.hxdi.msg.client.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hxdi.common.core.model.ResultBody;
import com.hxdi.msg.client.model.MsgSmsConditon;
import com.hxdi.msg.client.model.MsgSmsTemplate;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Map;

/**
 * 推送通知
 * <AUTHOR>
 */
public interface ISmsServiceClient {


    /**
     * 短信通知
     * @param
     * @return
     */
    @PostMapping("/msgSmsTemplate/sendMsg")
    ResultBody sendMsg(@RequestBody Map<String,String> map);

    /**
     * 消息通知
     * @param map
     * @return
     */
    @PostMapping("/msgSmsTemplate/sendMsgNotification")
    ResultBody sendMsgNotification(@RequestBody Map<String,String> map);

    /**
     *
     * @param condition
     * @return
     */
    @PostMapping("/msgSmsTemplate/configPageList")
    ResultBody<Page<MsgSmsTemplate>> configPageList(@RequestBody MsgSmsConditon condition);
}
