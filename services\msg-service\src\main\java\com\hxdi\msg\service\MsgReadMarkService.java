package com.hxdi.msg.service;

import com.hxdi.msg.client.model.entity.MsgReadMark;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hxdi.msg.client.model.vo.ClassifyMessageNumber;

import java.util.List;

public interface MsgReadMarkService extends IService<MsgReadMark>{

    /**
     * 标记消息为已读
     *
     * @param msgId  消息ID
     * @param userId 用户ID
     */
    void markAsRead(String msgId, String userId);

    /**
     * 批量标记消息为已读（用户操作）
     *
     * @param msgIds 消息ID列表
     * @param userId 用户ID
     */
    void markBatchAsRead(List<String> msgIds, String userId);

    /**
     * 批量标记消息为已读（用于自动已读操作）
     *
     * @param msgIds 消息ID
     * @param userId 用户ID列表
     */
    void markBatchAsRead(String msgIds, List<String> userId);

    /**
     * 获取分类的消息数量
     * @param userId 用户ID
     * @return
     */
    ClassifyMessageNumber getClassifyMessageNumber(String userId);

}
