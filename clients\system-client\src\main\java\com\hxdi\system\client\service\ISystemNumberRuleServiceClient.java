package com.hxdi.system.client.service;


import com.hxdi.system.client.model.dto.BusinessCode;
import com.hxdi.system.client.model.dto.BusinessCodeParams;
import com.hxdi.common.core.model.ResultBody;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

public interface ISystemNumberRuleServiceClient {

    @PostMapping("/rule/getNumber")
    ResultBody<BusinessCode> getNumber(@RequestBody BusinessCodeParams params);
}
