package com.hxdi.obs.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.file.client.model.entity.FileResource;
import com.hxdi.file.client.model.entity.VideoResource;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/2/22 16:33
 * @description
 * @version 1.0
 */
@Repository
@Mapper
public interface FileResourceMapper extends SuperMapper<VideoResource> {

    /**
     * 查询资源路径
     * @param resId
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    @Select("select `path`, extname, etag, content_type from fss_video_resource where id = #{resId}")
    VideoResource selectResourcePathAndType(String resId);

    /**
     * 查询视频告警资源
     * @param alarmId
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
    @Select("select `path`, extname, etag, content_type from fss_video_resource where alarm_id = #{alarmId}")
    VideoResource selectVideoAlarmResource(String alarmId);
}
