package com.hxdi.gateway.configuration;

import com.hxdi.common.core.constants.CommonConstants;
import com.hxdi.gateway.exception.JsonAccessDeniedHandler;
import com.hxdi.gateway.exception.JsonAuthenticationEntryPoint;
import com.hxdi.gateway.exception.JsonSignatureDeniedHandler;
import com.hxdi.gateway.filter.*;
import com.hxdi.gateway.locator.ResourceLocator;
import com.hxdi.gateway.oauth2.RedisAuthenticationManager;
import com.hxdi.gateway.service.AccessLogService;
import com.hxdi.gateway.service.AuthTokenService;
import com.hxdi.gateway.service.OnlineStatService;
import com.hxdi.gateway.service.feign.SystemAppServiceClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.security.config.web.server.SecurityWebFiltersOrder;
import org.springframework.security.config.web.server.ServerHttpSecurity;
import org.springframework.security.core.context.ReactiveSecurityContextHolder;
import org.springframework.security.oauth2.provider.token.TokenStore;
import org.springframework.security.oauth2.provider.token.store.redis.RedisTokenStore;
import org.springframework.security.oauth2.server.resource.web.server.ServerBearerTokenAuthenticationConverter;
import org.springframework.security.web.server.SecurityWebFilterChain;
import org.springframework.security.web.server.authentication.AuthenticationWebFilter;
import org.springframework.security.web.server.authentication.ServerAuthenticationEntryPointFailureHandler;
import org.springframework.security.web.server.context.SecurityContextServerWebExchange;
import org.springframework.web.cors.reactive.CorsUtils;
import org.springframework.web.server.ServerWebExchange;
import org.springframework.web.server.WebFilter;
import org.springframework.web.server.WebFilterChain;
import reactor.core.publisher.Mono;

import java.util.HashSet;
import java.util.Set;

/**
 * oauth2资源服务器配置
 *
 * @author: liuyadu
 * @date: 2019/5/8 18:45
 * @description:
 */
@Configuration
public class ResourceServerConfiguration {

    private static final String MAX_AGE = "18000L";

    @Autowired
    private RedisConnectionFactory redisConnectionFactory;
    @Autowired
    private ResourceLocator apiResourceLocator;
    @Autowired
    private ApiProperties apiProperties;
    @Autowired
    private AccessLogService accessLogService;
    @Autowired
    private OnlineStatService onlineStatService;
    @Autowired
    private AuthTokenService authTokenService;
    @Autowired
    private SystemAppServiceClient systemAppServiceClient;

    private static Set<String> allowOrigins = new HashSet<String>(){
        {
//            add("https://**************");
//            add("https://fzggw.zj.gov.cn");
//            add("https://www.zjyjwz.top");
//            add("https://mapi.zjzwfw.gov.cn");
        }
    };

    /**
     * 跨域配置
     */
    public WebFilter corsFilter() {
        return (ServerWebExchange ctx, WebFilterChain chain) -> {
            ServerHttpRequest request = ctx.getRequest();
            if (CorsUtils.isCorsRequest(request)) {
                HttpHeaders requestHeaders = request.getHeaders();
                ServerHttpResponse response = ctx.getResponse();
                HttpMethod requestMethod = requestHeaders.getAccessControlRequestMethod();
                HttpHeaders headers = response.getHeaders();

                String origin = requestHeaders.getOrigin();
                boolean pass = false;
                if (apiProperties.getEnv().equals(CommonConstants.RELEASE_VERSION)) {
                    // 生产环境
                    pass = allowOrigins.contains(origin);
                } else {
                    // 开发测试环境
                    pass = true;
                }

                if (pass) {
                    headers.add(HttpHeaders.ACCESS_CONTROL_ALLOW_ORIGIN, origin);
                    headers.addAll(HttpHeaders.ACCESS_CONTROL_ALLOW_HEADERS, requestHeaders.getAccessControlRequestHeaders());
                    if (requestMethod != null) {
                        headers.add(HttpHeaders.ACCESS_CONTROL_ALLOW_METHODS, requestMethod.name());
                    }
                    headers.add(HttpHeaders.ACCESS_CONTROL_ALLOW_CREDENTIALS, "true");
                    headers.add(HttpHeaders.ACCESS_CONTROL_EXPOSE_HEADERS, "*");
                    headers.add(HttpHeaders.ACCESS_CONTROL_MAX_AGE, MAX_AGE);
                }

                if (request.getMethod() == HttpMethod.OPTIONS) {
                    response.setStatusCode(HttpStatus.OK);
                    return Mono.empty();
                }
            }
            return chain.filter(ctx);
        };
    }

    @Bean
    public TokenStore tokenStore() {
        return new RedisTokenStore(redisConnectionFactory);
    }

    @Bean
    public SecurityWebFilterChain springWebFilterChain(ServerHttpSecurity http, TokenStore tokenStore) {
        // 自定义oauth2 认证, 使用redis读取token,而非jwt方式
        JsonAuthenticationEntryPoint entryPoint = new JsonAuthenticationEntryPoint(accessLogService);
        JsonAccessDeniedHandler accessDeniedHandler = new JsonAccessDeniedHandler(accessLogService);
        AccessManager accessManager = new AccessManager(apiResourceLocator, apiProperties);

        AuthenticationWebFilter oauth2Filter = new AuthenticationWebFilter(new RedisAuthenticationManager(tokenStore));
        oauth2Filter.setServerAuthenticationConverter(new ServerBearerTokenAuthenticationConverter());
        oauth2Filter.setAuthenticationFailureHandler(new ServerAuthenticationEntryPointFailureHandler(entryPoint));
        oauth2Filter.setAuthenticationSuccessHandler((webFilterExchange, authentication) -> {
            ServerWebExchange exchange = webFilterExchange.getExchange();
            SecurityContextServerWebExchange securityContextServerWebExchange = new SecurityContextServerWebExchange(exchange,
                    ReactiveSecurityContextHolder.getContext().subscriberContext(ReactiveSecurityContextHolder.withAuthentication(authentication)));
            return webFilterExchange.getChain().filter(securityContextServerWebExchange);
        });

        http.httpBasic().disable()
            .csrf().disable()
            .authorizeExchange()
            .pathMatchers("/").permitAll()
            // 动态权限验证
            .anyExchange().access(accessManager)
            .and()
            .exceptionHandling()
            .accessDeniedHandler(accessDeniedHandler)
            .authenticationEntryPoint(entryPoint)
            .and()
            // 日志前置过滤器
            .addFilterAt(new PreRequestFilter(), SecurityWebFiltersOrder.FIRST)
            // 跨域过滤器
            .addFilterAt(corsFilter(), SecurityWebFiltersOrder.CORS)
            // 签名验证过滤器
            .addFilterAt(new PreSignatureFilter(systemAppServiceClient, apiProperties, new JsonSignatureDeniedHandler(accessLogService), redisConnectionFactory), SecurityWebFiltersOrder.CSRF)
            // 访问验证前置过滤器
            .addFilterAt(new PreCheckFilter(accessManager, accessDeniedHandler), SecurityWebFiltersOrder.CSRF)
            // 访问授权检查
            .addFilterAt(new AuthPostCheckFilter(authTokenService), SecurityWebFiltersOrder.CSRF)
            // oauth2认证过滤器
            .addFilterAt(oauth2Filter, SecurityWebFiltersOrder.AUTHENTICATION)
            .addFilterAt(new OnlineStatFilter(onlineStatService), SecurityWebFiltersOrder.SECURITY_CONTEXT_SERVER_WEB_EXCHANGE)
            // 日志过滤器
            .addFilterAt(new AccessLogFilter(accessLogService), SecurityWebFiltersOrder.SECURITY_CONTEXT_SERVER_WEB_EXCHANGE);
        return http.build();
    }

}
