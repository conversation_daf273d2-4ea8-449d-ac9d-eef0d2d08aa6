package com.hxdi.file.util;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.Assert;
import com.hxdi.common.core.properties.FssProperties;
import com.hxdi.common.core.utils.codec.MD5Util;
import com.hxdi.file.util.dto.FileParams;
import com.hxdi.file.util.dto.FileWriteResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2023/2/27 17:51
 * @description 直接基于文件访问处理工具
 * @version 1.0
 */
@Slf4j
public class LocalFileUtil extends AbstractFileOperator{

    private FssProperties fssProperties;

    public LocalFileUtil(FssProperties fssProperties) {
        this.fssProperties = fssProperties;
    }

    @Override
    public Optional<FileWriteResult> storeObject(InputStream is, FileParams params) {
        Assert.notEmpty(params.getObjectName(), "文件名不能为空");
        isSupportUploadingFile(params.getObjectName());

        FileWriteResult result = new FileWriteResult();
        final String originalName = params.getObjectName();
        final String objectName = rename(originalName);
        final String contentType = parseContentType(originalName);
        try {
            // IO流只能一次性读取，通过byte[]缓存数据
            byte[] bytes = IOUtils.toByteArray(is);
            String absolutePath = fssProperties.getRoot() + objectName;
            File file = new File(absolutePath);
            file.setExecutable(false);
            file.setReadable(true);
            file.setWritable(true, true);
            FileUtil.writeBytes(bytes, file);

            String os = System.getProperty("os.name");
            if (os != null && os.toLowerCase().startsWith("win")) {
                // windows
            } else if (os != null && os.toLowerCase().startsWith("linux")) {
                // linux
            } else {
                // 其他操作系统
            }

            String etag = MD5Util.streamToMD5(new ByteArrayInputStream(bytes));
            result.setEtag(etag);
            result.setObjectName(objectName);
            result.setContentType(contentType);
            return Optional.of(result);
        } catch (Exception e) {
            log.error("文件上传失败, Object[{}], Error: {}", originalName, e.getMessage());
        }

        return Optional.empty();
    }

    @Override
    public Optional<InputStream> loadObject(FileParams params) {
        Assert.notEmpty(params.getObjectName(), "文件名不能为空");
        try {
            byte[] bytes = FileUtil.readBytes(fssProperties.getRoot() + params.getObjectName());
            return Optional.of(new ByteArrayInputStream(bytes));
        } catch (Exception e) {
            log.error("读取文件失败, Object[{}], Error: {}", params.getObjectName(), e.getMessage());
        }

        return Optional.empty();
    }

    @Override
    public Optional<String> getObjectUrl(FileParams params) {
        return Optional.empty();
    }

    @Override
    public void removeObject(FileParams params) {
        Assert.notEmpty(params.getObjectName(), "删除文件名不能为空");
        log.info("删除文件名: {}", params.getObjectName());
        File file = new File(fssProperties.getRoot() + params.getObjectName());
        del(file);
    }

    @Override
    public void removeObjects(FileParams params) {
        Assert.notNull(params.getObjects(), "删除文件列表不能为空");
        params.getObjects().forEach(objectName -> {
            del(new File(fssProperties.getRoot() + params.getObjectName()));
        });
    }

    private static boolean del(File file) {
        if (file != null && file.exists()) {
            if (file.isDirectory()) {
                return clean(file);
            } else {
                try {
                    Files.delete(file.toPath());
                    return true;
                } catch (IOException e) {
                    log.error("删除文件失败{}", file);
                    log.error(e.getMessage(), e);
                }
            }
        }

        return false;
    }

    private static boolean clean(File directory) {
        File[] files = directory.listFiles();
        File[] arr$ = files;
        int len$ = files.length;

        for(int i$ = 0; i$ < len$; ++i$) {
            File childFile = arr$[i$];
            del(childFile);
        }

        return true;
    }
}
