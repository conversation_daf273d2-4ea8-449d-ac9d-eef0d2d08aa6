package com.hxdi.msg.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hxdi.common.core.utils.CommonUtils;
import com.hxdi.msg.client.model.entity.MsgInfo;
import com.hxdi.msg.client.model.entity.MsgTask;
import com.hxdi.msg.enums.MsgEnum;
import com.hxdi.msg.mapper.MsgTaskMapper;
import com.hxdi.msg.service.MsgInfoService;
import com.hxdi.msg.service.MsgTaskService;
import com.hxdi.msg.service.MsgUsersService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

/**
 * @program: hxdicloud_2.0
 * @description: 消息定时发送服务实现
 * @author: flying
 * @create: 2025-06-30 15:31
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class MsgTaskServiceImpl extends ServiceImpl<MsgTaskMapper, MsgTask> implements MsgTaskService {

    @Resource
    private MsgInfoService msgInfoService;

    @Resource
    private MsgUsersService msgUsersService;

    @Override
    public void addTask(MsgInfo msg) {
        if(msg.getScheduledTime() == null || CommonUtils.isEmpty(msg.getReceiver())){
             return;
        }
        MsgTask task = new MsgTask();
        task.setMsgId(msg.getId());
        task.setReceiver(msg.getReceiver());
        task.setSendTime(msg.getScheduledTime());
        save(task);
    }

    @Override
    public void removeTask(MsgInfo msg) {
        remove(new LambdaQueryWrapper<MsgTask>().eq(MsgTask::getMsgId, msg.getId()));
    }

    @Override
    public void updateTask(MsgInfo msg) {
        removeTask(msg);
        addTask(msg);
    }

    @Override
    public void sendMsg(List<MsgInfo> msgList, List<MsgTask> taskList) {
        if(CommonUtils.isEmpty(msgList) && CommonUtils.isEmpty(taskList)){
            return;
        }
        if(msgList!=null && !msgList.isEmpty()){
            msgList.forEach(msg->{
                List<String> receiver;
                if(msg.getRcvType()==1){
                    receiver = Collections.singletonList(MsgEnum.MSG_USER_ALL.getCode());
                }else {
                    receiver = Arrays.asList(msg.getReceiver().split(","));
                }
                //发送消息
                msgUsersService.saveMsgUsers(msg.getId(), receiver);
                //更新消息发送状态
                msgInfoService.updateStateAndSendTime(msg.getId(), 1);
            });
        } else if(taskList!=null && !taskList.isEmpty()){
            taskList.forEach(task -> {
                //发送消息
                msgUsersService.saveMsgUsers(task.getMsgId(), Arrays.asList(task.getReceiver().split(",")));
                //更新消息发送状态及发送时间
                msgInfoService.updateStateAndSendTime(task.getMsgId(), 1);
                //删除任务
                removeById(task.getId());
            });
        }
        log.info("消息发送成功");
    }



    /**
     * 定时查询待发送的消息，每隔30秒执行一次，第一次启动后 5分钟再执行
     */
    @Scheduled(fixedRate = 30000, initialDelay = 300000)
    public void task(){
        log.info("开始执行定时发送消息任务");
        List<MsgTask> taskList = list(new QueryWrapper<MsgTask>().lt("SEND_TIME", new Date()));
        if(taskList.isEmpty()){
            return;
        }
        log.info("待发送消息任务数量:{}",taskList.size());
        sendMsg(null, taskList);
    }

}
