package com.hxdi.system.client.model.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hxdi.common.core.model.DataType;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/11/4 22:34
 * @description 系统设置
 * @version 1.0
 */
@Getter
@Setter
@TableName("system_setting")
public class SystemSetting implements Serializable {
    private static final long serialVersionUID = -5456376103682651496L;

    @TableId(type = IdType.AUTO)
    private Integer id;

    private String functionalName;

    private String propertyName;

    private String propertyVal;

    private String defaultVal;

    private String propertyType;

    /**
     * 解析属性值
     * @param <V>
     * @return
     */
    public <V> V parseValue() {
        if (this.propertyType.equalsIgnoreCase(DataType.NUMBER.value())) {
            return (V) Integer.valueOf(this.propertyVal);
        }

        return (V) this.propertyVal;
    }
}
