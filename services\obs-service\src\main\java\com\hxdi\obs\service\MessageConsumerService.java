package com.hxdi.obs.service;

import com.hxdi.common.core.constants.CommonConstants;
import com.hxdi.common.core.model.MessageDTO;
import com.hxdi.common.core.model.MessageType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/30 16:21
 * @description 消息订阅
 */
@Component
@Slf4j
public class MessageConsumerService {

    private FileResourceService fileResourceService;

    private Map<String, Consumer<MessageDTO>> consumerStrategy = new HashMap<>(32);

    @Autowired
    public MessageConsumerService(FileResourceService fileResourceService) {
        this.fileResourceService = fileResourceService;

        // 注册服务消费者
        consumerStrategy.put(MessageType.VIDEO_REC_UPLOAD_INFO.value(), fileResourceService::saveVideoRecUploadInfo);
    }

    /**
     * 订阅录制告警视频并上传的消息
     * @param dto
     */
    @RabbitListener(queuesToDeclare = @Queue(value = CommonConstants.QUEUE_OBS_MESSAGE_BUS, durable = "true", exclusive = "false", autoDelete = "false"))
    public void handleMessage(@Payload MessageDTO dto) {
        log.info("订阅【{}】消息开始处理...", dto.getTopic());

        // 具体服务消费消息
        consumerStrategy.get(dto.getTopic()).accept(dto);

        log.info("订阅【{}】消息处理成功!!!", dto.getTopic());
    }
}
