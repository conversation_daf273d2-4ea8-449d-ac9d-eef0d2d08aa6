package com.hxdi.file.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.hxdi.common.core.mybatis.base.mapper.SuperMapper;
import com.hxdi.file.client.model.entity.Scott;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/22 16:33
 * @description
 * @version 1.0
 */
@Repository
@Mapper
public interface ScottMapper extends SuperMapper<Scott> {


    @InterceptorIgnore(tenantLine = "true")
    @Select("select * from scott where tenant_id = #{tenantId}")
    List<Scott> selectListByTenant(String tenantId);
}
