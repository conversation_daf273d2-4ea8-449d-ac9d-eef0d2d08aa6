package com.hxdi.system.client.model.dto;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * 经纬度坐标
 */
@Getter
@Setter
public class Coordinate {

    /**
     * 经度
     */
    private BigDecimal lng;

    /**
     * 纬度
     */
    private BigDecimal lat;

    public Coordinate(){}

    public Coordinate(BigDecimal lng, BigDecimal lat){
        this.lng = lng;
        this.lat = lat;
    }
}
