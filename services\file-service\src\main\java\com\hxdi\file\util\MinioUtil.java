package com.hxdi.file.util;

import com.hxdi.file.util.dto.FileParams;
import com.hxdi.file.util.dto.FileWriteResult;
import io.minio.*;
import io.minio.http.Method;
import io.minio.messages.DeleteError;
import io.minio.messages.DeleteObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/2/24 10:55
 * @description 文件对象操作工具类
 * @version 1.0
 */
@Slf4j
public class MinioUtil extends AbstractFileOperator{

    private static final Pattern IMAGE_PATTERN = Pattern.compile("(gif|jpg|jpeg|png)$", Pattern.CASE_INSENSITIVE);

    private MinioClient minioClient;

    private String bucketName;

    public MinioUtil(MinioClient minioClient, String bucketName) {
        this.minioClient = minioClient;
        this.bucketName = bucketName;
    }

    /**
     * 查询对象信息
     * @param params
     * @return
     */
    public Optional<StatObjectResponse> statObject(FileParams params) {
        try {
            StatObjectResponse response = minioClient.statObject(StatObjectArgs.builder().bucket(bucketName).object(params.getObjectName()).build());
            return Optional.of(response);
        } catch (Exception e) {
            log.error("查询对象失败, Bucket[{}], Object[{}], Error: {}", bucketName, params.getObjectName(), e.getMessage());
        }

        return Optional.empty();
    }

    /**
     * 检查Bucket是否存在
     * @param bucketName
     * @return
     */
    public boolean bucketIsExists(String bucketName) {
        try {
            return minioClient.bucketExists(BucketExistsArgs.builder().bucket(bucketName).build());
        } catch (Exception e) {
            log.error("查询Bucket[{}]失败, Error: {}", bucketName, e.getMessage());
            return false;
        }
    }

    /**
     * 创建Bucket
     * @param bucketName
     */
    public void createBucket(String bucketName) {
        try {
            minioClient.makeBucket(MakeBucketArgs.builder().bucket(bucketName).build());
        } catch (Exception e) {
            log.error("创建Bucket[{}]失败, Error: {}", bucketName, e.getMessage());
        }
    }



    /**
     * 存储对象-输入流
     * @param is    对象
     * @param params
     * @return
     */
    @Override
    public Optional<FileWriteResult> storeObject(InputStream is, FileParams params) {
        isSupportUploadingFile(params.getObjectName());

        FileWriteResult result = new FileWriteResult();
        final String originalName = params.getObjectName();
        final String objectName = rename(originalName);
        final String contentType = parseContentType(originalName);
        try {
            ObjectWriteResponse response = minioClient.putObject(PutObjectArgs.builder()
                    .bucket(bucketName)
                    .object(objectName)
                    .stream(is, -1, PutObjectArgs.MIN_MULTIPART_SIZE)
                    .contentType(contentType)
                    .build());

            result.setEtag(response.etag());
            result.setObjectName(objectName);
            result.setContentType(contentType);
            return Optional.of(result);
        } catch (Exception e) {
            log.error("[InputStream]对象存储失败, Bucket[{}], Object[{}], Error: {}", bucketName, originalName, e.getMessage());
        }

        return Optional.empty();
    }

    /**
     * 存储对象-字节数组
     * @param bytes    对象
     * @param params
     */
    public Optional<FileWriteResult> storeObject(byte[] bytes, FileParams params) {
        isSupportUploadingFile(params.getObjectName());

        FileWriteResult result = new FileWriteResult();
        final String originalName = params.getObjectName();
        final String objectName = rename(originalName);
        final String contentType = parseContentType(originalName);
        try {
            ByteArrayInputStream bis = new ByteArrayInputStream(bytes);
            ObjectWriteResponse response = minioClient.putObject(PutObjectArgs.builder()
                    .bucket(bucketName)
                    .object(objectName)
                    .stream(bis, bis.available(), -1L)
                    .contentType(contentType)
                    .build());

            result.setEtag(response.etag());
            result.setObjectName(objectName);
            result.setContentType(contentType);
            return Optional.of(result);
        } catch (Exception e) {
            log.error("[byte[]]对象存储失败, Bucket[{}], Object[{}], Error: {}", bucketName, originalName, e.getMessage());
        }

        return Optional.empty();
    }

    /**
     * 存储对象-file
     * @param file   对象
     * @param params
     */
    public Optional<FileWriteResult> storeObject(MultipartFile file, FileParams params) {
        isSupportUploadingFile(params.getObjectName());

        FileWriteResult result = new FileWriteResult();
        final String originalName = params.getObjectName();
        final String objectName = rename(originalName);
        final String contentType = parseContentType(originalName);
        try {
            ObjectWriteResponse response = minioClient.putObject(PutObjectArgs.builder()
                    .bucket(bucketName)
                    .object(objectName)
                    .stream(file.getInputStream(), file.getSize(), -1L)
                    .contentType(contentType)
                    .build());

            result.setObjectName(objectName);
            result.setEtag(response.etag());
            result.setContentType(contentType);
            return Optional.of(result);
        } catch (Exception e) {
            log.error("[MultipartFile]对象存储失败, Bucket[{}], Object[{}], Error: {}", bucketName, originalName, e.getMessage());
        }

        return Optional.empty();
    }

    /**
     * 上传本地文件
     * @param filename 本地文件名--全路径名称
     * @param params
     */
    public Optional<FileWriteResult> storeObject(String filename, FileParams params) {
        isSupportUploadingFile(params.getObjectName());

        FileWriteResult result = new FileWriteResult();
        final String originalName = params.getObjectName();
        final String objectName = rename(originalName);
        final String contentType = parseContentType(originalName);
        try {
            ObjectWriteResponse response = minioClient.uploadObject(UploadObjectArgs.builder()
                    .bucket(bucketName)
                    .object(objectName)
                    .filename(filename)
                    .contentType(contentType)
                    .build());

            result.setObjectName(objectName);
            result.setEtag(response.etag());
            result.setContentType(contentType);
            return Optional.of(result);
        } catch (Exception e) {
            log.error("上传本地文件对象失败, Bucket[{}], Object[{}], Error: {}", bucketName, originalName, e.getMessage());
        }

        return Optional.empty();
    }

    /**
     * 获取对象数据
     * @param params
     * @return
     */
    @Override
    public Optional<InputStream> loadObject(FileParams params) {
        try (InputStream is = minioClient.getObject(
                GetObjectArgs.builder()
                        .bucket(bucketName)
                        .object(params.getObjectName())
                        .build())) {

            // 保证minio流使用后，释放资源
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            IOUtils.copy(is, bos);
            return Optional.of(new ByteArrayInputStream(bos.toByteArray()));
        } catch (Exception e) {
            log.error("获取对象数据失败, Bucket[{}], Object[{}], Error: {}", bucketName, params.getObjectName(), e.getMessage());
        }

        return Optional.empty();
    }

    /**
     * 下载文件数据到本地
     * @param filename  本地文件名--全路径
     * @param params
     */
    public void loadObject(String filename, FileParams params) {
        try {
            minioClient.downloadObject(DownloadObjectArgs.builder()
                    .bucket(bucketName)
                    .object(params.getObjectName())
                    .filename(filename)
                    .build());
        } catch (Exception e) {
            log.error("下载对象-本地失败, Bucket[{}], Object[{}], Error: {}", bucketName, params.getObjectName(), e.getMessage());
        }
    }

    /**
     * 获取文件对象访问链接，一天内有效
     * @param params
     * @return
     */
    @Override
    public Optional<String> getObjectUrl (FileParams params) {
        Map<String, String> reqParams = new HashMap<>(16);
        reqParams.put("response-content-type", parseContentType(params.getObjectName()));
        try {
            String url = minioClient.getPresignedObjectUrl(GetPresignedObjectUrlArgs.builder()
                        .method(Method.GET)
                        .bucket(bucketName)
                        .object(params.getObjectName())
                        .extraQueryParams(reqParams)
                        .expiry(1, TimeUnit.DAYS)
                        .build());
            return Optional.of(url);
        } catch (Exception e) {
            log.error("获取对象URL失败, Bucket[{}], Object[{}], Error: {}", bucketName, params.getObjectName(), e.getMessage());
        }

        return Optional.empty();
    }

    /**
     * 删除对象
     * @param params
     */
    @Override
    public void removeObject(FileParams params) {
        try {
            minioClient.removeObject(RemoveObjectArgs.builder()
                    .bucket(bucketName)
                    .object(params.getObjectName())
                    .build());
        } catch (Exception e) {
            log.error("删除对象失败, Bucket[{}], Object[{}], Error: {}", bucketName, params.getObjectName(), e.getMessage());
        }
    }

    /**
     * 批量删除对象
     * @param params
     */
    @Override
    public void removeObjects(FileParams params) {
        List<DeleteObject> delObjs = params.getObjects().stream().map(s -> new DeleteObject(s)).collect(Collectors.toList());
        try {
            Iterable<Result<DeleteError>> results = minioClient.removeObjects(RemoveObjectsArgs.builder()
                    .bucket(bucketName)
                    .objects(delObjs)
                    .build());

            for (Result<DeleteError> result : results) {
                DeleteError error = result.get();
                log.info("Error in deleting object {}, error {} ", error.objectName(), error.message());
            }
        } catch (Exception e) {
            log.error("批量删除对象失败, Bucket[{}], Object[{}], Error: {}", bucketName, params.getObjects(), e.getMessage());
        }
    }
}
