package com.hxdi.common.core.utils;

import com.hx.arch.core.model.Response;
import com.hx.arch.data.area.AreaInfo;
import com.hx.arch.data.area.HxProvinceAreaService;
import com.hxdi.common.core.constants.ErrorCode;
import com.hxdi.common.core.model.Coordinate;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2022/9/2 14:08
 * @description 地区解析辅助工具类
 * @version 1.0
 */
@Slf4j
public class AreaHelperUtil {

    private HxProvinceAreaService service;

    private static final String ROOT = "中国";

    /**
     * 单实例
     */
    private static volatile AreaHelperUtil INSTANCE;

    private AreaHelperUtil(HxProvinceAreaService service) {
        this.service = service;
    }

    /**
     * 通过双检锁实现单例模式
     * @param hxProvinceAreaService
     * @return
     */
    public static AreaHelperUtil build(HxProvinceAreaService hxProvinceAreaService) {
        CommonUtils.assertNotNull(hxProvinceAreaService, "地区服务接口不能为NULL");
        if (INSTANCE == null) {
            synchronized (AreaHelperUtil.class) {
                if (INSTANCE == null) {
                    INSTANCE = new AreaHelperUtil(hxProvinceAreaService);
                }
            }
        }

        return INSTANCE;
    }


    /**
     * 根据地址全称转换为地区编码映射列表
     * @param mergeName
     * @return
     */
    public static String parseAreaCodes(String mergeName) {
        if (CommonUtils.isEmpty(mergeName)) return null;

        if (mergeName.startsWith(ROOT)) {
            mergeName = mergeName.substring(3);// 中国,浙江省,xxx
        }
        String[] areaSpecs = mergeName.split("[,|/]");
        Strcat.StrcatBuilder areaCodes = Strcat.joinWithDelimiter(",");
        Strcat.StrcatBuilder name = Strcat.joinWithDelimiter(",", ROOT);
        for (int i = 0; i < areaSpecs.length; i++) {
            name.join(areaSpecs[i]);
            Response<AreaInfo> resp = INSTANCE.service.getByMergeName(name.toString());
            if (resp.getCode().intValue() == ErrorCode.SUCCESS.getCode() && resp.getData() != null) {
                areaCodes.join(resp.getData().getCode().toString());
            } else {
                areaCodes.join("0");
            }
        }

        return areaCodes.toString();
    }

    /**
     * 根据地址全称转换为地区编码
     * @param mergeName
     * @return 0 标识地址错误，解析失败
     */
    public static Long parseCode(String mergeName) {
        if (CommonUtils.isEmpty(mergeName)) return 0L;

        if (mergeName.startsWith(ROOT)) {
            mergeName = mergeName.substring(3);// 中国,浙江省,xxx
        }

        String[] areaSpecs = mergeName.split("[,|/]");

        Strcat.StrcatBuilder name = Strcat.joinWithDelimiter(",", ROOT).join(areaSpecs);
        Response<AreaInfo> resp = INSTANCE.service.getByMergeName(name.toString());
        if (resp.getCode().intValue() == ErrorCode.SUCCESS.getCode() && resp.getData() != null) {
            return resp.getData().getCode();
        }

        return 0L;
    }

    /**
     * 根据地区编码转换成经纬度坐标
     * @param code  地区唯一编码
     * @return
     */
    public static Coordinate convertCoordinate(Long code) {
        if (code != null) {
            Response<AreaInfo> resp = INSTANCE.service.getByCode(code);
            if (resp.getCode().intValue() == ErrorCode.SUCCESS.getCode() && resp.getData() != null) {
                AreaInfo areaInfo = resp.getData();
                return new Coordinate(areaInfo.getLon(), areaInfo.getLat());
            }
        }

        log.error("[{}]地区码转换经纬度失败", code);
        return Coordinate.NON;
    }

    /**
     * 根据地址全称转换成经纬度坐标
     * @param mergeName
     * @return
     */
    public static Coordinate convertCoordinate(String mergeName) {
        if (mergeName != null) {
            Long code = parseCode(mergeName);
            return convertCoordinate(code);
        }

        log.error("[{}]转换经纬度失败", mergeName);
        return Coordinate.NON;
    }
}
