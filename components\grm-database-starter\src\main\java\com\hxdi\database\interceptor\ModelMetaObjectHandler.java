package com.hxdi.database.interceptor;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.hxdi.common.core.security.SecurityHelper;
import org.apache.ibatis.reflection.MetaObject;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/2/6 13:45
 * @description mbp自动填充字段
 * @version 1.0
 */
public class ModelMetaObjectHandler implements MetaObjectHandler {

    private static final String CREATE_TIME = "createTime";
    private static final String CREATE_ID = "createId";
    private static final String UPDATE_TIME = "updateTime";
    private static final String UPDATE_ID = "updateId";
    private static final String DATA_HIERARCHY_ID = "dataHierarchyId";
    /**
     * metaObject是页面传递过来的参数的包装对象，不是从数据库取的持久化对象，因此页面传过来哪些值，metaObject里就有哪些值。
     */
    @Override
    public void insertFill(MetaObject metaObject) {
        if (metaObject.hasGetter(CREATE_TIME) && metaObject.hasSetter(CREATE_TIME)) {
            this.fillStrategy(metaObject, CREATE_TIME, new Date());
        }

        if (metaObject.hasGetter(UPDATE_TIME) && metaObject.hasSetter(UPDATE_TIME)) {
            metaObject.setValue(UPDATE_TIME, new Date());
        }

        if (metaObject.hasGetter(CREATE_ID) && metaObject.hasSetter(CREATE_ID)) {
            this.fillStrategy(metaObject, CREATE_ID, SecurityHelper.obtainUserId().orElse(null));
        }

        if (metaObject.hasGetter(UPDATE_ID) && metaObject.hasSetter(UPDATE_ID)) {
            metaObject.setValue(UPDATE_ID, SecurityHelper.obtainUserId().orElse(null));
        }

        if (metaObject.hasGetter(DATA_HIERARCHY_ID) && metaObject.hasSetter(DATA_HIERARCHY_ID)) {
            this.fillStrategy(metaObject, DATA_HIERARCHY_ID, SecurityHelper.obtainOrganId().orElse(null));
        }
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        if (metaObject.hasGetter(UPDATE_TIME) && metaObject.hasSetter(UPDATE_TIME)) {
            metaObject.setValue(UPDATE_TIME, new Date());
        }

        if (metaObject.hasGetter(UPDATE_ID) && metaObject.hasSetter(UPDATE_ID)) {
            metaObject.setValue(UPDATE_ID, SecurityHelper.obtainUserId().orElse(null));
        }
    }
}
